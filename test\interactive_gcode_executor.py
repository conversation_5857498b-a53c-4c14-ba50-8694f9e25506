#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 交互式执行程序

功能:
1.  加载G-code文件，并将其解析为指令列表。
2.  逐条显示G-code指令，等待用户按回车键确认。
3.  每按一次回车，发送一条移动指令给机械臂并等待其完成。
4.  允许用户清晰地观察每个G-code指令对应的物理移动。
5.  通过Ctrl+C可以随时安全地中断程序。
"""

import sys
import os
import time
import re
import math

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# --- 全局参数配置 ---
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1

# 速度配置: G0用于快速移动，G1用于工作进给
G0_VELOCITY_PERCENT = 80
G1_VELOCITY_PERCENT = 40
ACCEL_PERCENT = 40

# 平滑度配置 (0-8)
SMOOTHING_LEVEL = 3

# G-code默认角度值
GCODE_DEFAULT_A = 0.0
GCODE_DEFAULT_B = 0.0
GCODE_DEFAULT_C = 0.0

# G-code角度到机械臂角度的偏移量
GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = 0.0


def normalize_angle_degrees(angle):
    """将角度标准化到 [-180, 180] 范围内"""
    while angle > 180:
        angle -= 360
    while angle <= -180:
        angle += 360
    return angle

class RobotController:
    """封装了机器人连接、控制和状态查询的类"""
    def __init__(self, ip, port):
        self.ip = ip
        self.port = port
        self.socket_fd = -1
        self.is_connected = False

    def connect(self):
        print(f"🔗 正在连接机械臂 {self.ip}:{self.port}...")
        self.socket_fd = nrc.connect_robot(self.ip, str(self.port))
        if self.socket_fd <= 0:
            print("❌ 连接失败！")
            self.is_connected = False
            return False
        print(f"✅ 连接成功！Socket ID: {self.socket_fd}")
        self.is_connected = True
        return True

    def disconnect(self):
        if not self.is_connected:
            return
        print("🔌 正在断开连接...")
        nrc.disconnect_robot(self.socket_fd)
        self.is_connected = False
        self.socket_fd = -1
        print("✅ 连接已断开。")

    def initialize_robot(self):
        """按照正确的状态机顺序初始化机器人，并为队列模式做准备"""
        if not self.is_connected:
            return False
        
        if not self._power_on_if_needed():
            return False
        
        print("ℹ️ 切换到运行模式以准备执行...")
        result = nrc.set_current_mode(self.socket_fd, 2) # 0=示教, 1=远程, 2=运行
        if result != 0:
            print(f"❌ 切换到运行模式失败，错误码: {result}")
            return False
        time.sleep(0.2)

        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(self.socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return False
        time.sleep(0.1)

        print("ℹ️ 清空并启用队列模式...")
        nrc.queue_motion_clear_Data(self.socket_fd)
        time.sleep(0.1)
        result = nrc.queue_motion_set_status(self.socket_fd, True)
        if result != 0:
            print(f"❌ 启用队列模式失败，错误码: {result}")
            return False
        time.sleep(0.1)

        current_mode = self.get_current_mode()
        mode_names = {0: "示教", 1: "远程", 2: "运行"}
        print(f"✅ 机器人初始化完成。当前模式: {mode_names.get(current_mode, '未知')} ({current_mode})")
        
        return True

    def _power_on_if_needed(self):
        """先切换到示教模式，再执行上电"""
        try:
            servo_status_ref = 0
            result = nrc.get_servo_state(self.socket_fd, servo_status_ref)
            if isinstance(result, list) and result[1] == 3:
                print("✅ 机器人伺服已上电。")
                return True

            print("ℹ️ 机器人需要上电，开始上电流程...")
            
            print("   -> 切换到示教模式以上电...")
            nrc.set_current_mode(self.socket_fd, 0) # 0 = 示教模式
            time.sleep(0.2)
            
            nrc.clear_error(self.socket_fd)
            time.sleep(0.1)
            nrc.set_servo_state(self.socket_fd, 1) # 1 = 就绪
            time.sleep(1.5)
            
            print("   -> 执行上电指令...")
            result = nrc.set_servo_poweron(self.socket_fd)
            if result != 0:
                print(f"❌ 上电失败！返回码: {result}。")
                return False
            time.sleep(1.5)
            
            result = nrc.get_servo_state(self.socket_fd, servo_status_ref)
            final_status = result[1] if isinstance(result, list) else -1
            if final_status == 3:
                print("✅ 机器人上电成功！")
                return True
            else:
                print(f"❌ 上电后状态异常: {final_status}")
                return False
        except Exception as e:
            print(f"❌ 上电过程失败: {e}")
            return False

    def power_off(self):
        if not self.is_connected:
            return
        print("\nℹ️ 正在安全下电...")
        nrc.queue_motion_stop(self.socket_fd)
        time.sleep(0.5)
        nrc.set_servo_poweroff(self.socket_fd)
        time.sleep(1)
        print("✅ 机器人已下电。")

    def get_current_mode(self):
        if not self.is_connected:
            return -1
        mode_ref = 0
        result = nrc.get_current_mode(self.socket_fd, mode_ref)
        if isinstance(result, list) and result[0] == 0:
            return result[1]
        return -1
        
    def get_queue_length(self):
        if not self.is_connected:
            return -1
        len_ref = 0
        result = nrc.queue_motion_get_queuelen(self.socket_fd, len_ref)
        if isinstance(result, list) and result[0] == 0:
            return result[1]
        return -1
    
    def set_speed(self, speed_percent):
        """设置机器人的全局运行速度"""
        print(f"⚡️ 切换全局速度为: {speed_percent}%")
        result = nrc.set_speed(self.socket_fd, speed_percent)
        if result != 0:
            print(f"❌ 设置速度失败，错误码: {result}")
            return False
        time.sleep(0.1) # 等待速度设置生效
        return True

    def add_point_to_queue(self, point):
        """将点添加到队列"""
        gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
        gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
        gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

        rx_deg = normalize_angle_degrees(gcode_a + GCODE_TO_ROBOT_OFFSET_A)
        ry_deg = normalize_angle_degrees(gcode_b + GCODE_TO_ROBOT_OFFSET_B)
        rz_deg = normalize_angle_degrees(gcode_c + GCODE_TO_ROBOT_OFFSET_C)

        rx_rad = math.radians(rx_deg)
        ry_rad = math.radians(ry_deg)
        rz_rad = math.radians(rz_deg)

        target_pos = [point['x'], point['y'], point['z'], rx_rad, ry_rad, rz_rad]

        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0
        move_cmd.targetPosValue = nrc.VectorDouble()
        for val in target_pos:
            move_cmd.targetPosValue.append(val)
        
        move_cmd.coord = 3
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = 100
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL

        result = nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 添加点到队列失败，错误码: {result}")
            return False
        return True

    def sync_queue(self, size):
        if not self.is_connected or size == 0:
            return False
        result = nrc.queue_motion_send_to_controller(self.socket_fd, size)
        if result != 0:
            print(f"❌ 同步队列到控制器失败，错误码: {result}")
            return False
        return True


def parse_gcode_file(filepath):
    """解析G-code文件并返回一个包含指令的列表。"""
    print(f"🔩 解析G-code文件: {filepath}...")
    commands = []
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
            for line_num, line in enumerate(f, 1):
                line = line.strip().upper()
                if not line or line.startswith(';'):
                    continue
                
                command_type = None
                if line.startswith('G0'):
                    command_type = 'G0'
                elif line.startswith('G1'):
                    command_type = 'G1'

                if command_type:
                    coords = dict(coord_regex.findall(line))
                    if 'X' in coords and 'Y' in coords and 'Z' in coords:
                        point_coords = {
                            'x': float(coords.get('X', 0.0)),
                            'y': float(coords.get('Y', 0.0)),
                            'z': float(coords.get('Z', 0.0)),
                            'a': float(coords.get('A')) if 'A' in coords else None,
                            'b': float(coords.get('B')) if 'B' in coords else None,
                            'c': float(coords.get('C')) if 'C' in coords else None
                        }
                        commands.append({
                            'line_num': line_num,
                            'raw_line': line,
                            'type': command_type, 
                            'coords': point_coords
                        })
    except FileNotFoundError:
        print(f"❌ 错误：G-code文件未找到: {filepath}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None
    
    print(f"✅ 文件解析完成，共找到 {len(commands)} 条有效移动指令。")
    return commands


def main():
    print("=" * 60)
    print("INEXBOT机械臂 G-code 交互式执行程序")
    print("=" * 60)

    robot = RobotController(ROBOT_IP, ROBOT_PORT)
    
    gcode_commands = parse_gcode_file(GCODE_FILE)
    if not gcode_commands:
        return

    try:
        if not robot.connect() or not robot.initialize_robot():
            return

        print("\n" + "="*20 + " 开始交互式执行G-code " + "="*20)
        print("👉 按【回车】键执行下一条指令，按【Ctrl+C】退出。")

        current_speed_type = None

        for i, command in enumerate(gcode_commands):
            print("-" * 60)
            print(f"准备执行指令 {i + 1}/{len(gcode_commands)} (原文件第 {command['line_num']} 行):")
            print(f"  G-CODE: {command['raw_line']}")

            input("  >>> 请按回车键以发送此指令...")

            # 如果指令类型（G0/G1）发生变化，则更新速度
            if command['type'] != current_speed_type:
                new_speed = G0_VELOCITY_PERCENT if command['type'] == 'G0' else G1_VELOCITY_PERCENT
                if not robot.set_speed(new_speed):
                    print("❌ 设置速度失败，程序终止。")
                    break
                current_speed_type = command['type']
            
            print(f"  🚀 正在发送指令...")
            if not robot.add_point_to_queue(command['coords']):
                print("❌ 添加点到队列失败，程序终止。")
                break
            if not robot.sync_queue(1):
                print("❌ 同步指令到控制器失败，程序终止。")
                break

            # 等待指令执行完成
            print("  ⏳ 等待指令执行完成...")
            start_time = time.time()
            while True:
                q_len = robot.get_queue_length()
                if q_len == 0:
                    break
                if q_len == -1:
                    raise ConnectionError("在等待指令完成时与机器人失去连接")
                if time.time() - start_time > 60: # 每条指令的超时设为60秒
                    raise TimeoutError("等待指令完成超时！")
                time.sleep(0.1)
            print("  ✅ 指令执行完毕！")
            
        print("\n🎉 所有指令执行完毕！")

    except KeyboardInterrupt:
        print("\n🚫 检测到手动中断 (Ctrl+C)...")
    except Exception as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        print("\nℹ️ 开始清理和关闭程序...")
        if robot.is_connected:
            robot.power_off()
            robot.disconnect()
        print("✅ 程序已安全退出。")


if __name__ == "__main__":
    main()
