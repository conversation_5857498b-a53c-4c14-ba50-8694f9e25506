#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gcode_pose_smoother_20250811.py
--------------------------------
目标：让机械臂按G-code平稳运动，并把G-code中的A/B/C姿态映射为机器人RX/RY/RZ，
同时在以下场景对姿态做“限幅式插补”，避免突发旋转：
  1) G0(快速) → G1(线性) 的切换；
  2) G1段与段之间的姿态台阶；
  3) 单条G1内同时发生较大位姿变化。

KISS / YAGNI：仅做“需要的”姿态平滑；不引入复杂依赖；以阈值+线性角插补实现。
SOLID：主要逻辑拆到小函数；RobotController只负责连接/入队；生产者只负责解析与插补。

使用要点：
- G0 的姿态一律按 A=B=C=0 处理（最终映射为 RX=180°, RY=0°, RZ=0°，末端朝下）。
- G1 的姿态来自 G-code 的 A/B/C，并与 (180,0,0) 做差补映射。
- 角度内部统一使用“度”，发送给控制器前再转“弧度”。
- 遇到姿态突变时，按 ANGLE_STEP_MAX_DEG 限幅插补若干中间点（在同一位置旋转）。

依赖：项目 lib/ 下的 NRC Python SDK（nrc_interface.py / nrc_host.pyd）和 config.py。
"""

import os
import re
import time
import math
import copy
import threading
from queue import Queue, Empty

# ------------------------- 基本配置（按需修改） -------------------------

# G-code 文件路径（也可用命令行参数覆盖）
GCODE_FILE = "jiyi.Gcode"

# 机器人连接配置
try:
    from config import ROBOT_IP, ROBOT_PORT
except Exception:
    # 回退占位，避免导入失败
    ROBOT_IP, ROBOT_PORT = "************", "6001"

# 用户坐标编号（与现场标定一致）
USER_COORD_NUMBER = 1

# 速度：G0 快速、G1 工作进给；通过 set_speed() 切换全局速度
G0_VELOCITY_PERCENT = 35
G1_VELOCITY_PERCENT = 50
ACCEL_PERCENT = 20

# 轨迹平滑等级（控制器端的平滑，0~8，越大越“圆滑”，可能带来微小轨迹偏差）
SMOOTHING_LEVEL = 3

# 控制器队列相关
CONTROLLER_QUEUE_CAPACITY = 200
BATCH_SEND_SIZE = 20

# ------------------------ 姿态映射与插补参数 --------------------------

# G-code 的 A/B/C 与机器人 RX/RY/RZ 的“静态差补”（单位：度）
# 约束：当 A=B=C=0 时，应有 RX=180°, RY=0°, RZ=0°。
GCODE_TO_ROBOT_OFFSET = (180.0, 0.0, 0.0)  # (A→RX, B→RY, C→RZ)

# 角度插补：单次最大变化（度）。例如 3° 意味着每个中间点的姿态差不超过 3°。
ANGLE_STEP_MAX_DEG = 1.0

# 判断是否“需要插补”的阈值（度）。小于该阈值时不插补，直接连。
ORIENT_JUMP_THRESHOLD_DEG = 3.0

# ---------------------------------------------------------------------

# 添加 lib 到路径并导入 NRC SDK
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc


# ----------------------------- 工具函数 -------------------------------

def normalize_angle_deg(a: float) -> float:
    """把角度标准化到 (-180, 180]。"""
    a = (a + 180.0) % 360.0 - 180.0
    # 保证 +180 而不是 -180
    if a <= -180.0:
        a += 360.0
    return a


def shortest_diff_deg(dst: float, src: float) -> float:
    """从 src 转到 dst 的最短角度差（度），范围 (-180, 180]。"""
    return normalize_angle_deg(dst - src)


def add_tuple(a, b):
    return tuple(ai + bi for ai, bi in zip(a, b))


def lerp_orientation_deg(src_abc: tuple, dst_abc: tuple, step_max_deg: float) -> list:
    """
    在姿态空间做线性插补（逐分量限幅 + 最短角差）。
    返回“中间姿态点”列表（不含 src，含 dst）。
    """
    da = shortest_diff_deg(dst_abc[0], src_abc[0])
    db = shortest_diff_deg(dst_abc[1], src_abc[1])
    dc = shortest_diff_deg(dst_abc[2], src_abc[2])

    max_jump = max(abs(da), abs(db), abs(dc))
    if max_jump <= step_max_deg:
        return [dst_abc]

    steps = int(math.ceil(max_jump / step_max_deg))
    result = []
    for i in range(1, steps + 1):
        t = i / steps
        ia = normalize_angle_deg(src_abc[0] + da * t)
        ib = normalize_angle_deg(src_abc[1] + db * t)
        ic = normalize_angle_deg(src_abc[2] + dc * t)
        result.append((ia, ib, ic))
    return result


def euler_deg_to_robot_radians(abc_deg: tuple, offset_deg: tuple) -> tuple:
    """G-code A/B/C（度） → 机器人 RX/RY/RZ（弧度），带静态差补。"""
    ax = normalize_angle_deg(abc_deg[0] + offset_deg[0])
    ay = normalize_angle_deg(abc_deg[1] + offset_deg[1])
    az = normalize_angle_deg(abc_deg[2] + offset_deg[2])
    return math.radians(ax), math.radians(ay), math.radians(az)


# ---------------------------- 控制器封装 ------------------------------

class RobotController:
    def __init__(self, ip, port):
        self.ip = ip
        self.port = port
        self.socket_fd = -1
        self.is_connected = False

    def connect(self) -> bool:
        print(f"🔗 连接 {self.ip}:{self.port} ...")
        self.socket_fd = nrc.connect_robot(self.ip, str(self.port))
        self.is_connected = self.socket_fd > 0
        if self.is_connected:
            print(f"✅ 成功，socket={self.socket_fd}")
        else:
            print("❌ 连接失败")
        return self.is_connected

    def disconnect(self):
        if self.is_connected:
            print("🔌 断开连接 ...")
            nrc.disconnect_robot(self.socket_fd)
            self.is_connected = False
            self.socket_fd = -1
            print("✅ 已断开")

    def _power_on_if_needed(self) -> bool:
        try:
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            cur = res[1] if isinstance(res, list) else -1
            if cur == 3:
                print("✅ 伺服已上电")
                return True

            print("ℹ️ 上电流程 ...")
            nrc.set_current_mode(self.socket_fd, 0)  # TEACH
            time.sleep(0.2)
            nrc.clear_error(self.socket_fd)
            time.sleep(0.1)
            nrc.set_servo_state(self.socket_fd, 1)  # READY
            time.sleep(1.0)
            if nrc.set_servo_poweron(self.socket_fd) != 0:
                print("❌ 上电失败")
                return False
            time.sleep(1.2)
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            cur = res[1] if isinstance(res, list) else -1
            ok = cur == 3
            print("✅ 上电成功" if ok else f"❌ 上电状态异常:{cur}")
            return ok
        except Exception as e:
            print("❌ 上电异常：", e)
            return False

    def initialize(self) -> bool:
        if not self.is_connected:
            return False
        if not self._power_on_if_needed():
            return False
        # 进入运行模式、设置用户坐标、开启队列
        if nrc.set_current_mode(self.socket_fd, 2) != 0:
            print("❌ 切到运行模式失败")
            return False
        time.sleep(0.1)
        if nrc.set_user_coord_number(self.socket_fd, USER_COORD_NUMBER) != 0:
            print("❌ 设置用户坐标失败")
            return False
        time.sleep(0.05)
        nrc.queue_motion_clear_Data(self.socket_fd)
        time.sleep(0.05)
        if nrc.queue_motion_set_status(self.socket_fd, True) != 0:
            print("❌ 启用队列失败")
            return False
        print("✅ 初始化完毕（运行模式 / 队列开启）")
        return True

    def power_off(self):
        try:
            nrc.queue_motion_stop(self.socket_fd)
            time.sleep(0.2)
            nrc.set_servo_poweroff(self.socket_fd)
            print("✅ 已下电")
        except Exception:
            pass

    def get_queue_length(self) -> int:
        res = nrc.queue_motion_get_queuelen(self.socket_fd, 0)
        return res[1] if isinstance(res, list) and res[0] == 0 else -1

    def set_speed(self, percent: int) -> bool:
        print(f"⚡ 切换全局速度：{percent}%")
        return nrc.set_speed(self.socket_fd, percent) == 0

    def add_point(self, xyzabc_deg: tuple, move_type: str):
        """
        xyzabc_deg: (x,y,z,a,b,c) 角度单位=度
        move_type: 'G0' -> moveJ, 'G1' -> moveL
        """
        x, y, z, a, b, c = xyzabc_deg
        rx, ry, rz = euler_deg_to_robot_radians((a, b, c), GCODE_TO_ROBOT_OFFSET)

        cmd = nrc.MoveCmd()
        cmd.targetPosType = 0
        cmd.targetPosValue = nrc.VectorDouble([x, y, z, rx, ry, rz])
        cmd.coord = 3                 # 用户坐标
        cmd.userNum = USER_COORD_NUMBER
        cmd.velocity = 100            # 速度统一走全局 set_speed
        cmd.acc = ACCEL_PERCENT
        cmd.dec = ACCEL_PERCENT
        cmd.pl = SMOOTHING_LEVEL

        if move_type == 'G1':
            r = nrc.queue_motion_push_back_moveL(self.socket_fd, cmd)
        else:
            r = nrc.queue_motion_push_back_moveJ(self.socket_fd, cmd)

        if r != 0:
            raise RuntimeError(f"push_back失败 move_type={move_type}, err={r}")

    def flush(self, n: int) -> bool:
        if n <= 0:
            return True
        return nrc.queue_motion_send_to_controller(self.socket_fd, n) == 0


# ------------------------- G-code 解析与插补 ---------------------------

_COORD_RE = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')

def parse_line(line: str):
    line = line.strip()
    if not line or line.startswith(';'):
        return None
    up = line.upper()
    if not (up.startswith('G0') or up.startswith('G1')):
        return None
    cmd = 'G0' if up.startswith('G0') else 'G1'
    vals = dict((k, float(v)) for k, v in _COORD_RE.findall(up))
    return cmd, vals


def generate_orient_ramp_points(pos_xyz: tuple, src_abc: tuple, dst_abc: tuple, step_max_deg: float):
    """
    在同一个位置 pos_xyz 上，把姿态从 src_abc 旋到 dst_abc，返回多个 (x,y,z,a,b,c) 点（含最终点）。
    """
    intermediates = lerp_orientation_deg(src_abc, dst_abc, step_max_deg)
    result = []
    for a, b, c in intermediates:
        result.append((pos_xyz[0], pos_xyz[1], pos_xyz[2], a, b, c))
    return result


def gcode_producer(filepath: str, q: Queue, stop_event: threading.Event):
    """
    解析G-code，做模态继承 + 姿态限幅插补：
    - G0：强制 A=B=C=0；
    - G1：使用G-code提供的 A/B/C；
    - 当检测到 G0→G1 或 G1段间姿态台阶时，在“当前目标点位置”做原地旋转的插补点；
    - 单条G1若同时位置+姿态变化较大，也拆成【先到位(保持旧姿态)】→【原地旋转到新姿态】。
    """
    print(f"🏭 读取: {filepath}")
    last = {'x': 0.0, 'y': 0.0, 'z': 20.0, 'a': 0.0, 'b': 0.0, 'c': 0.0}
    last_cmd = 'G0'  # 初始默认为G0状态

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for raw in f:
                if stop_event.is_set():
                    break
                parsed = parse_line(raw)
                if not parsed:
                    continue
                cmd, vals = parsed

                # 模态继承
                cur = last.copy()
                for k, v in vals.items():
                    cur[k.lower()] = v

                # G0 的姿态强制为 0,0,0
                if cmd == 'G0':
                    cur['a'] = cur['b'] = cur['c'] = 0.0

                # 位置/姿态是否变化
                pos_changed = any(abs(cur[k] - last[k]) > 1e-6 for k in ('x', 'y', 'z'))
                abc_changed = any(abs(shortest_diff_deg(cur[k], last[k])) > 1e-6 for k in ('a', 'b', 'c'))

                # 1) G0 -> G1 且姿态差异明显：先到当前位置(旧姿态)，再做原地旋转插补到新姿态
                if last_cmd == 'G0' and cmd == 'G1':
                    jump = max(abs(shortest_diff_deg(cur['a'], last['a'])),
                               abs(shortest_diff_deg(cur['b'], last['b'])),
                               abs(shortest_diff_deg(cur['c'], last['c'])))
                    if jump > ORIENT_JUMP_THRESHOLD_DEG:
                        # 先移动到新位置但保持旧姿态（用G1，以保证直线）
                        q.put(('G1', (cur['x'], cur['y'], cur['z'], last['a'], last['b'], last['c'])))
                        # 在目标位置做原地旋转插补
                        ramp = generate_orient_ramp_points((cur['x'], cur['y'], cur['z']),
                                                           (last['a'], last['b'], last['c']),
                                                           (cur['a'], cur['b'], cur['c']),
                                                           ANGLE_STEP_MAX_DEG)
                        for p in ramp:
                            q.put(('G1', p))
                        last = cur
                        last_cmd = cmd
                        continue  # 下一行

                # 2) G1 → G1 段间姿态台阶：在“当前目标位置”做原地旋转插补
                if last_cmd == 'G1' and cmd == 'G1':
                    jump = max(abs(shortest_diff_deg(cur['a'], last['a'])),
                               abs(shortest_diff_deg(cur['b'], last['b'])),
                               abs(shortest_diff_deg(cur['c'], last['c'])))
                    if jump > ORIENT_JUMP_THRESHOLD_DEG:
                        # 先把位置跑到当前目标，但保持旧姿态
                        if pos_changed:
                            q.put(('G1', (cur['x'], cur['y'], cur['z'], last['a'], last['b'], last['c'])))
                        # 在目标位置原地旋转到新姿态
                        ramp = generate_orient_ramp_points((cur['x'], cur['y'], cur['z']),
                                                           (last['a'], last['b'], last['c']),
                                                           (cur['a'], cur['b'], cur['c']),
                                                           ANGLE_STEP_MAX_DEG)
                        for p in ramp:
                            q.put(('G1', p))
                        last = cur
                        last_cmd = cmd
                        continue

                # 3) 单条G1既变位又变姿态且幅度较大：拆成【到位(旧姿态)】+【原地旋转】
                if cmd == 'G1' and pos_changed and abc_changed:
                    jump = max(abs(shortest_diff_deg(cur['a'], last['a'])),
                               abs(shortest_diff_deg(cur['b'], last['b'])),
                               abs(shortest_diff_deg(cur['c'], last['c'])))
                    if jump > ORIENT_JUMP_THRESHOLD_DEG:
                        q.put((cmd, (cur['x'], cur['y'], cur['z'], last['a'], last['b'], last['c'])))
                        ramp = generate_orient_ramp_points((cur['x'], cur['y'], cur['z']),
                                                           (last['a'], last['b'], last['c']),
                                                           (cur['a'], cur['b'], cur['c']),
                                                           ANGLE_STEP_MAX_DEG)
                        for p in ramp:
                            q.put((cmd, p))
                        last = cur
                        last_cmd = cmd
                        continue

                # 4) 其它情况：直接入队（模态坐标已更新）
                q.put((cmd, (cur['x'], cur['y'], cur['z'], cur['a'], cur['b'], cur['c'])))

                last = cur
                last_cmd = cmd

    except FileNotFoundError:
        print(f"❌ 找不到 G-code 文件：{filepath}")
    finally:
        q.put(None)
        print("✅ 解析结束")


# ------------------------------- 主流程 --------------------------------

def main():
    # 允许命令行传入 G-code 路径
    import argparse
    parser = argparse.ArgumentParser(description="G-code 姿态平滑执行器")
    parser.add_argument("-f", "--file", default=GCODE_FILE, help="G-code 文件路径")
    args = parser.parse_args()

    q = Queue(maxsize=2000)
    stop = threading.Event()

    rc = RobotController(ROBOT_IP, ROBOT_PORT)
    producer = None

    try:
        if not rc.connect() or not rc.initialize():
            return

        # 准备：把末端抬起到安全高度，G0（关节）移动，姿态=0,0,0（映射后即RX=180,RY=0,RZ=0）
        rc.set_speed(G0_VELOCITY_PERCENT)
        rc.add_point((0.0, 0.0, 20.0, 0.0, 0.0, 0.0), 'G0')
        rc.flush(1)
        print("⏳ 等待准备完成 ...")
        t0 = time.time()
        while True:
            qlen = rc.get_queue_length()
            if qlen == 0:
                break
            if time.time() - t0 > 30:
                raise TimeoutError("准备动作超时")
            time.sleep(0.05)
        print("✅ 准备完成")

        # 启动生产者
        producer = threading.Thread(target=gcode_producer, args=(args.file, q, stop), daemon=True)
        producer.start()

        sent = 0
        batch = []
        cur_type = 'G0'  # 当前速度态

        while not stop.is_set():
            ctl_len = rc.get_queue_length()
            if ctl_len < 0:
                print("\n❌ 控制器队列查询失败")
                break

            # 控制本地批量，避免溢出控制器队列
            if ctl_len < CONTROLLER_QUEUE_CAPACITY - BATCH_SEND_SIZE:
                try:
                    item = q.get(timeout=0.05)
                except Empty:
                    item = None

                if item is None:
                    # 生产者结束
                    if batch:
                        rc.flush(len(batch))
                        batch.clear()
                    # 等控制器清空
                    if rc.get_queue_length() == 0:
                        print("\n🎉 全部执行完毕")
                        break
                    time.sleep(0.05)
                    continue

                move_type, pose = item

                # 速度态切换（G0/G1）→ 先flush旧批次，再切速度
                if move_type != cur_type:
                    if batch:
                        rc.flush(len(batch))
                        batch.clear()
                    rc.set_speed(G0_VELOCITY_PERCENT if move_type == 'G0' else G1_VELOCITY_PERCENT)
                    cur_type = move_type

                # 入队
                rc.add_point(pose, move_type)
                batch.append(item)
                sent += 1

                # 批量发给控制器
                if len(batch) >= BATCH_SEND_SIZE:
                    rc.flush(len(batch))
                    batch.clear()

            # 轻量打印
            print(f"\r📊 已发 {sent} 点 | 本地{q.qsize():4d} | 控制器{ctl_len:3d} | 模式 {cur_type} ", end="")
            time.sleep(0.01)

    except KeyboardInterrupt:
        print("\n🛑 中断")
    except Exception as e:
        print("\n❌ 运行异常：", e)
    finally:
        stop.set()
        if producer and producer.is_alive():
            producer.join()
        try:
            rc.power_off()
        finally:
            rc.disconnect()


if __name__ == "__main__":
    main()
