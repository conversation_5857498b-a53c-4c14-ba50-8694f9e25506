#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 交互式执行程序 (v4 - 正确运动模式)

功能:
1.  根据G-code的类型调用正确的底层运动指令：G1使用线性移动(moveL)，
    G0使用关节移动(moveJ)，从根本上区分了两种运动模式。
2.  完全实现G-code坐标的“模态”特性，未指定的轴会自动继承上一指令的状态，
    保证了指令间的姿态连续性。
3.  在正确继承和运动模式的基础上，自动分解复杂的G1移动指令，保证运动平滑。
4.  清晰地显示原始G-code与最终执行的完整指令，方便单步调试。
"""

import sys
import os
import time
import re
import math
import copy

# 添加lib目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))

from config import ROBOT_IP, ROBOT_PORT
import nrc_interface as nrc

# --- 全局参数配置 ---
GCODE_FILE = "jiyi.Gcode"
USER_COORD_NUMBER = 1

# 速度配置 (与queued_gcode_executor.py同步)
G0_VELOCITY_PERCENT = 40
G1_VELOCITY_PERCENT = 30
ACCEL_PERCENT = 20

# 平滑度配置 (0-8)
SMOOTHING_LEVEL = 3

# G-code默认/初始角度值
GCODE_DEFAULT_A = 0.0
GCODE_DEFAULT_B = 0.0
GCODE_DEFAULT_C = 0.0

# G-code角度到机械臂角度的偏移量
GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = 0.0


def normalize_angle_degrees(angle):
    """将角度标准化到 [-180, 180] 范围内"""
    while angle > 180: angle -= 360
    while angle <= -180: angle += 360
    return angle

class RobotController:
    """封装了机器人连接、控制和状态查询的类"""
    def __init__(self, ip, port):
        self.ip = ip; self.port = port; self.socket_fd = -1; self.is_connected = False

    def connect(self):
        print(f"🔗 正在连接机械臂 {self.ip}:{self.port}...")
        self.socket_fd = nrc.connect_robot(self.ip, str(self.port))
        if self.socket_fd <= 0: print("❌ 连接失败！"); self.is_connected = False; return False
        print(f"✅ 连接成功！Socket ID: {self.socket_fd}"); self.is_connected = True; return True

    def disconnect(self):
        if not self.is_connected: return
        print("🔌 正在断开连接..."); nrc.disconnect_robot(self.socket_fd)
        self.is_connected = False; self.socket_fd = -1; print("✅ 连接已断开。")

    def initialize_robot(self):
        if not self.is_connected or not self._power_on_if_needed(): return False
        print("ℹ️ 切换到运行模式...")
        if nrc.set_current_mode(self.socket_fd, 2) != 0: print("❌ 切换运行模式失败"); return False
        time.sleep(0.2)
        print(f"ℹ️ 设置用户坐标系: {USER_COORD_NUMBER}")
        if nrc.set_user_coord_number(self.socket_fd, USER_COORD_NUMBER) != 0: print("❌ 设置用户坐标系失败"); return False
        time.sleep(0.1)
        print("ℹ️ 清空并启用队列模式...")
        nrc.queue_motion_clear_Data(self.socket_fd); time.sleep(0.1)
        if nrc.queue_motion_set_status(self.socket_fd, True) != 0: print("❌ 启用队列模式失败"); return False
        time.sleep(0.1)
        mode = self.get_current_mode()
        print(f"✅ 机器人初始化完成。当前模式: {{0:'示教', 1:'远程', 2:'运行'}}.get(mode, '未知') ({mode})")
        return True

    def _power_on_if_needed(self):
        try:
            state_ref = 0; res = nrc.get_servo_state(self.socket_fd, state_ref)
            if isinstance(res, list) and res[1] == 3: print("✅ 伺服已上电。"); return True
            print("ℹ️ 需要上电，开始流程...")
            nrc.set_current_mode(self.socket_fd, 0); time.sleep(0.2)
            nrc.clear_error(self.socket_fd); time.sleep(0.1)
            nrc.set_servo_state(self.socket_fd, 1); time.sleep(1.5)
            print("   -> 执行上电指令...")
            if nrc.set_servo_poweron(self.socket_fd) != 0: print("❌ 上电失败！"); return False
            time.sleep(1.5)
            res = nrc.get_servo_state(self.socket_fd, state_ref)
            final_status = res[1] if isinstance(res, list) else -1
            if final_status == 3: print("✅ 上电成功！"); return True
            else: print(f"❌ 上电后状态异常: {final_status}"); return False
        except Exception as e: print(f"❌ 上电过程异常: {e}"); return False

    def power_off(self):
        if not self.is_connected: return
        print("\nℹ️ 安全下电..."); nrc.queue_motion_stop(self.socket_fd); time.sleep(0.5)
        nrc.set_servo_poweroff(self.socket_fd); time.sleep(1); print("✅ 已下电。")

    def get_current_mode(self):
        res = nrc.get_current_mode(self.socket_fd, 0); return res[1] if isinstance(res, list) and res[0] == 0 else -1
        
    def get_queue_length(self):
        res = nrc.queue_motion_get_queuelen(self.socket_fd, 0); return res[1] if isinstance(res, list) and res[0] == 0 else -1
    
    def set_speed(self, speed_percent):
        print(f"⚡️ 切换全局速度为: {speed_percent}%")
        if nrc.set_speed(self.socket_fd, speed_percent) != 0: print("❌ 设置速度失败"); return False
        time.sleep(0.1); return True

    def add_point_to_queue(self, point, move_type):
        """将点添加到队列，并根据move_type选择正确的运动指令。"""
        a = point.get('a', GCODE_DEFAULT_A); b = point.get('b', GCODE_DEFAULT_B); c = point.get('c', GCODE_DEFAULT_C)
        rx_rad = math.radians(normalize_angle_degrees(a + GCODE_TO_ROBOT_OFFSET_A))
        ry_rad = math.radians(normalize_angle_degrees(b + GCODE_TO_ROBOT_OFFSET_B))
        rz_rad = math.radians(normalize_angle_degrees(c + GCODE_TO_ROBOT_OFFSET_C))
        target_pos = [point['x'], point['y'], point['z'], rx_rad, ry_rad, rz_rad]
        
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0
        move_cmd.targetPosValue = nrc.VectorDouble(target_pos)
        move_cmd.coord = 3; move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = 100; move_cmd.acc = ACCEL_PERCENT; move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL

        if move_type == 'G1':
            result = nrc.queue_motion_push_back_moveL(self.socket_fd, move_cmd)
        else:  # 'G0' uses Joint movement for speed and smoothness
            result = nrc.queue_motion_push_back_moveJ(self.socket_fd, move_cmd)

        if result != 0:
            print(f"❌ 添加点到队列失败 (类型: {move_type})，错误码: {result}")
            return False
        return True

    def sync_queue(self, size):
        if not self.is_connected or size == 0: return False
        if nrc.queue_motion_send_to_controller(self.socket_fd, size) != 0: print("❌ 同步队列失败"); return False
        return True

def parse_gcode_file(filepath):
    print(f"🔩 解析G-code文件: {filepath}...")
    commands = []
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
            for line_num, line in enumerate(f, 1):
                line = line.strip().upper()
                if not line or line.startswith(';'): continue
                cmd_type = 'G0' if line.startswith('G0') else 'G1' if line.startswith('G1') else None
                if not cmd_type: continue
                coords = dict(coord_regex.findall(line))
                if any(ax in coords for ax in 'XYZ'):
                    commands.append({'line_num': line_num, 'raw_line': line, 'type': cmd_type, 'coords_from_gcode': coords})
    except FileNotFoundError: print(f"❌ G-code文件未找到: {filepath}"); return None
    except Exception as e: print(f"❌ 解析G-code文件时出错: {e}"); return None
    print(f"✅ 文件解析完成，共找到 {len(commands)} 条有效移动指令。")
    return commands

def execute_single_move(robot, coords_to_execute, move_type, speed_type, current_speed_ref, display_info):
    print("-" * 60); print(display_info)
    input("  >>> 请按回车键以发送此指令...")
    if speed_type != current_speed_ref[0]:
        new_speed = G0_VELOCITY_PERCENT if speed_type == 'G0' else G1_VELOCITY_PERCENT
        if not robot.set_speed(new_speed): print("❌ 设置速度失败"); return False
        current_speed_ref[0] = speed_type
    print("  🚀 正在发送指令...")
    if not robot.add_point_to_queue(coords_to_execute, move_type) or not robot.sync_queue(1):
        print("❌ 发送指令到控制器失败"); return False
    print("  ⏳ 等待指令执行完成...")
    start_time = time.time()
    while True:
        q_len = robot.get_queue_length()
        if q_len == 0: break
        if q_len == -1: raise ConnectionError("等待时连接丢失")
        if time.time() - start_time > 60: raise TimeoutError("等待指令完成超时")
        time.sleep(0.1)
    print("  ✅ 指令执行完毕！")
    return True

def main():
    print("=" * 60); print("INEXBOT机械臂 G-code 交互式执行程序 (v4 - 正确运动模式)"); print("=" * 60)
    robot = RobotController(ROBOT_IP, ROBOT_PORT)
    gcode_commands = parse_gcode_file(GCODE_FILE)
    if not gcode_commands: return

    try:
        if not robot.connect() or not robot.initialize_robot(): return
        print("\n" + "="*20 + " 开始交互式执行G-code " + "="*20)
        print("👉 按【回车】执行，【Ctrl+C】退出。")

        # 准备动作
        prep_coords = {'x': 0.0, 'y': 0.0, 'z': 20.0, 'a': GCODE_DEFAULT_A, 'b': GCODE_DEFAULT_B, 'c': GCODE_DEFAULT_C}
        last_executed_coords = copy.deepcopy(prep_coords)
        current_speed_ref = [None]
        
        print("\n" + "="*20 + " 执行准备动作 " + "="*20)
        prep_display_info = f"准备动作: G0 X{prep_coords['x']:.4f} Y{prep_coords['y']:.4f} Z{prep_coords['z']:.4f} ..."
        if not execute_single_move(robot, prep_coords, 'G0', 'G0', current_speed_ref, prep_display_info):
            raise Exception("执行准备动作失败")
        print("✅ 准备动作完成。"); time.sleep(1.5)


        for i, command in enumerate(gcode_commands):
            coords_from_gcode = command['coords_from_gcode']
            command_type = command['type']
            
            pos_changed = any(k.upper() in coords_from_gcode and not math.isclose(float(coords_from_gcode[k.upper()]), last_executed_coords[k]) for k in 'xyz')
            orient_changed = any(k.upper() in coords_from_gcode and not math.isclose(float(coords_from_gcode[k.upper()]), last_executed_coords[k]) for k in 'abc')

            final_target_coords = copy.deepcopy(last_executed_coords)
            for k_upper, v_str in coords_from_gcode.items():
                final_target_coords[k_upper.lower()] = float(v_str)

            is_complex_g1_move = command_type == 'G1' and pos_changed and orient_changed

            if is_complex_g1_move:
                print("-" * 60)
                print(f"检测到复杂G1移动 (指令 {i + 1}/{len(gcode_commands)}, 原文件第 {command['line_num']} 行):")
                print(f"  原始 G-CODE: {command['raw_line']}")
                print("  将分解为两个步骤执行以保证平滑性。")

                move_step_coords = copy.deepcopy(last_executed_coords)
                move_step_coords.update({k: final_target_coords[k] for k in 'xyz'})
                display_info = (f"  分解步骤 1/2 (移动): G1 "
                                f"X{move_step_coords['x']:.4f} Y{move_step_coords['y']:.4f} Z{move_step_coords['z']:.4f} "
                                f"A{move_step_coords['a']:.3f} B{move_step_coords['b']:.3f} C{move_step_coords['c']:.3f}")
                if not execute_single_move(robot, move_step_coords, 'G1', command_type, current_speed_ref, display_info): break
                last_executed_coords.update(move_step_coords)

                display_info = (f"  分解步骤 2/2 (旋转): G1 "
                                f"X{final_target_coords['x']:.4f} Y{final_target_coords['y']:.4f} Z{final_target_coords['z']:.4f} "
                                f"A{final_target_coords['a']:.3f} B{final_target_coords['b']:.3f} C{final_target_coords['c']:.3f}")
                if not execute_single_move(robot, final_target_coords, 'G1', command_type, current_speed_ref, display_info): break
                last_executed_coords.update(final_target_coords)
            else:
                display_info = (f"准备执行指令 {i + 1}/{len(gcode_commands)} (原文件第 {command['line_num']} 行):\n"
                                f"  原始 G-CODE: {command['raw_line']}\n"
                                f"  执行 G-CODE: {command_type} "
                                f"X{final_target_coords['x']:.4f} Y{final_target_coords['y']:.4f} Z{final_target_coords['z']:.4f} "
                                f"A{final_target_coords['a']:.3f} B{final_target_coords['b']:.3f} C{final_target_coords['c']:.3f}")
                if not execute_single_move(robot, final_target_coords, command_type, command_type, current_speed_ref, display_info): break
                last_executed_coords.update(final_target_coords)
            
        print("\n🎉 所有指令执行完毕！")

    except (KeyboardInterrupt, EOFError):
        print("\n🚫 检测到手动中断...")
    except Exception as e:
        print(f"\n❌ 发生严重错误: {e}")
    finally:
        print("\nℹ️ 开始清理和关闭程序...")
        if robot.is_connected:
            robot.power_off()
            robot.disconnect()
        print("✅ 程序已安全退出。")

if __name__ == "__main__":
    main()
