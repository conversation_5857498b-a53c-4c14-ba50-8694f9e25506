#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gcode_pose_polyline_timescaled_curvelimit_20250811.py
-----------------------------------------------------
“忠实几何 + 不超调姿态”版本：
  • 几何：严格沿原 G1 折线（polyline），不做空间样条，不制造“鼓包/偏离”；
  • 姿态：关键帧 = 原始姿态（A/B/C+offset），段内用 SLERP（最短弧）——不会超调；
  • 采样：仅按几何弧长等距细分（由 --ds），角步长（--da）仅影响每段最少细分数；
  • 时间：按几何弧长 s 做前后向加速度受限时间标定，v_max 由线速上限、角速上限（按 dθ/ds）和曲率限速共同约束；
  • 细节：Euler 连续展开、去零长段、分段 set_speed 档位。
"""

import os, re, time, math, threading
from queue import Queue, Empty

# ------------------------- 基本配置 -------------------------

GCODE_FILE = "jiyi copy 2.Gcode"
try:
    from config import ROBOT_IP, ROBOT_PORT
except Exception:
    ROBOT_IP, ROBOT_PORT = "************", "6001"

USER_COORD_NUMBER = 1

# 速度/平滑
G0_VEL_PCT = 35
G1_PCT_MIN = 35
G1_PCT_MAX = 60
G1_PCT_STEP = 10
ACCEL_PCT = 20
PL_SMOOTH  = 5

CTRL_QUEUE_CAP = 200
BATCH_SEND = 20

# ------------------------ 采样/时间参数 ----------------------

# A/B/C → RX/RY/RZ 的静态差补（A=B=C=0 应 → RX=180,RY=0,RZ=0）
GABC_TO_RXYZ = (180.0, 0.0, 0.0)

# 几何细分步长（mm）和角步长（仅用于计算最小细分数，位置仍沿直线）
DS_POS = 1.0
DA_DEG = 1.0

# 线/角速度上限（用于时间标定）
V_LIN_MAX = 60.0     # mm/s
V_ANG_MAX = 45.0     # deg/s

# s 域加速度（等效 mm/s^2）
A_EQ_MAX  = 300.0

# 曲率限速（横向加速度上限，mm/s^2）
A_LAT_MAX = 800.0

# 端点非零末速比例（防止完全停住抖动）
V_END_RATIO = 0.05

# 过滤阈值
EPS_POS = 1e-4
EPS_ANG = 1e-3

# -----------------------------------------------------------

import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# ---------------------------- 数学 ----------------------------

def deg2rad(d): return d * math.pi / 180.0
def rad2deg(r): return r * 180.0 / math.pi
def clamp(x, lo, hi): return max(lo, min(hi, x))

def normalize_angle_deg(a):
    a = (a + 180.0) % 360.0 - 180.0
    if a <= -180.0: a += 360.0
    return a

def unwrap_to_prev(cur, prev):
    k = round((prev - cur) / 360.0)
    return cur + 360.0 * k

def quat_normalize(q):
    w,x,y,z = q
    n = math.sqrt(w*w+x*x+y*y+z*z)
    if n == 0: return (1.0,0.0,0.0,0.0)
    return (w/n, x/n, y/n, z/n)

def quat_dot(a,b): return a[0]*b[0]+a[1]*b[1]+a[2]*b[2]+a[3]*b[3]

def euler_xyz_deg_to_quat(rx, ry, rz):
    rx, ry, rz = deg2rad(rx), deg2rad(ry), deg2rad(rz)
    cx, sx = math.cos(rx/2), math.sin(rx/2)
    cy, sy = math.cos(ry/2), math.sin(ry/2)
    cz, sz = math.cos(rz/2), math.sin(rz/2)
    w = cz*cy*cx + sz*sy*sx
    x = cz*cy*sx - sz*sy*cx
    y = cz*sy*cx + sz*cy*sx
    z = sz*cy*cx - cz*sy*sx
    return quat_normalize((w,x,y,z))

def quat_to_euler_xyz_deg(q):
    w,x,y,z = q
    r11 = 1 - 2*(y*y + z*z)
    r21 = 2*(x*y + w*z)
    r31 = 2*(x*z - w*y)
    r32 = 2*(y*z + w*x)
    r33 = 1 - 2*(x*x + y*y)
    rx = math.atan2(r32, r33)
    ry = -math.asin(max(-1.0, min(1.0, r31)))
    rz = math.atan2(r21, r11)
    return (rad2deg(rx), rad2deg(ry), rad2deg(rz))

def quat_slerp(q0, q1, t):
    cos_th = quat_dot(q0,q1)
    if cos_th < 0.0:
        q1 = (-q1[0], -q1[1], -q1[2], -q1[3])
        cos_th = -cos_th
    if cos_th > 0.9995:
        w = q0[0] + t*(q1[0]-q0[0])
        x = q0[1] + t*(q1[1]-q0[1])
        y = q0[2] + t*(q1[2]-q0[2])
        z = q0[3] + t*(q1[3]-q0[3])
        return quat_normalize((w,x,y,z))
    th = math.acos(cos_th)
    sin_th = math.sin(th)
    s0 = math.sin((1-t)*th) / sin_th
    s1 = math.sin(t*th) / sin_th
    return (q0[0]*s0+q1[0]*s1, q0[1]*s0+q1[1]*s1, q0[2]*s0+q1[2]*s1, q0[3]*s0+q1[3]*s1)

def chord_len(p, q):
    dx,dy,dz = (q[0]-p[0], q[1]-p[1], q[2]-p[2])
    return math.sqrt(dx*dx + dy*dy + dz*dz)

def angle_between_quats_deg(q0, q1):
    c = abs(quat_dot(q0,q1))
    c = max(-1.0, min(1.0, c))
    return rad2deg(2.0 * math.acos(c))

# 3D 离散曲率（用转角/邻段长度近似）
def discrete_curvature(p_prev, p_cur, p_next):
    v1 = (p_cur[0]-p_prev[0], p_cur[1]-p_prev[1], p_cur[2]-p_prev[2])
    v2 = (p_next[0]-p_cur[0], p_next[1]-p_cur[1], p_next[2]-p_cur[2])
    l1 = math.sqrt(v1[0]**2+v1[1]**2+v1[2]**2) + 1e-9
    l2 = math.sqrt(v2[0]**2+v2[1]**2+v2[2]**2) + 1e-9
    dot = (v1[0]*v2[0]+v1[1]*v2[1]+v1[2]*v2[2]) / (l1*l2)
    dot = clamp(dot, -1.0, 1.0)
    theta = math.acos(dot)   # rad
    s = 0.5*(l1+l2)
    if s < 1e-9: return 0.0
    kappa = theta / s        # 近似曲率，单位 1/mm
    return kappa

# ---------------------- 控制器封装 ----------------------

class RobotController:
    def __init__(self, ip, port):
        self.ip, self.port = ip, port
        self.socket_fd = -1
        self.is_connected = False

    def connect(self):
        print(f"🔗 连接 {self.ip}:{self.port} ...")
        self.socket_fd = nrc.connect_robot(self.ip, str(self.port))
        self.is_connected = self.socket_fd > 0
        print("✅ 已连接" if self.is_connected else "❌ 连接失败")
        return self.is_connected

    def disconnect(self):
        if self.is_connected:
            try: nrc.disconnect_robot(self.socket_fd)
            finally:
                self.is_connected = False
                self.socket_fd = -1

    def _power_on_if_needed(self) -> bool:
        try:
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            cur = res[1] if isinstance(res, list) else -1
            if cur == 3: return True
            nrc.set_current_mode(self.socket_fd, 0)  # TEACH
            time.sleep(0.1)
            nrc.clear_error(self.socket_fd)
            nrc.set_servo_state(self.socket_fd, 1)  # READY
            time.sleep(1.0)
            if nrc.set_servo_poweron(self.socket_fd) != 0:
                return False
            time.sleep(1.0)
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            return (isinstance(res, list) and res[1] == 3)
        except Exception:
            return False

    def initialize(self) -> bool:
        if not self.is_connected: return False
        if not self._power_on_if_needed(): return False
        if nrc.set_current_mode(self.socket_fd, 2) != 0:  # RUN
            print("❌ 切到运行模式失败"); return False
        nrc.queue_motion_clear_Data(self.socket_fd)
        if nrc.queue_motion_set_status(self.socket_fd, True) != 0:
            print("❌ 启用队列失败"); return False
        if nrc.set_user_coord_number(self.socket_fd, USER_COORD_NUMBER) != 0:
            print("❌ 设置用户坐标失败"); return False
        print("✅ 初始化完毕（运行模式/队列开启）")
        return True

    def power_off(self):
        try:
            nrc.queue_motion_stop(self.socket_fd); time.sleep(0.2)
            nrc.set_servo_poweroff(self.socket_fd)
        except Exception:
            pass

    def get_queue_length(self) -> int:
        res = nrc.queue_motion_get_queuelen(self.socket_fd, 0)
        return res[1] if isinstance(res, list) and res[0] == 0 else -1

    def set_speed(self, percent: int) -> bool:
        return nrc.set_speed(self.socket_fd, percent) == 0

    def add_point(self, x,y,z, rx_deg, ry_deg, rz_deg, move_type='G1'):
        # 保持展开后的角度，不再归一化到 (-180,180]
        rx = deg2rad(rx_deg); ry = deg2rad(ry_deg); rz = deg2rad(rz_deg)
        cmd = nrc.MoveCmd()
        cmd.targetPosType = 0
        cmd.targetPosValue = nrc.VectorDouble([x,y,z, rx,ry,rz])
        cmd.coord = 3
        cmd.userNum = USER_COORD_NUMBER
        cmd.velocity = 100
        cmd.acc = ACCEL_PCT
        cmd.dec = ACCEL_PCT
        cmd.pl  = PL_SMOOTH
        if move_type == 'G1':
            r = nrc.queue_motion_push_back_moveL(self.socket_fd, cmd)
        else:
            r = nrc.queue_motion_push_back_moveJ(self.socket_fd, cmd)
        if r != 0:
            raise RuntimeError(f"push_back失败: {r}")

    def flush(self, n):
        if n<=0: return True
        return nrc.queue_motion_send_to_controller(self.socket_fd, n) == 0

# --------------------- 解析 G-code ----------------------

_COORD_RE = re.compile(r'([XYZABCFE])([-+]?\d*\.?\d+)')

class WP:
    __slots__ = ("cmd","x","y","z","a","b","c","q")
    def __init__(self, cmd, x,y,z, a,b,c, q):
        self.cmd, self.x, self.y, self.z = cmd, x,y,z
        self.a, self.b, self.c = a,b,c
        self.q = q

def parse_line(line: str):
    s = line.strip()
    if not s or s.startswith(';'): return None
    up = s.upper()
    if not (up.startswith('G0') or up.startswith('G1')): return None
    cmd = 'G0' if up.startswith('G0') else 'G1'
    vals = dict((k, float(v)) for k, v in _COORD_RE.findall(up))
    x = vals.get('X'); y = vals.get('Y'); z = vals.get('Z')
    a = vals.get('A'); b = vals.get('B'); c = vals.get('C')
    return cmd, x,y,z, a,b,c

def gabc_to_rxyz(a,b,c):
    rx = normalize_angle_deg(a + GABC_TO_RXYZ[0])
    ry = normalize_angle_deg(b + GABC_TO_RXYZ[1])
    rz = normalize_angle_deg(c + GABC_TO_RXYZ[2])
    return rx,ry,rz

def load_wps(filepath):
    last = {'x':0.0,'y':0.0,'z':20.0,'a':0.0,'b':0.0,'c':0.0}
    wps = []
    with open(filepath,'r',encoding='utf-8') as f:
        for raw in f:
            p = parse_line(raw)
            if not p: continue
            cmd,x,y,z,a,b,c = p
            cur = dict(last)
            if x is not None: cur['x']=x
            if y is not None: cur['y']=y
            if z is not None: cur['z']=z
            if cmd == 'G0':
                cur['a']=0.0; cur['b']=0.0; cur['c']=0.0
            else:
                if a is not None: cur['a']=a
                if b is not None: cur['b']=b
                if c is not None: cur['c']=c
            rx,ry,rz = gabc_to_rxyz(cur['a'],cur['b'],cur['c'])
            q = euler_xyz_deg_to_quat(rx,ry,rz)
            wps.append(WP(cmd, cur['x'],cur['y'],cur['z'], cur['a'],cur['b'],cur['c'], q))
            last = cur
    # 半球对齐
    for i in range(1,len(wps)):
        if quat_dot(wps[i-1].q, wps[i].q) < 0.0:
            w = wps[i]
            wps[i] = WP(w.cmd, w.x,w.y,w.z, w.a,w.b,w.c, (-w.q[0],-w.q[1],-w.q[2],-w.q[3]))
    return wps

# ------------------ 几何细分（polyline + SLERP） ------------------

def polyline_presample_block(P, Q):
    """
    输入：P 点列、Q 姿态列（与 P 对齐；均来自原 G1）
    输出：细分后的 (x,y,z,q) 列表；位置严格在原折线上；姿态逐段 SLERP。
    """
    out = []
    out.append((P[0][0],P[0][1],P[0][2], Q[0]))
    for i in range(len(P)-1):
        p0, p1 = P[i], P[i+1]
        q0, q1 = Q[i], Q[i+1]
        L = chord_len(p0,p1)
        ang = angle_between_quats_deg(q0,q1)
        n_pos = max(1, int(math.ceil(L / DS_POS)))
        n_ang = max(1, int(math.ceil(ang / DA_DEG)))
        steps = max(n_pos, n_ang)
        for k in range(1, steps+1):
            t = k/steps
            x = p0[0] + (p1[0]-p0[0]) * t
            y = p0[1] + (p1[1]-p0[1]) * t
            z = p0[2] + (p1[2]-p0[2]) * t
            q = quat_slerp(q0, q1, t)  # 不超调
            out.append((x,y,z,q))
    return out

def poses_quat_to_unwrapped_deg(poses_xyz_q):
    out = []
    last_rx = last_ry = last_rz = None
    last_x = last_y = last_z = None
    for x,y,z,q in poses_xyz_q:
        rx,ry,rz = quat_to_euler_xyz_deg(q)
        if last_rx is None:
            rxu,ryu,rzu = rx,ry,rz
        else:
            rxu = unwrap_to_prev(rx, last_rx)
            ryu = unwrap_to_prev(ry, last_ry)
            rzu = unwrap_to_prev(rz, last_rz)
        if last_x is not None:
            dp = math.sqrt((x-last_x)**2 + (y-last_y)**2 + (z-last_z)**2)
            da = max(abs(rxu-last_rx), abs(ryu-last_ry), abs(rzu-last_rz))
            if dp < EPS_POS and da < EPS_ANG:
                continue
        out.append((x,y,z, rxu,ryu,rzu))
        last_rx,last_ry,last_rz = rxu,ryu,rzu
        last_x,last_y,last_z = x,y,z
    return out

# -------------------- 时间标定（弧长 + 曲率） --------------------

def time_scale_polyline(P, Q):
    """
    基于每个线段的长度 L_i 和姿态角差 TH_i，得到每段 v_max：
      v_max_i = min( V_LIN_MAX,
                     V_ANG_MAX * L_i / max(TH_i,1e-6),
                     曲率限速 )
    再做前向/后向加速度限幅，返回每段速度和档位合并区间。
    """
    n = len(P)
    if n < 2:
        return [0.0],[G1_PCT_MIN],[(0,0,G1_PCT_MIN)]

    L = [chord_len(P[i], P[i+1]) for i in range(n-1)]
    TH= [angle_between_quats_deg(Q[i], Q[i+1]) for i in range(n-1)]

    # 曲率（定义在顶点），用于限速：v_curve ≤ sqrt(A_LAT_MAX / κ)
    kappa = [0.0]*n
    for i in range(1, n-1):
        kappa[i] = discrete_curvature(P[i-1], P[i], P[i+1])
    v_curve_seg = []
    for i in range(n-1):
        k0 = kappa[i]; k1 = kappa[i+1]
        k = max(k0, k1)
        v_c = math.sqrt(A_LAT_MAX / max(k, 1e-9)) if k>1e-9 else float('inf')
        v_curve_seg.append(v_c)

    v_local = []
    for i in range(n-1):
        v_lin = V_LIN_MAX
        v_ang = V_ANG_MAX * (L[i] / max(TH[i], 1e-6))  # dθ/ds 限制
        v_local.append( min(v_lin, v_ang, v_curve_seg[i]) )

    # 前后向加减速限幅（定义在节点）
    v_nodes = [0.0]*n
    vmax_all = max(v_local) if v_local else 0.0
    v_nodes[0] = V_END_RATIO * vmax_all
    # forward
    for i in range(1, n):
        v_allow = math.sqrt(max(0.0, v_nodes[i-1]*v_nodes[i-1] + 2*A_EQ_MAX*L[i-1]))
        v_nodes[i] = min(v_allow, v_local[i-1])
    # backward
    v_nodes[-1] = min(v_nodes[-1], V_END_RATIO * vmax_all)
    for i in range(n-2, -1, -1):
        v_allow = math.sqrt(max(0.0, v_nodes[i+1]*v_nodes[i+1] + 2*A_EQ_MAX*L[i]))
        v_nodes[i] = min(v_nodes[i], v_allow, v_local[i])

    v_seg = [min(v_nodes[i], v_nodes[i+1], v_local[i]) for i in range(n-1)]
    v_peak = max(v_seg) if v_seg else 1.0
    if v_peak < 1e-9: v_peak = 1.0

    # 映射到 set_speed 档位
    p_seg = []
    for v in v_seg:
        r = v / v_peak
        p = G1_PCT_MIN + r * (G1_PCT_MAX - G1_PCT_MIN)
        p = int(round(p / G1_PCT_STEP) * G1_PCT_STEP)
        p = clamp(p, G1_PCT_MIN, G1_PCT_MAX)
        p_seg.append(p)

    # 合并相邻相同档位（end 为段索引的上界）
    merged = []
    start = 0; cur = p_seg[0]
    for i in range(len(p_seg)):
        if p_seg[i] != cur:
            merged.append((start, i, cur))
            start = i; cur = p_seg[i]
    merged.append((start, len(p_seg), cur))

    return L, p_seg, merged

# ------------------------ 生产者 ------------------------

def producer(filepath: str, q_pose: Queue, q_sched: Queue, stop):
    wps = load_wps(filepath)
    if not wps:
        q_pose.put(None); q_sched.put(None); return

    i = 0
    while i < len(wps):
        # 先把连续 G0 透传（末端朝下）
        while i < len(wps) and wps[i].cmd == 'G0':
            rx,ry,rz = gabc_to_rxyz(0.0,0.0,0.0)
            q_pose.put(('G0', (wps[i].x, wps[i].y, wps[i].z, rx,ry,rz)))
            i += 1
        if i >= len(wps): break

        # 收集连续 G1 block（几何严格沿原始折线）
        j = i
        while j < len(wps) and wps[j].cmd == 'G1':
            j += 1
        block = wps[i:j]
        P = [(w.x,w.y,w.z) for w in block]
        Q = [w.q for w in block]
        # 再做一次半球对齐
        for k in range(1,len(Q)):
            if quat_dot(Q[k-1], Q[k]) < 0.0:
                w = Q[k]; Q[k] = (-w[0],-w[1],-w[2],-w[3])

        # 细分（仅按几何等距 + 每段 SLERP）
        fine = polyline_presample_block(P, Q)           # (x,y,z,q)
        fine_deg = poses_quat_to_unwrapped_deg(fine)    # (x,y,z,rx,ry,rz)

        # 时间标定（按折线段）
        L, p_seg, merged = time_scale_polyline(P, Q)

        # 下发
        q_pose.put(('G1', fine_deg[0]))
        # merged 的索引以“段”为单位；把对应区间内的采样点下发
        # 先建立段 -> 采样点的前缀和：每段 steps_i = ceil(max(L/DS_POS, TH/DA))
        seg_steps = []
        for s in range(len(P)-1):
            Ls = chord_len(P[s], P[s+1])
            THs= angle_between_quats_deg(Q[s], Q[s+1])
            n_pos = max(1, int(math.ceil(Ls / DS_POS)))
            n_ang = max(1, int(math.ceil(THs / DA_DEG)))
            steps = max(n_pos, n_ang)
            seg_steps.append(steps)
        seg_prefix = [0]
        for st in seg_steps:
            seg_prefix.append(seg_prefix[-1] + st)

        base_idx = 1  # fine_deg 的当前索引（起点占了 1）
        for seg_l, seg_r, pct in merged:
            # 发送 [seg_l, seg_r) 段的采样点，再包括 seg_r 的端点
            send_steps = seg_prefix[seg_r] - seg_prefix[seg_l]
            end_idx = base_idx + send_steps
            for k in range(base_idx, end_idx+1):
                if k < len(fine_deg):
                    q_pose.put(('G1', fine_deg[k]))
            q_sched.put(('G1', pct))
            base_idx = end_idx + 1

        i = j

    q_pose.put(None); q_sched.put(None)

# ------------------------ 主流程 ------------------------

class RC(RobotController): pass

def main():
    import argparse
    global DS_POS, DA_DEG, V_LIN_MAX, V_ANG_MAX, A_EQ_MAX, A_LAT_MAX
    global PL_SMOOTH, G1_PCT_MIN, G1_PCT_MAX, G1_PCT_STEP
    pa = argparse.ArgumentParser("忠实折线 + SLERP + 弧长时间标定 + 曲率限速")
    pa.add_argument("-f","--file", default=GCODE_FILE)
    pa.add_argument("--ds", type=float, default=DS_POS, help="几何细分步长(mm)")
    pa.add_argument("--da", type=float, default=DA_DEG, help="角步长(deg)")
    pa.add_argument("--vlin", type=float, default=V_LIN_MAX, help="线速度上限(mm/s)")
    pa.add_argument("--vang", type=float, default=V_ANG_MAX, help="角速度上限(deg/s)")
    pa.add_argument("--aeq",  type=float, default=A_EQ_MAX,  help="s域加速度上限(mm/s^2)")
    pa.add_argument("--alat", type=float, default=A_LAT_MAX, help="曲率限速的横向加速度上限(mm/s^2)")
    pa.add_argument("--pl", type=int, default=PL_SMOOTH, help="控制器平滑等级(0~8)")
    pa.add_argument("--g1min", type=int, default=G1_PCT_MIN)
    pa.add_argument("--g1max", type=int, default=G1_PCT_MAX)
    pa.add_argument("--g1step", type=int, default=G1_PCT_STEP)
    args = pa.parse_args()

    
    DS_POS, DA_DEG = args.ds, args.da
    V_LIN_MAX, V_ANG_MAX, A_EQ_MAX, A_LAT_MAX = args.vlin, args.vang, args.aeq, args.alat
    PL_SMOOTH = args.pl
    G1_PCT_MIN, G1_PCT_MAX, G1_PCT_STEP = args.g1min, args.g1max, args.g1step

    q_pose = Queue(maxsize=12000)
    q_sched = Queue(maxsize=200)
    stop = threading.Event()

    rc = RC(ROBOT_IP, ROBOT_PORT)
    prod = None

    try:
        if not rc.connect() or not rc.initialize():
            return
        rc.set_speed(G0_VEL_PCT)
        rx0,ry0,rz0 = gabc_to_rxyz(0.0,0.0,0.0)
        rc.add_point(0.0,0.0,20.0, rx0,ry0,rz0, move_type='G0')
        rc.flush(1)

        prod = threading.Thread(target=producer, args=(args.file, q_pose, q_sched, stop), daemon=True)
        prod.start()

        sent = 0
        batch = []
        cur_mode = 'G0'
        cur_pct = None

        while not stop.is_set():
            ctl_len = rc.get_queue_length()
            if ctl_len < 0: print("\n❌ 队列查询失败"); break

            # 速度档位切换（尽量在批次边界）
            try:
                while True:
                    item = q_sched.get_nowait()
                    if item is None: break
                    _, pct = item
                    if pct != cur_pct:
                        if batch: rc.flush(len(batch)); batch.clear()
                        rc.set_speed(pct)
                        cur_pct = pct
            except Empty:
                pass

            if ctl_len < CTRL_QUEUE_CAP - BATCH_SEND:
                try: item = q_pose.get(timeout=0.02)
                except Empty: item = None

                if item is None:
                    if batch: rc.flush(len(batch)); batch.clear()
                    if rc.get_queue_length() == 0:
                        print("\n🎉 执行完成"); break
                    time.sleep(0.02); continue

                move_type, pose = item
                if move_type != cur_mode:
                    if batch: rc.flush(len(batch)); batch.clear()
                    rc.set_speed(G0_VEL_PCT if move_type=='G0' else (cur_pct or G1_PCT_MIN))
                    cur_mode = move_type

                x,y,z,rx,ry,rz = pose
                rc.add_point(x,y,z, rx,ry,rz, move_type)
                batch.append(item); sent += 1
                if len(batch) >= BATCH_SEND:
                    rc.flush(len(batch)); batch.clear()

            print(f"\r📊 已发{sent} | 队列{ctl_len:3d} | 速{cur_pct or 0:3d}% | 模式{cur_mode}", end="")
            time.sleep(0.01)

    except KeyboardInterrupt:
        print("\n🛑 中断")
    except Exception as e:
        print("\n❌ 异常：", e)
    finally:
        stop.set()
        if prod and prod.is_alive(): prod.join()
        try: rc.power_off()
        finally: rc.disconnect()

if __name__ == "__main__":
    main()
