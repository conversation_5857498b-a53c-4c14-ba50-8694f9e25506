#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KISS + YAGNI + SOLID 版本：只做一件事——沿给定路径平稳走完，尽量快但不冲、不急停。

设计取舍：
  • KISS：
      - 不做三维样条；严格走原 G1 折线（polyline）。
      - 姿态只在段内做 SLERP（四元数最短弧），保证“不过冲”。
      - 采样只按几何弧长/角差控制点密度；参数少且直观。
  • YAGNI：
      - 不做多余的前瞻避障/重规划（假定路径永远可达）。
      - 不做复杂的在线最优；仅用前后向加速度限幅得到时间标定。
      - 不引入外部依赖；仅依赖 nrc 接口。
  • SOLID（简化贯彻）：
      - SRP：解析、插值、时间标定、下发、容错分别在独立函数/类内。
      - OCP：机器人接口封装为 RobotController，便于替换实现。
      - LSP/ISP/DIP：此处轻量，不额外抽象接口以免过度设计。

满足的业务需求：
  1) 平稳：段间不出现突发加减速；首末端保持非零末速比例，避免起步/停靠冲击。
  2) 大姿态变化自动降速，小变化尽量快：速度上限受 (i) 线速上限 (ii) 角速上限 dθ/ds (iii) 曲率限速 共同约束。
  3) 不中断：若故障，先低速重试；仍失败则清错(1s)+上电(1s)重试；再失败则停等人工；若选择继续，则先抬高再跳到下一点。
  4) 代码注释全部中文。

本版仅添加**日志/调试语句**，不修改任何控制逻辑与数值；可通过环境变量 `ARM_DEBUG=0/1` 控制输出详略。
"""

import os, re, time, math, sys

# ========== 调试日志（CSV）==========
import csv
from datetime import datetime

class TraceLogger:
    """极简 CSV 日志：边动边记录，便于与实际运动对齐"""
    def __init__(self, path):
        self.path = path
        self._fp = open(self.path, "w", newline="", encoding="utf-8")
        self._csv = csv.writer(self._fp)
        self._csv.writerow([
            "abs_time","t_ms","idx","total","run_pct","queue","servo",
            "line_v_mm_s","pct_override",
            "x","y","z","rx","ry","rz",
            "target_x","target_y","target_z","target_rx","target_ry","target_rz"
        ])
        self._fp.flush()
        self._t0 = None

    def log(self, t_now, idx, total, run_pct, queue, servo,
            line_v, pct, cur_pose, target_pose):
        if self._t0 is None:
            self._t0 = t_now
        t_ms = int((t_now - self._t0) * 1000)
        ts = datetime.now().isoformat(timespec="milliseconds")
        x=y=z=rx=ry=rz=None
        if cur_pose is not None:
            x,y,z,rx,ry,rz = cur_pose
        tx=ty=tz=trx=try_=trz=None
        if target_pose is not None:
            tx,ty,tz,trx,try_,trz = target_pose
        self._csv.writerow([ts,t_ms,idx,total,run_pct,queue,servo,
                            f"{line_v:.3f}" if isinstance(line_v,(int,float)) and line_v is not None else "",
                            pct if pct is not None else "",
                            x,y,z,rx,ry,rz,
                            tx,ty,tz,trx,try_,trz])
        self._fp.flush()

    def close(self):
        try:
            self._fp.close()
        except Exception:
            pass
from dataclasses import dataclass
from typing import List, Tuple

# =============== 日志/调试基础 ===============
START_T = time.perf_counter()
DEBUG = (os.environ.get("ARM_DEBUG", "1") != "0")  # 1=打印调试信息，0=安静
LOG_POINT_EVERY = int(os.environ.get("ARM_LOG_POINT_EVERY", 1))  # 每多少个点打印一次点位明细
LOG_RUN_HEAD = os.environ.get("ARM_LOG_RUN_HEAD", "1") != "0"  # 每个速度run起始打印一行
STATUS_EVERY_MS = int(os.environ.get("ARM_STATUS_EVERY_MS", 0))  # 实时状态行刷新周期（毫秒），0=关闭

# --- Windows 控制台 UTF-8，避免中文日志乱码 ---
def _ensure_utf8_console():
    """在 Windows 下切到 UTF-8 代码页，并重设 Python 输出编码。"""
    try:
        if os.name == 'nt':
            os.system("chcp 65001 >NUL")
            try:
                sys.stdout.reconfigure(encoding='utf-8')
                sys.stderr.reconfigure(encoding='utf-8')
            except Exception:
                pass
    except Exception:
        pass

def _ts():
    """返回相对启动的毫秒时间戳字符串。"""
    return f"{(time.perf_counter()-START_T)*1000:8.1f}ms"

def dlog(msg: str):
    """调试输出（带相对时间）。"""
    if DEBUG:
        try:
            print(f"[{_ts()}] {msg}")
        except Exception:
            # 防止编码问题导致异常
            print(msg)

_ensure_utf8_console()

# ------------------------- 基本配置 -------------------------

GCODE_FILE = os.environ.get("GCODE_FILE", "jiyi copy 2.Gcode")  # 可用环境变量覆盖

def _load_config_from_module():
    try:
        from config import ROBOT_IP, ROBOT_PORT  # type: ignore
        return ROBOT_IP, ROBOT_PORT
    except Exception:
        return "************", "6001"

ROBOT_IP, ROBOT_PORT = _load_config_from_module()

USER_COORD_NUMBER = 1   # 用户坐标号

# 速度/平滑（控制器层面的全局档位与加减速参数）
G1_PCT_MIN = 35        # set_speed 最低百分比
G1_PCT_MAX = 60        # set_speed 最高百分比
G1_PCT_STEP = 5        # 挡位步长（越小越细腻，但切换更频繁）
ACCEL_PCT = 20         # MoveL 用的 acc/dec 百分比
PL_SMOOTH  = 5         # 平滑等级（控制器内部处理）

# ------------------------ 采样/时间参数 ----------------------

# A/B/C → RX/RY/RZ 的静态差补（A=B=C=0 → RX=180, RY=0, RZ=0），按现场机械/标定调整
GABC_TO_RXYZ = (180.0, 0.0, 0.0)

# 几何细分步长（mm）与角步长（deg），用于确定段内插值控制点数
DS_POS = 1.0
DA_DEG = 1.0

# 线/角速度上限（时间标定用）
V_LIN_MAX = 60.0      # mm/s
V_ANG_MAX = 45.0      # deg/s

# s 域加速度（等效 mm/s^2，用于前后向限幅）
A_EQ_MAX  = 300.0

# 曲率限速（横向加速度上限，单位 mm/s^2）
A_LAT_MAX = 800.0

# 端点非零末速比例（避免完全停住导致抖动/冲击）
V_END_RATIO = 0.05

# 发送与容错
CTRL_QUEUE_CAP = 200   # 控制器队列容量（估计值，避免一次塞太多）
BATCH_SEND = 20        # 每批次发多少条 MoveL（更小更稳）
# 队列水位（根据控制器容量估计），超过高水位就暂停送，等降到低水位再继续
HIGH_WATER = int(CTRL_QUEUE_CAP * 0.7)
LOW_WATER  = int(CTRL_QUEUE_CAP * 0.4)
ERROR_RETRY_SLOW_PCT = 30  # 故障后低速重试的 set_speed 百分比
LIFT_MM = 2.0          # 二次失败后“略抬”的距离
# 二次失败默认动作（避免再次等待人工输入）：
#   "continue" -> 自动继续，并先抬高 LIFT_MM
#   "stop"     -> 自动停止
AUTO_SECOND_FAIL = os.environ.get("ARM_AUTO_SECOND_FAIL", "continue").lower()

# 自适应采样上限：若采样点数超过该阈值，自动放大 DS_POS/DA_DEG 以降点
MAX_POINTS = 10000
# 结束是否主动断开（部分控制器在大队列进行中主动断开会让示教器报错）
DISCONNECT_AT_END = False

# 过滤阈值
EPS_POS = 1e-4
EPS_ANG = 1e-3

# -----------------------------------------------------------

# 将 nrc 接口加入路径（按你的工程布局微调）
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc  # type: ignore  # 依赖 SWIG 生成的绑定

# 启动时打印一次关键信息
if DEBUG:
    dlog("=== 启动配置 ===")
    dlog(f"ROBOT_IP={ROBOT_IP}:{ROBOT_PORT}  USER_COORD={USER_COORD_NUMBER}")
    dlog(f"DS_POS={DS_POS} mm  DA_DEG={DA_DEG} °  V_LIN_MAX={V_LIN_MAX} mm/s  V_ANG_MAX={V_ANG_MAX} °/s")
    dlog(f"A_EQ_MAX={A_EQ_MAX}  A_LAT_MAX={A_LAT_MAX}  V_END_RATIO={V_END_RATIO}")
    dlog(f"BATCH_SEND={BATCH_SEND}  QUEUE_CAP={CTRL_QUEUE_CAP}  HIGH/LOW={HIGH_WATER}/{LOW_WATER}")
    dlog(f"MAX_POINTS={MAX_POINTS}  DISCONNECT_AT_END={DISCONNECT_AT_END}")

# ---------------------------- 数学/姿态 ----------------------------

def deg2rad(d): return d * math.pi / 180.0

def rad2deg(r): return r * 180.0 / math.pi

def clamp(x, lo, hi): return max(lo, min(hi, x))

# 角度归一到 (-180,180]

def normalize_angle_deg(a):
    a = (a + 180.0) % 360.0 - 180.0
    if a <= -180.0: a += 360.0
    return a

# 依据上一个值展开角度，避免 360° 跳变

def unwrap_to_prev(cur, prev):
    k = round((prev - cur) / 360.0)
    return cur + 360.0 * k

# 四元数工具

def quat_normalize(q):
    w,x,y,z = q
    n = math.sqrt(w*w+x*x+y*z*z) if False else math.sqrt(q[0]*q[0]+q[1]*q[1]+q[2]*q[2]+q[3]*q[3])
    # 上面一行只是显式写出计算，行为不变
    if n == 0: return (1.0,0.0,0.0,0.0)
    return (w/n, x/n, y/n, z/n)


def quat_dot(a,b): return a[0]*b[0]+a[1]*b[1]+a[2]*b[2]+a[3]*b[3]


def euler_xyz_deg_to_quat(rx, ry, rz):
    rx, ry, rz = deg2rad(rx), deg2rad(ry), deg2rad(rz)
    cx, sx = math.cos(rx/2), math.sin(rx/2)
    cy, sy = math.cos(ry/2), math.sin(ry/2)
    cz, sz = math.cos(rz/2), math.sin(rz/2)
    w = cz*cy*cx + sz*sy*sx
    x = cz*cy*sx - sz*sy*cx
    y = cz*sy*cx + sz*cy*sx
    z = sz*cy*cx - cz*sy*sx
    return quat_normalize((w,x,y,z))


def quat_to_euler_xyz_deg(q):
    w,x,y,z = q
    r11 = 1 - 2*(y*y + z*z)
    r21 = 2*(x*y + w*z)
    r31 = 2*(x*z - w*y)
    r32 = 2*(y*z + w*x)
    r33 = 1 - 2*(x*x + y*y)
    rx = math.atan2(r32, r33)
    ry = -math.asin(max(-1.0, min(1.0, r31)))
    rz = math.atan2(r21, r11)
    return (rad2deg(rx), rad2deg(ry), rad2deg(rz))


def quat_slerp(q0, q1, t):
    cos_th = quat_dot(q0,q1)
    if cos_th < 0.0:
        q1 = (-q1[0], -q1[1], -q1[2], -q1[3])
        cos_th = -cos_th
    if cos_th > 0.9995:  # 非常接近时线性插值
        w = q0[0] + t*(q1[0]-q0[0])
        x = q0[1] + t*(q1[1]-q0[1])
        y = q0[2] + t*(q1[2]-q0[2])
        z = q0[3] + t*(q1[3]-q0[3])
        return quat_normalize((w,x,y,z))
    th = math.acos(cos_th)
    sin_th = math.sin(th)
    s0 = math.sin((1-t)*th) / sin_th
    s1 = math.sin(t*th) / sin_th
    return (q0[0]*s0+q1[0]*s1, q0[1]*s0+q1[1]*s1, q0[2]*s0+q1[2]*s1, q0[3]*s0+q1[3]*s1)


def chord_len(p, q):
    dx,dy,dz = (q[0]-p[0], q[1]-p[1], q[2]-p[2])
    return math.sqrt(dx*dx + dy*dy + dz*dz)


def angle_between_quats_deg(q0, q1):
    c = abs(quat_dot(q0,q1))
    c = max(-1.0, min(1.0, c))
    return rad2deg(2.0 * math.acos(c))

# 3D 离散曲率（用转角/邻段长度近似），仅作限速粗估

def discrete_curvature(p_prev, p_cur, p_next):
    v1 = (p_cur[0]-p_prev[0], p_cur[1]-p_prev[1], p_cur[2]-p_prev[2])
    v2 = (p_next[0]-p_cur[0], p_next[1]-p_cur[1], p_next[2]-p_cur[2])
    l1 = math.sqrt(v1[0]**2+v1[1]**2+v1[2]**2) + 1e-9
    l2 = math.sqrt(v2[0]**2+v2[1]**2+v2[2]**2) + 1e-9
    dot = (v1[0]*v2[0]+v1[1]*v2[1]+v1[2]*v2[2]) / (l1*l2)
    dot = clamp(dot, -1.0, 1.0)
    theta = math.acos(dot)   # rad
    s = 0.5*(l1+l2)
    if s < 1e-9: return 0.0
    kappa = theta / s        # 近似曲率，单位 1/mm
    return kappa

# --------------------- G-code 解析 ---------------------

_COORD_RE = re.compile(r'([XYZABCFE])([-+]?\d*\.?\d+)')

@dataclass
class Pose:
    # 原始 G1 关键点（米勒坐标 + 姿态），并缓存姿态四元数
    x: float; y: float; z: float
    a: float; b: float; c: float
    q: Tuple[float,float,float,float]


def parse_line(line: str):
    s = line.strip()
    if not s or s.startswith(';'): return None
    up = s.upper()
    if not (up.startswith('G0') or up.startswith('G1')): return None
    cmd = 'G0' if up.startswith('G0') else 'G1'
    vals = dict((k, float(v)) for k, v in _COORD_RE.findall(up))
    x = vals.get('X'); y = vals.get('Y'); z = vals.get('Z')
    a = vals.get('A'); b = vals.get('B'); c = vals.get('C')
    return cmd, x,y,z, a,b,c


def gabc_to_rxyz(a,b,c):
    # 将 A/B/C 加上静态差补并归一化到 (-180,180]
    rx = normalize_angle_deg(a + GABC_TO_RXYZ[0])
    ry = normalize_angle_deg(b + GABC_TO_RXYZ[1])
    rz = normalize_angle_deg(c + GABC_TO_RXYZ[2])
    return rx,ry,rz


def load_poses(filepath: str) -> List[Pose]:
    # 从 G-code 中抽取 G1 关键点（G0 只更新位置，姿态固定为 0,0,0 对应的差补）
    last = {'x':0.0,'y':0.0,'z':20.0,'a':0.0,'b':0.0,'c':0.0}
    pts: List[Pose] = []
    line_cnt = 0
    g1_cnt = 0
    with open(filepath,'r',encoding='utf-8') as f:
        for raw in f:
            line_cnt += 1
            p = parse_line(raw)
            if not p: continue
            cmd,x,y,z,a,b,c = p
            if cmd == 'G1':
                g1_cnt += 1
            cur = dict(last)
            if x is not None: cur['x']=x
            if y is not None: cur['y']=y
            if z is not None: cur['z']=z
            if cmd == 'G0':
                cur['a']=0.0; cur['b']=0.0; cur['c']=0.0
            else:
                if a is not None: cur['a']=a
                if b is not None: cur['b']=b
                if c is not None: cur['c']=c
            rx,ry,rz = gabc_to_rxyz(cur['a'],cur['b'],cur['c'])
            q = euler_xyz_deg_to_quat(rx,ry,rz)
            pts.append(Pose(cur['x'],cur['y'],cur['z'], cur['a'],cur['b'],cur['c'], q))
            last = cur
    # 半球对齐（避免 SLERP 走长弧）
    for i in range(1,len(pts)):
        if quat_dot(pts[i-1].q, pts[i].q) < 0.0:
            qq = pts[i].q
            pts[i].q = (-qq[0],-qq[1],-qq[2],-qq[3])
    dlog(f"G-code 总行数={line_cnt}，关键点(G1/G0)={len(pts)}，其中 G1={g1_cnt}")
    return pts

# ------------------ 几何细分（polyline + SLERP） ------------------

def polyline_presample_block(P_xyz: List[Tuple[float,float,float]], Q_quat: List[Tuple[float,float,float,float]]):
    """位置严格在原折线上；姿态逐段 SLERP；返回 [(x,y,z,q), ...]。"""
    out = []
    out.append((P_xyz[0][0],P_xyz[0][1],P_xyz[0][2], Q_quat[0]))
    for i in range(len(P_xyz)-1):
        p0, p1 = P_xyz[i], P_xyz[i+1]
        q0, q1 = Q_quat[i], Q_quat[i+1]
        L = chord_len(p0,p1)
        ang = angle_between_quats_deg(q0,q1)
        n_pos = max(1, int(math.ceil(L / DS_POS)))
        n_ang = max(1, int(math.ceil(ang / DA_DEG)))
        steps = max(n_pos, n_ang)
        # 保险：限制单段最大细分，避免异常小步导致爆点
        if steps > 2000:
            steps = 2000
        for k in range(1, steps+1):
            t = k/steps
            x = p0[0] + (p1[0]-p0[0]) * t
            y = p0[1] + (p1[1]-p0[1]) * t
            z = p0[2] + (p1[2]-p0[2]) * t
            q = quat_slerp(q0, q1, t)
            out.append((x,y,z,q))
    dlog(f"预采样得到点数={len(out)}")
    return out


def poses_quat_to_unwrapped_deg(poses_xyz_q):
    # 将四元数列表转成“展开”的欧拉角（避免 360° 折返），并去除重复点
    out = []
    last_rx = last_ry = last_rz = None
    last_x = last_y = last_z = None
    for x,y,z,q in poses_xyz_q:
        rx,ry,rz = quat_to_euler_xyz_deg(q)
        if last_rx is None:
            rxu,ryu,rzu = rx,ry,rz
        else:
            rxu = unwrap_to_prev(rx, last_rx)
            ryu = unwrap_to_prev(ry, last_ry)
            rzu = unwrap_to_prev(rz, last_rz)
        if last_x is not None:
            dp = math.sqrt((x-last_x)**2 + (y-last_y)**2 + (z-last_z)**2)
            da = max(abs(rxu-last_rx), abs(ryu-last_ry), abs(rzu-last_rz))
            if dp < EPS_POS and da < EPS_ANG:
                continue
        out.append((x,y,z, rxu,ryu,rzu))
        last_rx,last_ry,last_rz = rxu,ryu,rzu
        last_x,last_y,last_z = x,y,z
    dlog(f"展开欧拉角后点数={len(out)}（去重后）")
    return out

# -------------------- 时间标定（弧长/角速/曲率） --------------------

def time_scale_polyline(P_xyz, Q_quat):
    """
    对 polyline 做速度上限评估和前后向限幅，输出：
      - v_node: 每个采样点的目标速度（mm/s）
      - pct_run: 合并后的 set_speed 档位区间 [(start_idx, end_idx, pct), ...]
    速度上限来源：
      v_max_i = min( V_LIN_MAX, V_ANG_MAX * L_i / max(TH_i,eps), sqrt(A_LAT_MAX/κ) )
    然后做“前向/后向”加速度限幅，保持端点非零末速。
    """
    n = len(P_xyz)
    if n < 2:
        return [0.0], [(0,0,G1_PCT_MIN)]

    # 段长 & 段间姿态角差
    L = [chord_len(P_xyz[i], P_xyz[i+1]) for i in range(n-1)]
    TH= [angle_between_quats_deg(Q_quat[i], Q_quat[i+1]) for i in range(n-1)]

    # 顶点曲率（限速）
    kappa = [0.0]*n
    for i in range(1, n-1):
        kappa[i] = discrete_curvature(P_xyz[i-1], P_xyz[i], P_xyz[i+1])
    v_curve_seg = []
    for i in range(n-1):
        k = max(kappa[i], kappa[i+1])
        v_c = math.sqrt(A_LAT_MAX / max(k, 1e-9)) if k>1e-9 else float('inf')
        v_curve_seg.append(v_c)

    # 段内局部上限（取决于线速/角速/曲率）
    v_local = []
    for i in range(n-1):
        v_lin = V_LIN_MAX
        v_ang = V_ANG_MAX * (L[i] / max(TH[i], 1e-6))  # dθ/ds 约束
        v_local.append(min(v_lin, v_ang, v_curve_seg[i]))

    # 节点速度（前后向限幅）
    v_node = [0.0]*n
    v_node[0] = max(V_END_RATIO * v_local[0], 1e-6)
    for i in range(1, n-1):
        # 前向：受加速度 A_EQ_MAX 限制
        v_node[i] = min(v_local[i-1], v_local[i])
    v_node[-1] = max(V_END_RATIO * v_local[-1], 1e-6)

    # 前向扫：限制加速（v^2 - v_prev^2 ≤ 2*a*L）
    for i in range(1, n):
        dv2 = v_node[i-1]*v_node[i-1] + 2*A_EQ_MAX*L[i-1]
        v_node[i] = min(v_node[i], math.sqrt(dv2))

    # 后向扫：限制减速
    for i in range(n-2, -1, -1):
        dv2 = v_node[i+1]*v_node[i+1] + 2*A_EQ_MAX*L[i]
        v_node[i] = min(v_node[i], math.sqrt(dv2))

    # 将 v 映射成 set_speed 百分比挡位，并合并区间
    def v_to_pct(v):
        if not math.isfinite(v) or v <= 0: return G1_PCT_MIN
        # 简化：线性映射到 [G1_PCT_MIN, G1_PCT_MAX]，并四舍五入到档位
        # 也可改为基于 v/V_LIN_MAX 的归一化
        t = clamp(v / V_LIN_MAX, 0.0, 1.0)
        pct = G1_PCT_MIN + t * (G1_PCT_MAX - G1_PCT_MIN)
        # 挡位量化
        step = max(1, G1_PCT_STEP)
        pct = int(round(pct / step)) * step
        return int(clamp(pct, G1_PCT_MIN, G1_PCT_MAX))

    pct_list = [v_to_pct(v) for v in v_node]

    # 合并相邻同挡位区间（减少 set_speed 调用次数）
    runs = []
    s = 0
    cur = pct_list[0]
    for i in range(1, n):
        if pct_list[i] != cur:
            runs.append((s, i-1, cur))
            s = i
            cur = pct_list[i]
    runs.append((s, n-1, cur))

    # 调试摘要
    if DEBUG:
        uniq = sorted(set(pct_list))
        dlog(f"速度run数={len(runs)}，挡位种类={uniq}，首末速={pct_list[0]}/{pct_list[-1]}")
    return v_node, runs

""" ---------------------- 控制器封装 ---------------------- """

class RobotController:
    """对 nrc 控制器做最小封装：连接、初始化、设置速度、下发点位、容错处理。"""

    def __init__(self, ip=None, port=None):
        self.ip = ip if ip is not None else ROBOT_IP
        self.port = port if port is not None else ROBOT_PORT
        self.socket_fd = -1
        self.is_connected = False
        self.last_xyz = None
        self.last_rxyz = None

    def connect(self) -> bool:
        print(f"🔗 连接 {self.ip}:{self.port} ...")
        self.socket_fd = nrc.connect_robot(self.ip, str(self.port))
        self.is_connected = self.socket_fd > 0
        print("✅ 已连接" if self.is_connected else "❌ 连接失败")
        return self.is_connected

    def disconnect(self):
        if self.is_connected:
            try:
                nrc.disconnect_robot(self.socket_fd)
            finally:
                self.is_connected = False
                self.socket_fd = -1

    def read_current_pose(self):
        """读取当前TCP位姿 (x,y,z, rx_deg,ry_deg,rz_deg)。失败返回 None。"""
        try:
            res = nrc.get_current_position(self.socket_fd, 3, 0)
            if isinstance(res, (list, tuple)) and len(res) >= 2:
                v = res[1]
                x, y, z, rx, ry, rz = (
                    float(v[0]), float(v[1]), float(v[2]),
                    float(v[3]), float(v[4]), float(v[5])
                )
                return (
                    x, y, z,
                    rx * 180.0 / 3.141592653589793,
                    ry * 180.0 / 3.141592653589793,
                    rz * 180.0 / 3.141592653589793,
                )
        except Exception:
            pass
        return None

    def read_runtime_speed(self):
        """读取实时速度：优先控制器 API；失败回退为速度档位。
        返回 (line_mm_s, joint_deg_s_list, pct_override)。"""
        line_v = None
        joints = None
        pct = None
        # 1) 档位百分比
        try:
            res = nrc.get_speed(self.socket_fd, 0)
            if isinstance(res, (list, tuple)) and len(res) >= 2:
                pct = int(res[1])
        except Exception:
            pass
        # 2) 线速度/关节速度
        for name in [
            "get_curretn_line_speed_and_joint_speed",
            "get_curretn_line_speed_and_joint_speed_robot",
            "get_curretn_motor_speed",
            "get_curretn_motor_speed_robot",
        ]:
            fn = getattr(nrc, name, None)
            if not fn:
                continue
            try:
                for args in ((self.socket_fd, 0, 0, 0), (self.socket_fd,), (self.socket_fd, 0)):
                    try:
                        val = fn(*args)
                    except TypeError:
                        continue
                    except Exception:
                        val = None
                    if not isinstance(val, (list, tuple)) or len(val) < 2:
                        continue
                    def to_float(x):
                        try:
                            return float(x)
                        except Exception:
                            try:
                                return float(x[0]) if hasattr(x, "__getitem__") else None
                            except Exception:
                                return None
                    if name.startswith("get_curretn_line_speed"):
                        line_v = to_float(val[1])
                        try:
                            js = val[2]
                            if hasattr(js, "__len__") and not isinstance(js, (str, bytes)):
                                joints = [to_float(j) for j in js]
                        except Exception:
                            pass
                    else:
                        maybe = to_float(val[1])
                        line_v = line_v or maybe
                    break
                if (line_v is not None) or (joints is not None):
                    break
            except Exception:
                continue
        return line_v, joints, pct

    def _power_on_if_needed(self) -> bool:
        try:
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            cur = res[1] if isinstance(res, list) else -1
            dlog(f"servo_state={cur}")
            if cur == 3:
                return True  # 3=POWERON
            nrc.set_current_mode(self.socket_fd, 0)  # TEACH
            time.sleep(0.1)
            nrc.clear_error(self.socket_fd)
            nrc.set_servo_state(self.socket_fd, 1)  # READY
            time.sleep(1.0)
            if nrc.set_servo_poweron(self.socket_fd) != 0:
                return False
            time.sleep(1.0)
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            cur = res[1] if isinstance(res, list) else -1
            dlog(f"servo_state_after_poweron={cur}")
            return isinstance(res, list) and res[1] == 3
        except Exception as ex:
            dlog(f"_power_on_if_needed 异常: {ex}")
            return False

    def initialize(self) -> bool:
        if not self.is_connected:
            return False
        if not self._power_on_if_needed():
            return False
        if nrc.set_current_mode(self.socket_fd, 2) != 0:  # RUN
            print("❌ 切到运行模式失败")
            return False
        nrc.queue_motion_clear_Data(self.socket_fd)
        if nrc.queue_motion_set_status(self.socket_fd, True) != 0:
            print("❌ 启用队列失败")
            return False
        if nrc.set_user_coord_number(self.socket_fd, USER_COORD_NUMBER) != 0:
            print("❌ 设置用户坐标失败")
            return False
        print("✅ 初始化完毕（运行模式/队列开启）")
        return True

    def power_off(self):
        try:
            nrc.queue_motion_stop(self.socket_fd)
            time.sleep(0.2)
            nrc.set_servo_poweroff(self.socket_fd)
        except Exception:
            pass

    def get_queue_length(self) -> int:
        res = nrc.queue_motion_get_queuelen(self.socket_fd, 0)
        q = res[1] if isinstance(res, list) and res[0] == 0 else -1
        if DEBUG and q >= 0:
            dlog(f"控制器队列长度={q}")
        return q

    def read_servo_state(self) -> int:
        try:
            res = nrc.get_servo_state(self.socket_fd, 0)
            return res[1] if isinstance(res, list) else -1
        except Exception:
            return -1

    def try_fetch_alarm(self):
        cand = [
            "get_alarm_code", "get_fault_code", "get_last_alarm", "get_last_error",
            "get_error_code", "get_servo_alarm", "get_alarm",
        ]
        for name in cand:
            fn = getattr(nrc, name, None)
            if not fn:
                continue
            for args in ((self.socket_fd,), (self.socket_fd, 0), ()):  # 宽松尝试不同签名
                try:
                    val = fn(*args)
                    return name, val
                except Exception:
                    continue
        return None, None

    def set_speed(self, percent: int) -> bool:
        ok = nrc.set_speed(self.socket_fd, percent) == 0
        dlog(f"set_speed({percent}) -> {ok}")
        return ok

    def add_moveL(self, x, y, z, rx_deg, ry_deg, rz_deg):
        rx = deg2rad(rx_deg)
        ry = deg2rad(ry_deg)
        rz = deg2rad(rz_deg)
        cmd = nrc.MoveCmd()
        cmd.targetPosType = 0
        cmd.targetPosValue = nrc.VectorDouble([x, y, z, rx, ry, rz])
        cmd.coord = 3
        cmd.userNum = USER_COORD_NUMBER
        cmd.velocity = 100
        cmd.acc = ACCEL_PCT
        cmd.dec = ACCEL_PCT
        cmd.pl = PL_SMOOTH
        r = nrc.queue_motion_push_back_moveL(self.socket_fd, cmd)
        if r != 0:
            raise RuntimeError(f"push_back 失败: {r}")
        self.last_xyz = (x, y, z)
        self.last_rxyz = (rx_deg, ry_deg, rz_deg)

    def wait_inflight_below(self, high: int = HIGH_WATER, low: int = LOW_WATER, poll: float = 0.02, echo_every: int = 25):
        tries = 0
        while True:
            q = self.get_queue_length()
            if q < 0:
                time.sleep(poll)
                return
            if q <= low:
                return
            if q >= high:
                if tries % echo_every == 0:
                    print(f"⏳ 控制器队列={q}（高水位={high}），等待降到 {low} 再继续…")
                time.sleep(poll)
                tries += 1

    def flush(self, n: int) -> bool:
        if n <= 0:
            return True
        t0 = time.perf_counter()
        ok = nrc.queue_motion_send_to_controller(self.socket_fd, n) == 0
        dt = (time.perf_counter() - t0) * 1000.0
        dlog(f"flush {n} 条 -> {ok}，用时 {dt:.1f} ms")
        return ok

    def recover_then_low_speed(self) -> bool:
        try:
            print("⚠️ 出现错误，尝试低速恢复 …")
            nrc.queue_motion_stop(self.socket_fd)
            time.sleep(0.2)
            nrc.clear_error(self.socket_fd)
            time.sleep(1.0)
            nrc.set_servo_state(self.socket_fd, 1)
            time.sleep(1.0)
            ok = nrc.set_servo_poweron(self.socket_fd) == 0
            if ok:
                self.set_speed(ERROR_RETRY_SLOW_PCT)
            return ok
        except Exception as ex:
            dlog(f"recover_then_low_speed 异常: {ex}")
            return False

# ------------------------ 主流程 ------------------------

def build_robot():
    """构造控制器实例：兼容两种构造风格 RobotController(ip,port)/RobotController()"""
    try:
        return RobotController(ROBOT_IP, str(ROBOT_PORT))
    except TypeError:
        rb = RobotController()
        # 若类内部未设置 ip/port，这里兜底
        if not hasattr(rb, 'ip') or rb.ip in (None, ''):
            rb.ip = ROBOT_IP
        if not hasattr(rb, 'port') or rb.port in (None, ''):
            rb.port = str(ROBOT_PORT)
        return rb

def run(gcode_path: str):
    # 由于函数内会临时修改采样步长，这里提前声明使用全局变量，避免“先用后声明”导致的语法错误
    global DS_POS, DA_DEG
    dlog(f"读取 G-code: {gcode_path}")

    # 1) 解析关键点（只取 G1 为姿态关键帧）
    keyposes = load_poses(gcode_path)
    if len(keyposes) < 2:
        print("关键点不足，退出。"); return
    dlog(f"关键点数量={len(keyposes)}")

    # 2) 构造 polyline & SLERP 采样
    P_xyz = [(p.x,p.y,p.z) for p in keyposes]
    Q_quat= [p.q for p in keyposes]
    samples_xyz_q = polyline_presample_block(P_xyz, Q_quat)
    # 若采样点过多，自动放大步长，避免一次性生成几十万点导致控制器拥堵
    if len(samples_xyz_q) > MAX_POINTS:
        scale = max(2.0, len(samples_xyz_q) / MAX_POINTS)
        new_ds = DS_POS * scale
        new_da = DA_DEG * scale
        print(f"⚠️ 采样点过多({len(samples_xyz_q)})，自动放大步长：DS_POS {DS_POS}→{new_ds:.2f} mm, DA_DEG {DA_DEG}→{new_da:.2f} °")
        # 临时放大后重新采样（用 try/finally 确保复原）
        old_ds, old_da = DS_POS, DA_DEG
        try:
            DS_POS, DA_DEG = new_ds, new_da
            samples_xyz_q = polyline_presample_block(P_xyz, Q_quat)
        finally:
            DS_POS, DA_DEG = old_ds, old_da
    samples_xyz_r = poses_quat_to_unwrapped_deg(samples_xyz_q)
    dlog(f"最终下发的采样点数={len(samples_xyz_r)}")

    # 3) 时间标定与档位区间
    t0 = time.perf_counter()
    _, runs = time_scale_polyline(
        P_xyz=[(x,y,z) for (x,y,z,_,_,_) in samples_xyz_r],
        Q_quat=[euler_xyz_deg_to_quat(rx,ry,rz) for (_,_,_,rx,ry,rz) in samples_xyz_r]
    )
    dlog(f"时间标定完成，用时 {(time.perf_counter()-t0)*1000:.1f} ms，run 数={len(runs)}")

    # 4) 连接/初始化控制器
    robot = build_robot()
    if not robot.connect():
        print("连接失败"); return
    if not robot.initialize():
        print("初始化失败"); return

    # 5) 分档位下发（每个 run 前设置一次 set_speed），批量 flush，带容错
    total = len(samples_xyz_r)
    idx = 0
    batch = 0  # 当前尚未下发到控制器的缓存条数
    last_status_ms = 0  # 上次状态行刷新时间
    # 初始化轨迹跟踪 CSV 日志
    trace_filename = f"{os.path.splitext(os.path.basename(gcode_path))[0]}_trace_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    trace = TraceLogger(trace_filename)
    try:
        for (s, e, pct) in runs:
            # 安全：合并区间的边界落在采样索引上
            s = clamp(s, 0, total-1); e = clamp(e, 0, total-1)
            s = int(s); e = int(e)
            if s>e: continue

            # 切换速度挡位（尽量减少切换频率，避免突兀）
            if LOG_RUN_HEAD:
                dlog(f"▶ 进入速度挡位 {pct}%：索引 {s}..{e}（{e-s+1} 点）")
            if not robot.set_speed(int(pct)):
                print(f"⚠️ set_speed({pct}) 失败，尝试恢复 …")
                if not robot.recover_then_low_speed():
                    # 二次失败：人工决策
                    if not wait_user_decision_and_lift(robot):
                        return

            # 逐点入队，分批 flush
            for i in range(s, e+1):
                x,y,z, rx,ry,rz = samples_xyz_r[i]
                try:
                    robot.add_moveL(x,y,z, rx,ry,rz)
                    idx += 1
                    batch += 1
                except Exception as ex:
                    print(f"⚠️ push_back 异常：{ex}，尝试低速重试 …")
                    if robot.recover_then_low_speed():
                        try:
                            robot.add_moveL(x,y,z, rx,ry,rz)
                            idx += 1
                            batch += 1
                        except Exception:
                            # 再失败则人工决策
                            if not wait_user_decision_and_lift(robot):
                                return
                    else:
                        if not wait_user_decision_and_lift(robot):
                            return

                # 每隔 N 个点打印一次具体点位，便于复现问题
                if LOG_POINT_EVERY > 0 and (idx % LOG_POINT_EVERY == 0 or i == e):
                    dlog(f"点#{idx}: XYZ=({x:.2f},{y:.2f},{z:.2f}) RXYZ=({rx:.2f},{ry:.2f},{rz:.2f}) 当前run速度={pct}%")
                # 实时状态行（限频刷一行，不污染控制台）
                if STATUS_EVERY_MS > 0:
                    now_ms = int((time.perf_counter()-START_T)*1000)
                    if now_ms - last_status_ms >= STATUS_EVERY_MS:
                        q_now = robot.get_queue_length()
                        ss = robot.read_servo_state()
                        line_v, joints, pct_read = robot.read_runtime_speed()
                        vtxt = f"{line_v:.1f} mm/s" if isinstance(line_v, (int,float)) and line_v is not None else "-"
                        line_v, joints, pct_read = robot.read_runtime_speed()
                        cur_pose = robot.read_current_pose()
                        # 取当前下发目标点（近似）：samples_xyz_r[idx] 若越界则夹紧
                        tgt_idx = max(0, min(idx if isinstance(idx,int) else int(idx), total-1))
                        target_pose = samples_xyz_r[tgt_idx]
                        vtxt = f"{line_v:.1f} mm/s" if isinstance(line_v,(int,float)) and line_v is not None else "-"
                        print(f"▶ 进度 {idx}/{total}  队列={q_now}  伺服={ss}  当前run={pct}%  实测线速 {vtxt}", end="", flush=True)
                        # 同步写入 CSV 日志
                        trace.log(time.perf_counter(), idx, total, pct, q_now, ss, line_v, pct_read, cur_pose, target_pose)
                        last_status_ms = now_ms

                # 队列打包发送（按批量，或到 run 尾部把余量也发掉）
                if (batch >= BATCH_SEND) or (i == e):
                    n_to_flush = batch
                    if n_to_flush > 0:
                        # 发送前先看控制器水位，必要时等待
                        robot.wait_inflight_below(high=HIGH_WATER, low=LOW_WATER)
                        q_before = robot.get_queue_length()
                        if not robot.flush(n_to_flush):
                            print("⚠️ flush 失败，尝试低速恢复 …")
                            if not robot.recover_then_low_speed():
                                if not wait_user_decision_and_lift(robot):
                                    return
                        else:
                            q_after = robot.get_queue_length()
                            if DEBUG:
                                q_after = robot.get_queue_length()
                                dlog(f"批量发送成功：{n_to_flush} 条（累计 {idx}），当前队列={q_after}")
                        batch = 0

        # 最后一波 flush（若有残留）
        if batch:
            q_before = robot.get_queue_length()
            if robot.flush(batch):
                q_after = robot.get_queue_length()
                if DEBUG:
                    dlog(f"尾批发送成功：{batch} 条（累计 {idx}），当前队列={q_after}")
            else:
                print("⚠️ 尾批 flush 失败，尝试低速恢复 …")
                if not robot.recover_then_low_speed():
                    if not wait_user_decision_and_lift(robot):
                        return
        print("✅ 路径已全部下发。")
        if DISCONNECT_AT_END:
            robot.wait_inflight_below(high=HIGH_WATER, low=LOW_WATER)
    finally:
        try:
            trace.close()
        except Exception:
            pass
        if DISCONNECT_AT_END:
            # 不主动 poweroff；交由上层决定
            robot.disconnect()
        dlog("run() 结束")


def wait_user_decision_and_lift(robot: RobotController) -> bool:
    """二次失败后的自动处理：根据 AUTO_SECOND_FAIL 决策；不再等待人工输入。"""
    try:
        if AUTO_SECOND_FAIL == "continue":
            if robot.last_xyz and robot.last_rxyz:
                x,y,z = robot.last_xyz
                rx,ry,rz = robot.last_rxyz
                try:
                    robot.set_speed(ERROR_RETRY_SLOW_PCT)
                    robot.add_moveL(x, y, z + LIFT_MM, rx, ry, rz)
                    robot.flush(1)
                    print(f"⬆️ 已自动抬高 {LIFT_MM} mm，继续执行 …")
                except Exception as ex:
                    print(f"⚠️ 自动抬高失败：{ex}，仍继续执行 …")
            else:
                print("ℹ️ 无上一次位姿缓存，自动继续执行 …")
            return True
        else:
            print("⏸️ 已自动选择停止（AUTO_SECOND_FAIL=stop）。")
            return False
    except Exception as ex:
        dlog(f"wait_user_decision_and_lift 异常: {ex}")
        return False


# ------------------------ 入口 ------------------------

# ------------------------ 入口 ------------------------

if __name__ == "__main__":
    path = GCODE_FILE if len(sys.argv) < 2 else sys.argv[1]
    if not os.path.isfile(path):
        print(f"找不到 G-code 文件：{path}")
        sys.exit(1)
    dlog("程序开始运行 …")
    
    # 诊断：打印 RobotController.__init__ 签名
    import inspect
    try:
        sig = str(inspect.signature(RobotController.__init__))
    except Exception:
        sig = "<unavailable>"
    print(f"[DIAG] RobotController.__init__ signature: {sig}")
    run(path)
    dlog("程序正常结束。")