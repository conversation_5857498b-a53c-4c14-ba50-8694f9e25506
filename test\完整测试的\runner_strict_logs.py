# -*- coding: utf-8 -*-
"""
严格日志包装器 v5（只加强日志与故障可见性）：
- 抓 “故障发生时的 P 索引 + 最近3条运动指令 + 报警/伺服/队列 + 伺服历史”。
- 不改动你的运动/插补/限速逻辑（KISS / YAGNI）。
"""

import sys, time, re, importlib.util, argparse, collections, inspect
from pathlib import Path
from types import ModuleType
from typing import Optional

# —— 你的原始控制脚本路径（确认无误；r"" 防转义）——
ORIG_FILE = Path(r"C:\Users\<USER>\Desktop\INEXBOT_3DP2\test\kiss_yagni_solid_arm_runner.py")
DEFAULT_GCODE = "jiyi copy 2.Gcode"
NOMINAL_SERVO_STATES = {3}  # 3=正常；2/0 视为异常

# 运行期上下文：最近进度、最近指令、伺服历史
LAST_PROGRESS = {"idx": None, "total": None, "run": None, "ts": None}
RECENT_CMDS = collections.deque(maxlen=8)      # [(ts, func, args, kwargs)]
SERVO_HISTORY = collections.deque(maxlen=20)   # [(ts, state)]

# =============== 故障门控（抑制噪音 + 强化可见） ===============
FAULT_ACTIVE = False
FAULT_CTX = {
    "where": None,
    "alarm": None,
    "servo": None,
    "queue": None,
    "reason": None,
    "last_cmd": None,
    "ts": None,
    "p_idx": None,
    "p_total": None,
}

def _probe_alarm(robot):
    """尽可能探测报警：遍历零参数的 *alarm*/*fault*/*error* 方法。"""
    names = [n for n in dir(robot) if any(k in n.lower() for k in ("alarm","fault","error"))]
    for n in sorted(set(names)):
        try:
            attr = getattr(robot, n)
            if callable(attr):
                sig = inspect.signature(attr)
                if all(p.default is not inspect._empty or p.kind in (p.VAR_KEYWORD, p.VAR_POSITIONAL)
                       for p in sig.parameters.values()) or len(sig.parameters)==0:
                    try:
                        res = attr()
                        if res is not None:
                            return f"{n}", res
                    except Exception:
                        pass
        except Exception:
            pass
    return None, None

def _fault_on(robot, where: str, reason: str = "", ex: Exception = None):
    """进入故障态：记录上下文并打印摘要行（仅一次）。"""
    global FAULT_ACTIVE, FAULT_CTX
    if FAULT_ACTIVE:
        return
    FAULT_ACTIVE = True

    # 读取报警/伺服/队列
    aname, aval = (None, None)
    try:
        aname, aval = _probe_alarm(robot)
    except Exception:
        pass
    if not aname:
        try:
            aname, aval = robot.try_fetch_alarm()
        except Exception:
            pass
    try:
        servo = robot.read_servo_state()
    except Exception:
        servo = None
    try:
        qlen  = robot.get_queue_length()
    except Exception:
        qlen = None

    last_cmd = None
    if RECENT_CMDS:
        t, fn, args, kwargs = RECENT_CMDS[-1]
        last_cmd = f"{fn}{args}{kwargs}"

    FAULT_CTX = {
        "where": where,
        "alarm": (f"{aname}={aval}" if aname else "未提供"),
        "servo": servo,
        "queue": qlen,
        "reason": (str(reason) if reason else (str(ex) if ex else "")),
        "last_cmd": last_cmd,
        "ts": time.strftime("%H:%M:%S"),
        "p_idx": LAST_PROGRESS["idx"],
        "p_total": LAST_PROGRESS["total"],
    }

    # 主摘要
    print(
        f"⛔ 故障：来源={where}  P={FAULT_CTX['p_idx']}/{FAULT_CTX['p_total']}  "
        f"原因={FAULT_CTX['reason']}  报警={FAULT_CTX['alarm']}  "
        f"伺服={FAULT_CTX['servo']}  队列={FAULT_CTX['queue']}"
    )

    # 最近 3 条运动指令
    if RECENT_CMDS:
        k = min(3, len(RECENT_CMDS))
        for i in range(1, k+1):
            t, fn, args, kwargs = RECENT_CMDS[-i]
            print(f"  • 最近指令[-{i}] @{time.strftime('%H:%M:%S', time.localtime(t))}: {fn}{args}{kwargs}")

    # 伺服历史
    if SERVO_HISTORY:
        trail = ", ".join(f"{st}@{time.strftime('%H:%M:%S', time.localtime(ts))}" for ts, st in list(SERVO_HISTORY)[-6:])
        print(f"  • 伺服历史(最近): {trail}")

def _fault_off(robot=None):
    """退出故障态：仅当伺服恢复正常时才真正退出。"""
    global FAULT_ACTIVE, FAULT_CTX
    if robot is not None:
        try:
            ss = robot.read_servo_state()
            if ss not in NOMINAL_SERVO_STATES:
                return
        except Exception:
            return
    if FAULT_ACTIVE:
        print("✅ 故障已恢复，继续执行。")
    FAULT_ACTIVE = False
    for k in FAULT_CTX:
        FAULT_CTX[k] = None

# =============== 加载原脚本 ===============
def _load_orig(orig_path: Path) -> ModuleType:
    if not orig_path.exists():
        raise FileNotFoundError(f"原始控制脚本不存在：{orig_path}")
    spec = importlib.util.spec_from_file_location("orig_runner", str(orig_path))
    if spec is None or spec.loader is None:
        raise RuntimeError(f"无法加载原始脚本：{orig_path}")
    mod = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mod)
    return mod

# =============== 打补丁（最小侵入） ===============
def _install_patches(orig: ModuleType):
    global FAULT_ACTIVE

    # 1) 覆写 dlog：故障态下静默
    _old_dlog = getattr(orig, "dlog", lambda msg: None)
    def dlog_patched(msg: str):
        if not FAULT_ACTIVE:
            _old_dlog(msg)
    setattr(orig, "dlog", dlog_patched)

    # 2) 包裹 RobotController 关键方法
    RC = orig.RobotController

    # 通用装饰器：记录最近指令
    def _record_call(fn_name, fn):
        def wrap(self, *args, **kwargs):
            try:
                RECENT_CMDS.append((time.time(), fn_name, args, kwargs))
                self._last_motion_cmd = f"{fn_name}{args}{kwargs}"
            except Exception:
                pass
            return fn(self, *args, **kwargs)
        return wrap

    # 2.1 flush
    if hasattr(RC, "flush"):
        _old_flush = RC.flush
        def _flush(self, n: int):
            ok = _old_flush(self, n)
            if ok:
                try:
                    ss = self.read_servo_state()
                except Exception:
                    ss = None
                if ss in NOMINAL_SERVO_STATES:
                    _fault_off(self)
                else:
                    if not FAULT_ACTIVE:
                        _fault_on(self, "flush", f"flush ok 但伺服异常={ss}")
            else:
                _fault_on(self, "flush", "queue_motion_send_to_controller 返回非 0")
            return ok
        RC.flush = _flush

    # 2.2 set_speed
    if hasattr(RC, "set_speed"):
        _old_set_speed = RC.set_speed
        def _set_speed(self, *args, **kwargs):
            ok = _old_set_speed(self, *args, **kwargs)
            if not ok:
                _fault_on(self, "set_speed", "设置速度失败")
            return ok
        RC.set_speed = _set_speed

    # 2.3 push_back 及所有 push/enqueue 家族方法：统一记录
    for name in dir(RC):
        if not any(name.startswith(p) for p in ("push", "enqueue")) and ("push_back" not in name):
            continue
        if not callable(getattr(RC, name)):
            continue
        if name in ("flush", "set_speed", "read_servo_state"):
            continue
        fn_old = getattr(RC, name)
        setattr(RC, name, _record_call(name, fn_old))

    # 2.4 read_servo_state：记录伺服历史；异常即刻进故障
    if hasattr(RC, "read_servo_state"):
        _old_read_servo_state = RC.read_servo_state
        def _read_servo_state(self, *args, **kwargs):
            ss = _old_read_servo_state(self, *args, **kwargs)
            try:
                SERVO_HISTORY.append((time.time(), ss))
            except Exception:
                pass
            if ss not in NOMINAL_SERVO_STATES:
                _fault_on(self, "servo_state", f"伺服异常={ss}")
            return ss
        RC.read_servo_state = _read_servo_state

    # 3) 包裹 stdout：先解析“进度行”，再按需抑制噪音
    real_write = sys.stdout.write
    progress_re = re.compile(r"^▶\s*进度\s+(\d+)\s*/\s*(\d+).*?当前run=([0-9]+)%")
    noisy_prefix = ("▶ 进度", "批量发送成功", "尾批发送成功", "[")
    def write_patched(s: str):
        # 捕获进度（即便待会儿抑制，也先解析出来）
        line = s.strip()
        m = progress_re.match(line)
        if m:
            try:
                LAST_PROGRESS.update({
                    "idx": int(m.group(1)),
                    "total": int(m.group(2)),
                    "run": int(m.group(3)),
                    "ts": time.time(),
                })
            except Exception:
                pass

        if FAULT_ACTIVE:
            low = line.lower()
            if (
                any(line.startswith(p) for p in noisy_prefix) or
                ("sending" in low) or ("发送" in line) or
                ("路径已全部下发" in line)
            ):
                return 0  # 丢弃噪音
        return real_write(s)
    sys.stdout.write = write_patched  # type: ignore

# =============== 对外入口 ===============
def run(gcode_path: Optional[str] = None, orig_path: Optional[Path] = None):
    orig_path = (orig_path or ORIG_FILE)
    orig = _load_orig(orig_path)
    _install_patches(orig)
    g = gcode_path or DEFAULT_GCODE
    return orig.run(g)

# =============== CLI ===============
def main():
    parser = argparse.ArgumentParser(description="严格日志包装器（故障可见、无冗余日志）")
    parser.add_argument("gcode", nargs="?", default=None, help="GCode 文件路径（可选）")
    args = parser.parse_args()
    run(args.gcode)

if __name__ == "__main__":
    main()
