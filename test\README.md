# 机器人控制器测试程序

## 🎯 核心程序

### 1. 标准运行器
- `kiss_yagni_solid_arm_runner.py` - 主要运行器（已优化参数）
- `runner_strict_logs.py` - 带详细日志的运行器

### 2. 增强运行器
- `enhanced_runner.py` - 集成故障解决和诊断功能的完整版本

## 🚀 使用方法

### 基础使用（推荐）
```bash
# 使用已优化的标准版本
python test/kiss_yagni_solid_arm_runner.py "jiyi copy.Gcode"

# 使用带日志的版本
python test/runner_strict_logs.py "jiyi copy.Gcode"
```

### 增强功能
```bash
# 使用增强版本（包含诊断和故障恢复）
python test/enhanced_runner.py "jiyi copy.Gcode"

# 显示统计信息
python test/enhanced_runner.py "jiyi copy.Gcode" --stats
```

## ⚡ 关键优化参数

已验证有效的参数配置：
```python
BATCH_SEND = 5        # 批次大小：5个点
HIGH_WATER = 20       # 队列高水位：20
LOW_WATER = 5         # 队列低水位：5
```

**效果**：完全解决间歇性伺服故障，14205个点稳定执行。

## 🔍 增强功能

`enhanced_runner.py` 包含：
- ✅ 实时伺服状态监控
- ✅ 队列状态追踪
- ✅ 故障上下文捕获
- ✅ 增强故障恢复
- ✅ 速度和位置诊断
- ✅ 统计信息收集

## 📊 故障解决方案

**问题**：间歇性伺服故障（状态3→2）
**原因**：批次过大 + 队列水位过高
**解决**：小批次(5) + 低水位(20/5)
**结果**：完全消除故障

## 🧪 其他测试程序

- `move_x_test.py` - 单轴移动测试
- `power_test.py` - 上电测试
- `test.py` / `test1.py` - 基础功能测试
- `full_test.py` - 完整功能测试

## 📁 历史版本

`完整测试的/` 目录包含历史版本的备份。
