# -*- coding: utf-8 -*-
"""
增强运行器 - 合并所有故障解决和诊断功能
解决间歇性伺服故障的完整解决方案
"""

import sys
import time
import threading
from pathlib import Path
from collections import deque
from typing import Dict, List, Any, Optional

class EnhancedDiagnostics:
    """增强诊断系统"""
    
    def __init__(self, robot_controller):
        self.robot = robot_controller
        self.socket_fd = robot_controller.socket_fd
        
        # 监控数据
        self.servo_history = deque(maxlen=20)
        self.queue_history = deque(maxlen=50)
        self.alarm_buffer = deque(maxlen=20)
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread = None
        
        # 统计
        self.stats = {
            'total_alarms': 0,
            'servo_changes': 0,
            'queue_clears': 0,
            'speed_violations': 0
        }
        
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring_active:
            return
            
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print("🔍 增强诊断监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        print("🔍 增强诊断监控已停止")
        
    def _monitor_loop(self):
        """监控主循环"""
        while self.monitoring_active:
            try:
                self._check_states()
                time.sleep(0.1)  # 10Hz监控
            except Exception as e:
                print(f"⚠️ 监控异常: {e}")
                time.sleep(0.5)
                
    def _check_states(self):
        """检查状态"""
        try:
            import nrc_interface as nrc
            
            # 检查伺服状态
            servo_param = 0
            servo_result = nrc.get_servo_state(self.socket_fd, servo_param)
            servo_state = servo_result[1] if isinstance(servo_result, list) and len(servo_result) > 1 else -1
            
            # 检查队列长度
            queue_param = 0
            queue_result = nrc.queue_motion_get_queuelen(self.socket_fd, queue_param)
            queue_length = queue_result[1] if isinstance(queue_result, list) and len(queue_result) > 1 else -1
            
            # 记录历史
            current_time = time.time()
            self.servo_history.append((current_time, servo_state))
            self.queue_history.append((current_time, queue_length))
            
            # 检测变化
            if len(self.servo_history) >= 2:
                prev_servo = self.servo_history[-2][1]
                if servo_state != prev_servo:
                    self.stats['servo_changes'] += 1
                    print(f"📊 伺服状态变化: {prev_servo} -> {servo_state}")
                    
                    if servo_state == 2:  # 报警状态
                        self._capture_fault_context(servo_state, queue_length)
                        
            if len(self.queue_history) >= 2:
                prev_queue = self.queue_history[-2][1]
                if prev_queue > 10 and queue_length == 0:
                    self.stats['queue_clears'] += 1
                    print(f"📦 队列清零事件: {prev_queue} -> 0")
                    
        except Exception as e:
            # 静默处理监控异常，避免刷屏
            pass
            
    def _capture_fault_context(self, servo_state: int, queue_length: int):
        """捕获故障上下文"""
        try:
            import nrc_interface as nrc
            
            print(f"\n🚨 故障诊断 [伺服={servo_state}, 队列={queue_length}]")
            
            # 获取位置信息
            try:
                pos_param = 1
                pos_result = nrc.get_current_position(self.socket_fd, pos_param)
                if isinstance(pos_result, list) and len(pos_result) > 1:
                    position = pos_result[1]
                    print(f"   位置: {[f'{p:.3f}' for p in position[:6]]}")
            except:
                pass
                
            # 获取速度信息
            try:
                speed_param = 0
                speed_result = nrc.get_curretn_line_speed_and_joint_speed(self.socket_fd, speed_param)
                if isinstance(speed_result, list) and len(speed_result) > 1:
                    speed_data = speed_result[1]
                    print(f"   速度数据: {speed_data}")
            except:
                pass
                
            # 显示历史趋势
            if len(self.servo_history) >= 5:
                recent = list(self.servo_history)[-5:]
                print(f"   伺服历史: {[s for _, s in recent]}")
                
            if len(self.queue_history) >= 5:
                recent = list(self.queue_history)[-5:]
                print(f"   队列历史: {[q for _, q in recent]}")
                
        except Exception as e:
            print(f"   ⚠️ 故障上下文捕获异常: {e}")
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'monitoring_active': self.monitoring_active,
            'statistics': self.stats.copy(),
            'recent_servo': list(self.servo_history)[-5:],
            'recent_queue': list(self.queue_history)[-5:]
        }

def create_enhanced_controller():
    """创建增强控制器类"""
    
    # 导入原始控制器
    from kiss_yagni_solid_arm_runner import RobotController
    
    class EnhancedRobotController(RobotController):
        """增强机器人控制器"""
        
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.diagnostics = None
            self.fault_count = 0
            
        def connect(self):
            """增强连接"""
            result = super().connect()
            if result:
                # 启动诊断
                try:
                    self.diagnostics = EnhancedDiagnostics(self)
                    self.diagnostics.start_monitoring()
                    print("✅ 诊断系统已启动")
                except Exception as e:
                    print(f"⚠️ 诊断系统启动失败: {e}")
            return result
            
        def read_servo_state(self):
            """增强伺服状态读取"""
            try:
                state = super().read_servo_state()
                
                # 检测异常状态
                if state == 2:  # 报警状态
                    self.fault_count += 1
                    if self.fault_count <= 3:  # 避免刷屏
                        print(f"🚨 伺服报警检测 (第{self.fault_count}次)")
                        
                return state
            except Exception as e:
                print(f"⚠️ 读取伺服状态异常: {e}")
                return -1
                
        def flush(self, n: int) -> bool:
            """增强flush"""
            # 执行前检查
            servo_state = self.read_servo_state()
            queue_len = self.get_queue_length()
            
            if servo_state != 3:
                print(f"⚠️ flush前伺服异常: {servo_state}")
                
            if queue_len > 15:
                print(f"⚠️ flush前队列过满: {queue_len}, 等待消化...")
                self.wait_inflight_below(high=15, low=5)
                
            # 执行flush
            result = super().flush(n)
            
            # 执行后检查
            if result and n > 3:
                time.sleep(0.05)  # 短暂等待
                final_servo = self.read_servo_state()
                if final_servo != 3:
                    print(f"⚠️ flush后伺服异常: {final_servo}")
                    
            return result
            
        def recover_then_low_speed(self) -> bool:
            """增强故障恢复"""
            print("🔧 启动增强故障恢复...")
            
            try:
                import nrc_interface as nrc
                
                # 记录恢复前状态
                pre_servo = self.read_servo_state()
                pre_queue = self.get_queue_length()
                print(f"   恢复前: 伺服={pre_servo}, 队列={pre_queue}")
                
                # 标准恢复流程
                nrc.queue_motion_stop(self.socket_fd)
                time.sleep(0.3)
                
                nrc.clear_error(self.socket_fd)
                time.sleep(1.0)
                
                nrc.set_servo_state(self.socket_fd, 1)
                time.sleep(1.5)
                
                result = nrc.set_servo_poweron(self.socket_fd)
                time.sleep(2.0)
                
                # 验证恢复
                post_servo = self.read_servo_state()
                print(f"   恢复后: 伺服={post_servo}, 上电结果={result}")
                
                if post_servo == 3:
                    # 设置保守速度
                    self.set_speed(20)
                    print("   ✅ 恢复成功，设置保守速度20%")
                    return True
                else:
                    print(f"   ⚠️ 恢复后状态异常: {post_servo}")
                    return False
                    
            except Exception as e:
                print(f"   ❌ 恢复异常: {e}")
                return False
                
        def __del__(self):
            """析构时停止诊断"""
            if hasattr(self, 'diagnostics') and self.diagnostics:
                try:
                    self.diagnostics.stop_monitoring()
                except:
                    pass
            super().__del__()
            
    return EnhancedRobotController

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="增强机器人运行器")
    parser.add_argument("gcode_file", nargs="?", default="jiyi copy.Gcode", help="G代码文件")
    parser.add_argument("--stats", action="store_true", help="显示统计信息")
    
    args = parser.parse_args()
    
    print("🤖 增强机器人运行器")
    print("🎯 集成故障解决和诊断功能")
    print("=" * 50)
    
    # 检查文件
    gcode_file = args.gcode_file
    if not Path(gcode_file).exists():
        print(f"❌ 文件不存在: {gcode_file}")
        return
    
    # 应用增强功能
    apply_enhancements()
    
    # 执行G代码
    try:
        from kiss_yagni_solid_arm_runner import run
        print(f"📁 执行G代码: {gcode_file}")
        run(gcode_file)
        print("✅ 执行完成")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"❌ 执行异常: {e}")

def apply_enhancements():
    """应用所有增强功能"""
    print("🔧 应用增强功能...")
    
    try:
        import kiss_yagni_solid_arm_runner as runner
        
        # 1. 应用最佳参数（已验证有效）
        runner.BATCH_SEND = 5
        runner.HIGH_WATER = 20
        runner.LOW_WATER = 5
        print(f"   📦 批次大小: {runner.BATCH_SEND}")
        print(f"   🌊 队列水位: {runner.HIGH_WATER}/{runner.LOW_WATER}")
        
        # 2. 替换为增强控制器
        EnhancedController = create_enhanced_controller()
        runner.RobotController = EnhancedController
        print("   🔧 控制器已增强")
        
        print("✅ 增强功能应用完成")
        
    except ImportError as e:
        print(f"   ❌ 应用增强功能失败: {e}")

if __name__ == "__main__":
    main()
