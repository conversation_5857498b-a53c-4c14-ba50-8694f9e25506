========================
CODE SNIPPETS
========================
TITLE: Track Record Start Example
DESCRIPTION: Example of how to start track recording with specified sampling parameters using the track_record_start function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_start(SOCKETFD socketFd, double maxSamplingNum, double samplingInterval);
```

----------------------------------------

TITLE: Example: Two IO Boards
DESCRIPTION: This example demonstrates the setup and potential error messages when dealing with two IO boards, each with 16 input and 16 output bits. It specifically addresses an error scenario for the output port of IO board 1.

SOURCE: https://doc.hmilib.inexbot.coision.cn/examples

LANGUAGE: example
CODE:
```
Example: Two IO Boards

This example demonstrates the setup and potential error messages when dealing with two IO boards, each with 16 input and 16 output bits. It specifically addresses an error scenario for the output port of IO board 1.
```

----------------------------------------

TITLE: C++ Job Open Example
DESCRIPTION: Example of how to use the job_open function in C++ to open a job file.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: C++
CODE:
```
SOCKETFD socketFd;
// Assume socketFd is initialized and connected
std::string jobName = "QQQ.JBR";
Result result = job_open(socketFd, jobName);
// Handle the result
```

----------------------------------------

TITLE: C++ Job Run Example
DESCRIPTION: Example of how to use the job_run function in C++ to execute a job file.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: C++
CODE:
```
SOCKETFD socketFd;
// Assume socketFd is initialized and connected
std::string jobName = "QQQ.JBR";
Result result = job_run(socketFd, jobName);
// Handle the result
```

----------------------------------------

TITLE: net_lib Navigation
DESCRIPTION: Provides navigation links for the net_lib documentation, including sections for classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_q

LANGUAGE: html
CODE:
```
<a href="https://doc.hmilib.inexbot.coision.cn/index.html">net_lib</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/test.html">Test List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/deprecated.html">Deprecated List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/annotated.html">Classes</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">Files</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">File List</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">File Members</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">All</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">Functions</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">b</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_c.html">c</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_d.html">d</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_f.html">f</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_g.html">g</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_j.html">j</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_l.html">l</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_m.html">m</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_q.html">q</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_r.html">r</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_s.html">s</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_t.html">t</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_v.html">v</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_w.html">w</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_vars.html">Variables</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_type.html">Typedefs</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_enum.html">Enumerations</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_eval.html">Enumerator</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_defs.html">Macros</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/examples.html">Examples</a>
```

----------------------------------------

TITLE: net_lib Navigation
DESCRIPTION: Provides navigation links for the net_lib documentation, including sections for classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_w

LANGUAGE: html
CODE:
```
<a href="https://doc.hmilib.inexbot.coision.cn/index.html">net_lib</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/test.html">Test List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/deprecated.html">Deprecated List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/annotated.html">Classes</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">Files</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">File List</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">File Members</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">All</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">Functions</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">b</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_c.html">c</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_d.html">d</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_f.html">f</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_g.html">g</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_j.html">j</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_l.html">l</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_m.html">m</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_q.html">q</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_r.html">r</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_s.html">s</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_t.html">t</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_v.html">v</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_w.html">w</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_vars.html">Variables</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_type.html">Typedefs</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_enum.html">Enumerations</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_eval.html">Enumerator</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_defs.html">Macros</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/examples.html">Examples</a>
```

----------------------------------------

TITLE: Track Record Playback Example
DESCRIPTION: Example of how to initiate track playback with a specified velocity using the track_record_playback function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_playback(SOCKETFD socketFd, int vel);
```

----------------------------------------

TITLE: net_lib Navigation
DESCRIPTION: Provides navigation links for the net_lib documentation, including sections for classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_t

LANGUAGE: html
CODE:
```
<a href="https://doc.hmilib.inexbot.coision.cn/index.html">net_lib</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/test.html">Test List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/deprecated.html">Deprecated List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/annotated.html">Classes</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">Files</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">File List</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">File Members</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">All</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">Functions</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">b</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_c.html">c</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_d.html">d</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_f.html">f</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_g.html">g</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_j.html">j</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_l.html">l</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_m.html">m</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_q.html">q</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_r.html">r</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_s.html">s</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_t.html">t</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_v.html">v</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_w.html">w</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_vars.html">Variables</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_type.html">Typedefs</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_enum.html">Enumerations</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_eval.html">Enumerator</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_defs.html">Macros</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/examples.html">Examples</a>
```

----------------------------------------

TITLE: net_lib Navigation
DESCRIPTION: Provides navigation links for the net_lib documentation, including sections for classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: html
CODE:
```
<a href="https://doc.hmilib.inexbot.coision.cn/index.html">net_lib</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/test.html">Test List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/deprecated.html">Deprecated List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/annotated.html">Classes</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">Files</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">File List</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">File Members</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">All</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">Functions</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">b</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_c.html">c</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_d.html">d</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_f.html">f</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_g.html">g</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_j.html">j</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_l.html">l</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_m.html">m</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_q.html">q</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_r.html">r</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_s.html">s</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_t.html">t</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_v.html">v</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_w.html">w</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_vars.html">Variables</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_type.html">Typedefs</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_enum.html">Enumerations</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_eval.html">Enumerator</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_defs.html">Macros</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/examples.html">Examples</a>
```

----------------------------------------

TITLE: net_lib Navigation
DESCRIPTION: Provides navigation links for the net_lib documentation, including sections for classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_v

LANGUAGE: html
CODE:
```
<a href="https://doc.hmilib.inexbot.coision.cn/index.html">net_lib</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/test.html">Test List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/deprecated.html">Deprecated List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/annotated.html">Classes</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">Files</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">File List</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">File Members</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">All</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">Functions</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">b</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_c.html">c</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_d.html">d</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_f.html">f</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_g.html">g</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_j.html">j</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_l.html">l</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_m.html">m</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_q.html">q</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_r.html">r</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_s.html">s</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_t.html">t</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_v.html">v</a>
          * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func_w.html">w</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_vars.html">Variables</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_type.html">Typedefs</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_enum.html">Enumerations</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_eval.html">Enumerator</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_defs.html">Macros</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/examples.html">Examples</a>
```

----------------------------------------

TITLE: Queue Motion Push Back Vision Craft Start
DESCRIPTION: Initiates a vision craft start operation in the queue. Requires a socket file descriptor and an ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_vision_craft_start(SOCKETFD socketFd, int id);
```

----------------------------------------

TITLE: Navigation - net_lib
DESCRIPTION: Provides navigation links for the net_lib documentation, including tests, deprecated lists, classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_x

LANGUAGE: javascript
CODE:
```
javascript:searchBox.CloseResultsWindow()

```

----------------------------------------

TITLE: Track Record Playback Robot Example
DESCRIPTION: Example of how to initiate track playback for a specific robot with a specified velocity using the track_record_playback_robot function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_playback_robot(SOCKETFD socketFd, int robotNum, int vel);
```

----------------------------------------

TITLE: net_lib Navigation
DESCRIPTION: Provides navigation links for the net_lib documentation, including sections for test lists, deprecated items, classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_vars

LANGUAGE: html
CODE:
```
<a href="https://doc.hmilib.inexbot.coision.cn/index.html">net_lib</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/test.html">Test List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/deprecated.html">Deprecated List</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/annotated.html">Classes</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">Files</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/files.html">File List</a>
      * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">File Members</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals.html">All</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_func.html">Functions</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_vars.html">Variables</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_type.html">Typedefs</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_enum.html">Enumerations</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_eval.html">Enumerator</a>
        * <a href="https://doc.hmilib.inexbot.coision.cn/globals_defs.html">Macros</a>
    * <a href="https://doc.hmilib.inexbot.coision.cn/examples.html">Examples</a>
```

----------------------------------------

TITLE: Remote Parameter Configuration
DESCRIPTION: Functions to set and get remote parameters for a specified robot. These include speed, start status, time, and count, with default values provided for some parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
Result set_remote_param(SOCKETFD socketFd, int robotNum, int speed, bool start, int time, int startTime, int num = 10);
Result get_remote_param(SOCKETFD socketFd, int robotNum, int& speed, bool& start, int& time, int& startTime, int& num);
```

----------------------------------------

TITLE: Remote Parameter Control
DESCRIPTION: Functions to set and get remote parameters for a specific robot. These include speed, start status, time, and start time. They require a socket file descriptor, robot number, and relevant parameter variables.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: cpp
CODE:
```
int set_remote_param(SOCKETFD socketFd, int robotNum, int speed, bool start, int time, int startTime, int num = 10);
int get_remote_param(SOCKETFD socketFd, int robotNum, int &speed, bool &start, int &time, int &startTime, int &num);
```

----------------------------------------

TITLE: Insert Vision Craft Start Instruction
DESCRIPTION: Inserts a VISION_RUN instruction to start a vision craft operation. This function is used to initiate a visual process within the job file.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_start(SOCKETFD socketFd, int line, int id)
  - Inserts a VISION_RUN instruction to start a vision craft.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - line: The line number in the job file.
    - id: The identifier for the vision craft.
```

----------------------------------------

TITLE: Insert Vision Craft Start Operation for Robot
DESCRIPTION: Inserts a VISION_RUN instruction to start a vision process for a robot. This function is used to initiate robot-controlled vision tasks. It requires a socket file descriptor, robot number, line number, and a craft ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_start_robot(_socketFd, _robotNum, _line, _id)

Parameters:
  _socketFd: SOCKETFD - File descriptor for the socket connection.
  _robotNum: int - The robot number.
  _line: int - The line number where the job instruction will be inserted.
  _id: int - The craft ID.
```

----------------------------------------

TITLE: Insert Robot Vision Craft Start Instruction
DESCRIPTION: Inserts a VISION_RUN instruction to start a vision craft operation for a specific robot. This function is the robot-specific counterpart to job_insert_vision_craft_start.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_start_robot(SOCKETFD socketFd, int robotNum, int line, int id)
  - Inserts a VISION_RUN instruction to start a vision craft for a specific robot.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - line: The line number in the job file.
    - id: The identifier for the vision craft.
```

----------------------------------------

TITLE: Insert Vision Craft Start Operation
DESCRIPTION: Inserts a VISION_RUN instruction to start a vision process within a job. This function initiates a vision-related task. It requires a socket file descriptor, line number, and a craft ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_start(_socketFd, _line, _id)

Parameters:
  _socketFd: SOCKETFD - File descriptor for the socket connection.
  _line: int - The line number where the job instruction will be inserted.
  _id: int - The craft ID.
```

----------------------------------------

TITLE: Start Vision Craft Instruction
DESCRIPTION: Initiates a vision craft instruction within the job file, associated with a specific line and ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_insert_vision_craft_start(SOCKETFD socketFd, int line, int id);
```

----------------------------------------

TITLE: Vision Craft Start Commands
DESCRIPTION: Adds a 'VISION_RUN (start vision)' command to the local queue for motion mode. Supports both general and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: APIDOC
CODE:
```
queue_motion_push_back_vision_craft_start(_socketFd_, _id_)
  - Adds a VISION_RUN command to the queue.
  - Parameters:
    - _socketFd_: Socket file descriptor.
    - _id_: Craft ID.

queue_motion_push_back_vision_craft_start_robot(_socketFd_, _robotNum_, _id_)
  - Adds a VISION_RUN command to the queue for a specific robot.
  - Parameters:
    - _socketFd_: Socket file descriptor.
    - _robotNum_: Robot number.
    - _id_: Craft ID.
```

LANGUAGE: C
CODE:
```
EXPORT_API Result queue_motion_push_back_vision_craft_start(SOCKETFD _socketFd_, int _id_);
EXPORT_API Result queue_motion_push_back_vision_craft_start_robot(SOCKETFD _socketFd_, int _robotNum_, int _id_);
```

----------------------------------------

TITLE: Get Remote Function
DESCRIPTION: Retrieves remote IO function settings, including general parameters like start, pause, stop, and program-specific settings. It handles different versions of the library.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_remote_function(SOCKETFD _socketFd_, int _robotNum_, int & _num_, int & _time_, RemoteControl & _general_, std::vector< RemoteProgram > & _program_)
  - Retrieves remote IO function settings data.
  - Parameters:
    - _socketFd_: Socket file descriptor for communication.
    - _robotNum_: Robot number (1-4).
    - _num_: Number of remote IOs. In version 22.07, this parameter is not present and will return -1.
    - _time_: IO repeat trigger shielding time in milliseconds.
    - _general_: General function remote IO parameter settings (start, pause, stop, clear alarm, etc.). See RemoteControl for details.
    - _program_: Remote control program parameter settings. See RemoteProgram for details.
  - Returns: Result status.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_remote_function(SOCKETFD _socketFd_, int _robotNum_, int & _num_, int & _time_, RemoteControl & _general_, std::vector< RemoteProgram > & _program_);
```

----------------------------------------

TITLE: net_lib Library Structure
DESCRIPTION: Overview of the net_lib library structure, including links to test lists, deprecated lists, classes, files, and examples.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_p

LANGUAGE: javascript
CODE:
```
net_lib 2.0.4 |  [](javascript:searchBox.CloseResultsWindow())  
---|---  
  * [▼](javascript:void(0))[net_lib](https://doc.hmilib.inexbot.coision.cn/index.html)
    * [Test List](https://doc.hmilib.inexbot.coision.cn/test.html)
    * [Deprecated List](https://doc.hmilib.inexbot.coision.cn/deprecated.html)
    * [►](javascript:void(0))[Classes](https://doc.hmilib.inexbot.coision.cn/annotated.html)
    * [▼](javascript:void(0))[Files](https://doc.hmilib.inexbot.coision.cn/files.html)
      * [►](javascript:void(0))[File List](https://doc.hmilib.inexbot.coision.cn/files.html)
      * [▼](javascript:void(0))[File Members](https://doc.hmilib.inexbot.coision.cn/globals.html)
        * [▼](javascript:void(0))[All](https://doc.hmilib.inexbot.coision.cn/globals.html)
          * [b](https://doc.hmilib.inexbot.coision.cn/globals.html)
          * [c](https://doc.hmilib.inexbot.coision.cn/globals_c.html)
          * [d](https://doc.hmilib.inexbot.coision.cn/globals_d.html)
          * [e](https://doc.hmilib.inexbot.coision.cn/globals_e.html)
          * [f](https://doc.hmilib.inexbot.coision.cn/globals_f.html)
          * [g](https://doc.hmilib.inexbot.coision.cn/globals_g.html)
          * [j](https://doc.hmilib.inexbot.coision.cn/globals_j.html)
          * [l](https://doc.hmilib.inexbot.coision.cn/globals_l.html)
          * [m](https://doc.hmilib.inexbot.coision.cn/globals_m.html)
          * [n](https://doc.hmilib.inexbot.coision.cn/globals_n.html)
          * [o](https://doc.hmilib.inexbot.coision.cn/globals_o.html)
          * [p](https://doc.hmilib.inexbot.coision.cn/globals_p.html)
          * [q](https://doc.hmilib.inexbot.coision.cn/globals_q.html)
          * [r](https://doc.hmilib.inexbot.coision.cn/globals_r.html)
          * [s](https://doc.hmilib.inexbot.coision.cn/globals_s.html)
          * [t](https://doc.hmilib.inexbot.coision.cn/globals_t.html)
          * [v](https://doc.hmilib.inexbot.coision.cn/globals_v.html)
          * [w](https://doc.hmilib.inexbot.coision.cn/globals_w.html)
        * [►](javascript:void(0))[Functions](https://doc.hmilib.inexbot.coision.cn/globals_func.html)
        * [Variables](https://doc.hmilib.inexbot.coision.cn/globals_vars.html)
        * [Typedefs](https://doc.hmilib.inexbot.coision.cn/globals_type.html)
        * [Enumerations](https://doc.hmilib.inexbot.coision.cn/globals_enum.html)
        * [Enumerator](https://doc.hmilib.inexbot.coision.cn/globals_eval.html)
        * [Macros](https://doc.hmilib.inexbot.coision.cn/globals_defs.html)
    * [►](javascript:void(0))[Examples](https://doc.hmilib.inexbot.coision.cn/examples.html)


[•All](javascript:void(0))[](javascript:void(0))[](javascript:void(0))[](javascript:void(0))[](javascript:void(0))[](javascript:void(0))[](javascript:void(0))[](javascript:void(0))[](javascript:void(0))[](javascript:void(0))
Loading...
Searching...
No Matches
```

----------------------------------------

TITLE: Get Track Record Status Robot Example
DESCRIPTION: Example of how to get the track recording status for a specific robot using the get_track_record_status_robot function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_track_record_status_robot(SOCKETFD socketFd, int robotNum, bool &recordStart);
```

----------------------------------------

TITLE: Track Record Save Robot Example
DESCRIPTION: Example of how to save a track record for a specific robot using the track_record_save_robot function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_save_robot(SOCKETFD socketFd, int robotNum, std::string trajName);
```

----------------------------------------

TITLE: Start Vision Craft Instruction for Specific Robot
DESCRIPTION: Initiates a vision craft instruction for a specific robot, allowing robot-specific vision processing steps.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_insert_vision_craft_start_robot(SOCKETFD socketFd, int robotNum, int line, int id);
```

----------------------------------------

TITLE: Get Sensor Data (Robot Specific)
DESCRIPTION: Retrieves sensor data for a specific robot. This function allows accessing sensor information from a particular robot in a multi-robot setup.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_sensor_data_robot(SOCKETFD socketFd, int robotNum, std::vector< int > &data)
  - Retrieves sensor data for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the target robot.
    - data: A reference to a vector of integers to store the sensor data.
  - Returns: void (Sensor data is populated in the 'data' parameter)
```

----------------------------------------

TITLE: Track Record Stop Example
DESCRIPTION: Example of how to stop the track recording process using the track_record_stop function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_stop(SOCKETFD socketFd);
```

----------------------------------------

TITLE: Track Record Delete Example
DESCRIPTION: Example of how to delete the track record using the track_record_delete function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_delete(SOCKETFD socketFd);
```

----------------------------------------

TITLE: Track Record Save Example
DESCRIPTION: Example of how to save the track record with a given trajectory name using the track_record_save function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_save(SOCKETFD socketFd, std::string trajName);
```

----------------------------------------

TITLE: nrc_io.h File Reference
DESCRIPTION: This section provides details about the nrc_io.h file, including its source code link and included header files. It serves as an entry point for understanding the I/O functionalities within the net_lib.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: c++
CODE:
```
#include "parameter/nrc_define.h"
#include "parameter/nrc_io_parameter.h"

[Go to the source code of this file.](https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source.html)
```

----------------------------------------

TITLE: Track Record Delete Robot Example
DESCRIPTION: Example of how to delete the track record for a specific robot using the track_record_delete_robot function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result track_record_delete_robot(SOCKETFD socketFd, int robotNum);
```

----------------------------------------

TITLE: Laser Cutting I/O Parameter Functions
DESCRIPTION: Provides functions to set and get I/O parameters for laser cutting operations. Includes methods for specific robots and general settings.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam param)
  Sets the I/O parameters for laser cutting operations.
  Parameters:
    socketFd: File descriptor for the socket connection.
    param: A LaserCuttingIOParam structure containing the parameters to set.
  Returns: Result indicating success or failure.

laser_cutting_set_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam param)
  Sets the I/O parameters for a specific robot in laser cutting.
  Parameters:
    socketFd: File descriptor for the socket connection.
    robotNum: The number of the robot to configure.
    param: A LaserCuttingIOParam structure containing the parameters to set.
  Returns: Result indicating success or failure.

laser_cutting_get_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam& param)
  Retrieves the current I/O parameters for laser cutting.
  Parameters:
    socketFd: File descriptor for the socket connection.
    param: A reference to a LaserCuttingIOParam structure to store the retrieved parameters.
  Returns: Result indicating success or failure.

laser_cutting_get_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam& param)
  Retrieves the I/O parameters for a specific robot in laser cutting.
  Parameters:
    socketFd: File descriptor for the socket connection.
    robotNum: The number of the robot to query.
    param: A reference to a LaserCuttingIOParam structure to store the retrieved parameters.
  Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Input/Output Parameters
DESCRIPTION: Parameters related to input and output configurations, such as PWM ports.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_p

LANGUAGE: APIDOC
CODE:
```
IO:
  pwm_port_: Configuration for a Pulse Width Modulation (PWM) port.
```

----------------------------------------

TITLE: Get Current Mode (C)
DESCRIPTION: C function to get the current operating mode. Requires a socket file descriptor and a reference to an integer to store the mode. Assumes the existence of EXPORT_API and Result definitions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: C
CODE:
```
EXPORT_API Result get_current_mode(SOCKETFD socketFd, int& mode);
```

----------------------------------------

TITLE: Camera and Cutting Parameters
DESCRIPTION: Structures for camera list management and laser cutting craft parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_c

LANGUAGE: APIDOC
CODE:
```
CameraList:
  Description: List of available cameras.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_camera_list.html

LaserCuttingCraftParam:
  Description: Parameters for laser cutting operations.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_craft_param.html
```

----------------------------------------

TITLE: Single Cycle and Speed Information
DESCRIPTION: Functions to get information about single cycles and the speed of the robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_single_cycle()
  - Retrieves information about a single cycle.
  - Source: nrc_interface.h

get_single_cycle_robot()
  - Retrieves information about a single cycle from the robot.
  - Source: nrc_interface.h

get_speed()
  - Retrieves the current speed settings.
  - Source: nrc_interface.h

get_speed_robot()
  - Retrieves the current speed settings from the robot.
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: API Documentation for Digital I/O
DESCRIPTION: Detailed API documentation for digital input and output functions within the net_lib C interface. This includes function signatures, parameter descriptions, return values, and usage notes for setting and getting digital states.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__io_8h

LANGUAGE: apidoc
CODE:
```
set_digital_output_c(SOCKETFD socketFd, int port, int value)
  - Sets the state of a digital output.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - port: The specific digital port to control.
    - value: The desired state for the digital output (e.g., 0 or 1).
  - Returns: An integer status code indicating success or failure.

get_digital_output_c(SOCKETFD socketFd, double *out)
  - Retrieves the current state of all digital outputs.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - out: A pointer to a double array where the output states will be stored. The array is expected to have a length of 64.
  - Returns: An integer status code indicating success or failure.

get_digital_input_c(SOCKETFD socketFd, double *in)
  - Retrieves the current state of all digital inputs.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - in: A pointer to a double array where the input states will be stored. The array is expected to have a length of 64.
  - Returns: An integer status code indicating success or failure.

Notes:
  - All functions require a valid SOCKETFD to establish communication.
  - The 'out' and 'in' parameters for get_digital_output_c and get_digital_input_c are arrays designed to hold 64 values, representing the states of 64 digital ports.
```

----------------------------------------

TITLE: Get Sensor Data
DESCRIPTION: Retrieves data from the six-dimensional force sensor. This function is used to get real-time sensor readings.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_sensor_data(SOCKETFD socketFd, std::vector< int > &data)
  - Retrieves data from the six-dimensional force sensor.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - data: A reference to a vector of integers to store the sensor data.
  - Returns: void (Sensor data is populated in the 'data' parameter)
```

----------------------------------------

TITLE: Configuration and Utility Functions
DESCRIPTION: Functions for setting robot configuration parameters such as coordinate systems and speed, as well as establishing a connection to the robot controller and clearing errors.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_current_coord_c(SOCKETFD socketFd, int coord)
  Sets the robot's current coordinate system.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    coord: The coordinate system to set.
  Returns: An integer status code.

set_speed_c(SOCKETFD socketFd, int speed)
  Sets the robot's current speed mode.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    speed: The speed mode (e.g., teaching, running, remote).
  Returns: An integer status code.

clear_error_c(SOCKETFD socketFd)
  Clears any active errors on the robot servos.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
  Returns: An integer status code.

connect_robot_c(const char *ip, const char *port)
  Establishes a connection to the robot controller.
  Parameters:
    ip: The IP address of the robot controller.
    port: The port number for the connection.
  Returns: A SOCKETFD representing the connection, or an invalid value on failure.
```

----------------------------------------

TITLE: Input/Output Parameters
DESCRIPTION: Parameters related to input and output configurations, such as PWM ports.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_p

LANGUAGE: APIDOC
CODE:
```
IO:
  pwm_port_: Configuration for a Pulse Width Modulation (PWM) port.
```

----------------------------------------

TITLE: Get Digital Output
DESCRIPTION: Retrieves all digital output states for a given socket connection. This function is used to efficiently get the status of multiple digital outputs at once.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: APIDOC
CODE:
```
get_digital_output(SOCKETFD socketFd, std::vector< int > &out)
  - Retrieves all digital output states.
  - Parameters:
    - socketFd: The socket file descriptor.
    - out: A reference to a vector of integers to store the output states.
  - Returns: Result status.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_digital_output(SOCKETFD socketFd, std::vector< int > &out);
```

----------------------------------------

TITLE: Speed Retrieval Functions
DESCRIPTION: Provides functions to get the current speed. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_speed()
  Retrieves the current speed.
  Source: nrc_interface.h

get_speed_robot()
  Retrieves the current speed for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Add TIGWELD Start Command to Queue
DESCRIPTION: Appends a TIGWELD (fish scale weld start) instruction to the local motion queue. This function takes parameters for weld type and timing/distance.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_tigweld_on(SOCKETFD _socketFd_, int _type_, double _l1_, double _l2_);
```

----------------------------------------

TITLE: Get Static Search Position
DESCRIPTION: Retrieves the static search position coordinates. This function is used to get predefined positional data for robotic operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_static_search_position(SOCKETFD socketFd, int fileid, int tableid, int delaytime, std::vector< double > &pos)
  - Retrieves the static search position coordinates.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - fileid: The ID of the file containing the position data.
    - tableid: The ID of the table within the file.
    - delaytime: A delay time parameter for the search.
    - pos: A reference to a vector of doubles to store the position coordinates.
  - Returns: void (Position coordinates are populated in the 'pos' parameter)
```

----------------------------------------

TITLE: Camera and Cutting Parameters
DESCRIPTION: Structures for camera list management and laser cutting craft parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_c

LANGUAGE: APIDOC
CODE:
```
CameraList:
  Description: List of available cameras.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_camera_list.html

LaserCuttingCraftParam:
  Description: Parameters for laser cutting operations.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_craft_param.html
```

----------------------------------------

TITLE: Teach Type Functions
DESCRIPTION: Provides functions to get the teach type. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_teach_type()
  Retrieves the teach type.
  Source: nrc_interface.h

get_teach_type_robot()
  Retrieves the teach type for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Teachbox Connection Status Functions
DESCRIPTION: Provides functions to get the teachbox connection status. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_teachbox_connection_status()
  Retrieves the connection status of the teachbox.
  Source: nrc_interface.h

get_teachbox_connection_status_robot()
  Retrieves the connection status of the teachbox for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Coordinate and Movement Parameters
DESCRIPTION: Structures related to coordinate systems, movement commands, and servo parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_c

LANGUAGE: APIDOC
CODE:
```
MoveCmd:
  Description: Command for moving an object.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_move_cmd.html

OffsetCommandParam:
  Description: Parameters for offset commands.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_offset_command_param.html

PositionData:
  Description: Data structure for position information.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_position_data.html

ServoMovePara:
  Description: Parameters for servo motor movement.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_servo_move_para.html
```

----------------------------------------

TITLE: Get Robot Type (C)
DESCRIPTION: C function to get the type of the robot. Requires a socket file descriptor and a reference to an integer to store the type. Assumes the existence of EXPORT_API and Result definitions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: C
CODE:
```
EXPORT_API Result get_robot_type(SOCKETFD socketFd, int& type);
```

----------------------------------------

TITLE: Laser Cutting IO Parameter Functions
DESCRIPTION: Provides functions to get and set input/output (IO) parameters for laser cutting. These functions manage the interaction with input and output devices and include robot-specific versions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_l

LANGUAGE: APIDOC
CODE:
```
laser_cutting_get_io_parameter()
  - Retrieves the current IO parameter for laser cutting.
  - Related functions: laser_cutting_get_io_parameter_robot(), laser_cutting_set_io_parameter()

laser_cutting_get_io_parameter_robot()
  - Retrieves the current IO parameter for laser cutting, specifically for robot control.
  - Related functions: laser_cutting_get_io_parameter(), laser_cutting_set_io_parameter_robot()

laser_cutting_set_io_parameter(parameter_value)
  - Sets the IO parameter for laser cutting.
  - Parameters:
    - parameter_value: The value to set for the IO parameter.
  - Related functions: laser_cutting_get_io_parameter(), laser_cutting_set_io_parameter_robot()

laser_cutting_set_io_parameter_robot(parameter_value)
  - Sets the IO parameter for laser cutting, specifically for robot control.
  - Parameters:
    - parameter_value: The value to set for the IO parameter.
  - Related functions: laser_cutting_get_io_parameter_robot(), laser_cutting_set_io_parameter()
```

----------------------------------------

TITLE: Job Opening
DESCRIPTION: Opens a job specified by its name. Requires a socket file descriptor and the job name.

SOURCE: https://doc.hmilib.inexbot.coision.cn/test

LANGUAGE: cpp
CODE:
```
job_open(SOCKETFD socketFd, const std::string &jobName)
```

----------------------------------------

TITLE: Robot Tool Hand Management API
DESCRIPTION: Provides functions to get and set tool hand parameters for robots. Includes functions to get the number of tool hands, set tool hand parameters, and retrieve current tool hand parameters. These functions require a socket file descriptor and relevant tool/robot identifiers.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_tool_hand_number_robot(SOCKETFD socketFd, int robotNum, int &toolNum)
  Retrieves the number of tool hands associated with a specific robot.
  Parameters:
    socketFd: The file descriptor for the socket connection.
    robotNum: The identifier for the robot.
    toolNum: A reference to an integer where the number of tool hands will be stored.

set_tool_hand_param(SOCKETFD socketFd, int toolNum, ToolParam param)
  Sets the parameters for a specific tool hand.
  Parameters:
    socketFd: The file descriptor for the socket connection.
    toolNum: The identifier for the tool hand.
    param: A ToolParam structure containing the parameters to set.

set_tool_hand_param_robot(SOCKETFD socketFd, int robotNum, int toolNum, ToolParam param)
  Sets the parameters for a specific tool hand on a specific robot.
  Parameters:
    socketFd: The file descriptor for the socket connection.
    robotNum: The identifier for the robot.
    toolNum: The identifier for the tool hand.
    param: A ToolParam structure containing the parameters to set.

get_tool_hand_param(SOCKETFD socketFd, int toolNum, ToolParam &param)
  Retrieves the current parameters of a specific tool hand.
  Parameters:
    socketFd: The file descriptor for the socket connection.
    toolNum: The identifier for the tool hand.
    param: A reference to a ToolParam structure where the current parameters will be stored.

get_tool_hand_param_robot(SOCKETFD socketFd, int robotNum, int toolNum, ToolParam &param)
  Retrieves the current parameters of a specific tool hand on a specific robot.
  Parameters:
    socketFd: The file descriptor for the socket connection.
    robotNum: The identifier for the robot.
    toolNum: The identifier for the tool hand.
    param: A reference to a ToolParam structure where the current parameters will be stored.
```

----------------------------------------

TITLE: Coordinate and Movement Parameters
DESCRIPTION: Structures related to coordinate systems, movement commands, and servo parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_c

LANGUAGE: APIDOC
CODE:
```
MoveCmd:
  Description: Command for moving an object.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_move_cmd.html

OffsetCommandParam:
  Description: Parameters for offset commands.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_offset_command_param.html

PositionData:
  Description: Data structure for position information.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_position_data.html

ServoMovePara:
  Description: Parameters for servo motor movement.
  URL: https://doc.hmilib.inexbot.coision.cn/struct_servo_move_para.html
```

----------------------------------------

TITLE: Add TIGWELD Start Command to Robot Queue
DESCRIPTION: Appends a TIGWELD (fish scale weld start) instruction to the local motion queue for a specific robot. This function allows specifying the robot number, weld type, and timing/distance parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_tigweld_on_robot(SOCKETFD _socketFd_, int _robotNum_, int _type_, double _l1_, double _l2_);
```

----------------------------------------

TITLE: Backup System
DESCRIPTION: Performs a one-click system backup, saving the backup to the current execution directory. Requires a socket file descriptor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: c++
CODE:
```
int backup_system(SOCKETFD _socketFd_)
```

----------------------------------------

TITLE: Start Robot Jogging (Single)
DESCRIPTION: Initiates jogging for a specific axis of a single robot. Requires a socket file descriptor, the axis number, and a boolean indicating the direction of movement. The axis parameter specifies which axis to jog, and the dir parameter determines the direction (true for positive, false for negative).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: c++
CODE:
```
Result robot_start_jogging(SOCKETFD _socketFd_, int _axis_, bool _dir_)
```

----------------------------------------

TITLE: Get Current Position
DESCRIPTION: Retrieves the current position of a robot. This function is used to get the XYZ coordinates of a robot's end-effector. It requires a valid socket file descriptor and a coordinate system identifier.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_current_position(SOCKETFD socketFd, int coord, std::vector<double>& pos)
  - Retrieves the current position of a robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - coord: The coordinate system to use (e.g., 0 for world, 1 for robot).
    - pos: A reference to a vector of doubles to store the position (x, y, z).
  - Returns: Result code indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_current_position_robot(SOCKETFD socketFd, int robotNum, int coord, std::vector<double>& pos)
  - Retrieves the current position of a specific robot in a multi-robot system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier of the robot.
    - coord: The coordinate system to use.
    - pos: A reference to a vector of doubles to store the position.
  - Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: Job Creation
DESCRIPTION: Creates a new job with the specified name. Requires a socket file descriptor and the job name as input.

SOURCE: https://doc.hmilib.inexbot.coision.cn/test

LANGUAGE: cpp
CODE:
```
job_create(SOCKETFD socketFd, const std::string &jobName)
```

----------------------------------------

TITLE: Vision Basic Parameter Functions
DESCRIPTION: Functions to get and set basic parameters for the vision system. Includes general and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_v

LANGUAGE: APIDOC
CODE:
```
vision_get_basic_parameter()
  Description: Retrieves the basic parameters of the vision system.
  Source: nrc_craft_vision.h

vision_get_basic_parameter_robot()
  Description: Retrieves the basic parameters of the vision system for robot operations.
  Source: nrc_craft_vision.h

vision_set_basic_parameter(params)
  Description: Sets the basic parameters for the vision system.
  Parameters:
    params: The basic parameters to set.
  Source: nrc_craft_vision.h

vision_set_basic_parameter_robot(params)
  Description: Sets the basic parameters for the vision system for robot operations.
  Parameters:
    params: The basic parameters to set.
  Source: nrc_craft_vision.h
```

----------------------------------------

TITLE: Get Robot Type for Robot (C)
DESCRIPTION: C function to get the type of a specific robot. Requires a socket file descriptor, the robot number, and a reference to an integer to store the type. Assumes the existence of EXPORT_API and Result definitions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: C
CODE:
```
EXPORT_API Result get_robot_type_robot(SOCKETFD socketFd, int robotNum, int& type);
```

----------------------------------------

TITLE: Backup System
DESCRIPTION: Initiates a system backup. This function requires a socket file descriptor to establish communication for the backup process.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result backup_system(SOCKETFD socketFd);
```

----------------------------------------

TITLE: Get Remote Parameter
DESCRIPTION: Retrieves remote parameter settings, including speed, auto-start status, and timing parameters. It also includes a parameter for the number of remote IOs, with version-specific notes.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_remote_param(SOCKETFD _socketFd_, int _robotNum_, int & _speed_, bool & _start_, int & _time_, int & _startTime_, int & _num_)
  - Retrieves remote parameter settings data.
  - Parameters:
    - _socketFd_: Socket file descriptor for communication.
    - _robotNum_: Robot number (1-4).
    - _speed_: Remote mode default speed [1, 100].
    - _start_: Whether to auto-start.
    - _time_: IO repeat trigger shielding time in milliseconds.
    - _startTime_: Start confirmation time.
    - _num_: Number of remote IOs. In version 22.07, this parameter is not present and will return -1.
  - Returns: Result status.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_remote_param(SOCKETFD _socketFd_, int _robotNum_, int & _speed_, bool & _start_, int & _time_, int & _startTime_, int & _num_);
```

----------------------------------------

TITLE: Get Current Mode for Robot (C)
DESCRIPTION: C function to get the current operating mode for a specific robot. Requires a socket file descriptor, the robot number, and a reference to an integer to store the mode. Assumes the existence of EXPORT_API and Result definitions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: C
CODE:
```
EXPORT_API Result get_current_mode_robot(SOCKETFD socketFd, int robotNum, int& mode);
```

----------------------------------------

TITLE: Robot Connection and Control Functions
DESCRIPTION: Provides functions to establish a connection to the robot controller, clear errors, set servo states, and manage robot power. These functions are essential for initial setup and ongoing control.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
connect_robot_c(ip: str, port: str) -> SOCKETFD
  Connects to the robot controller.
  Parameters:
    ip: The IP address of the robot controller.
    port: The port number for the connection.
  Returns: A socket file descriptor for the connection.

clear_error_c(socketFd: SOCKETFD) -> int
  Clears any errors on the robot controller.
  Parameters:
    socketFd: The socket file descriptor of the robot connection.
  Returns: An integer status code.

set_servo_state_c(socketFd: SOCKETFD, state: int) -> int
  Sets the state of the robot's servos.
  Parameters:
    socketFd: The socket file descriptor of the robot connection.
    state: The desired servo state (e.g., enabled, disabled).
  Returns: An integer status code.

set_servo_poweron_c(socketFd: SOCKETFD) -> int
  Powers on the robot's servos.
  Parameters:
    socketFd: The socket file descriptor of the robot connection.
  Returns: An integer status code.

set_servo_poweroff_c(socketFd: SOCKETFD) -> int
  Powers off the robot's servos.
  Parameters:
    socketFd: The socket file descriptor of the robot connection.
  Returns: An integer status code.
```

----------------------------------------

TITLE: Single Cycle Functions
DESCRIPTION: Provides functions to get single cycle information. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_single_cycle()
  Retrieves single cycle information.
  Source: nrc_interface.h

get_single_cycle_robot()
  Retrieves single cycle information for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Add Vision Craft Start Command to Queue
DESCRIPTION: Appends a 'vision_craft_start' command to the motion queue, initiating a vision process. Requires a socket file descriptor and an ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c++
CODE:
```
EXPORT_API Result queue_motion_push_back_vision_craft_start(SOCKETFD socketFd, int id)
```

----------------------------------------

TITLE: Vision System Parameters
DESCRIPTION: Configuration settings for the vision system, including protocol and position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_p

LANGUAGE: APIDOC
CODE:
```
VisionParam:
  protocol: Defines the communication protocol for the vision system.
```

LANGUAGE: APIDOC
CODE:
```
VisionPositionParam:
  protocol: Defines the communication protocol for vision-based positioning.
```

----------------------------------------

TITLE: Insert Vision Craft Get Position Operation
DESCRIPTION: Inserts a VISION_POS instruction into the job file to get a vision position. This is used in conjunction with vision systems to capture and store positional data. It requires a socket file descriptor, line number, an ID, and a position name.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_get_pos(_socketFd, _line, _id, _posName)

Parameters:
  _socketFd: SOCKETFD - File descriptor for the socket connection.
  _line: int - The line number where the job instruction will be inserted.
  _id: int - The craft ID.
  _posName: const std::string - The variable name to store the position in (e.g., GP0001).
```

----------------------------------------

TITLE: Robot State and Status API
DESCRIPTION: APIs to get the current running state of the robot and the status of its servos.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_robot_running_state_c(_socketFd_, _status_)
  Retrieves the current running state of the robot.
  Parameters:
    _socketFd_: Socket file descriptor for communication.
    _status_: Reference to an integer to store the robot's running state (0: Stopped, 1: Paused, 2: Running).
  Returns: 0 on success, non-zero on failure.
```

LANGUAGE: APIDOC
CODE:
```
get_servo_state_c(_socketFd_, _status_)
  Retrieves the current state of the robot's servos.
  Parameters:
    _socketFd_: Socket file descriptor for communication.
    _status_: Reference to an integer to store the servo state (0: Stopped, 1: Ready, 2: Alarm, 3: Running).
  Returns: 0 on success, non-zero on failure.
```

----------------------------------------

TITLE: Add TIGWELD Command to Queue
DESCRIPTION: Adds a TIGWELD (TIG welding start) instruction to the local queue for initiating TIG welding operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_tigweld_on(SOCKETFD socketFd, int type, double l1, double l2)
// 队列运动模式的本地队列最后插入一条TIGWELD(鱼鳞焊开始)指令
```

----------------------------------------

TITLE: Remote Parameter Setting
DESCRIPTION: Sets remote parameters for a robot, including speed, start status, and timing. It allows for a default number of parameters to be set.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: APIDOC
CODE:
```
set_remote_param(SOCKETFD socketFd, int robotNum, int speed, bool start, int time, int startTime, int num=10)
  - Sets remote parameters for a robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - speed: The speed setting.
    - start: Boolean indicating if the operation should start.
    - time: The time parameter.
    - startTime: The start time parameter.
    - num: The number of parameters (defaults to 10).
  - Returns: Result status.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result set_remote_param(SOCKETFD socketFd, int robotNum, int speed, bool start, int time, int startTime, int num=10);
```

----------------------------------------

TITLE: Tool Hand Parameter Functions
DESCRIPTION: Provides functions to get tool hand parameters. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_tool_hand_param()
  Retrieves the tool hand parameters.
  Source: nrc_interface.h

get_tool_hand_param_robot()
  Retrieves the tool hand parameters for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Add TOFFSETON Command to Queue
DESCRIPTION: Adds a TOFFSETON (trajectory offset start) instruction to the local queue for robot motion control.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_TOFFSETON(SOCKETFD socketFd, OffsetCommandParam params)
// 队列运动模式的本地队列最后插入一条TOFFSETON(轨迹偏移开始)指令
```

----------------------------------------

TITLE: Insert Vision Craft Get Position Operation for Robot
DESCRIPTION: Inserts a VISION_POS instruction for a robot to get a vision position. This function is used to capture and store positional data for robot-controlled vision tasks. It requires a socket file descriptor, robot number, line number, an ID, and a position name.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_get_pos_robot(_socketFd, _robotNum, _line, _id, _posName)

Parameters:
  _socketFd: SOCKETFD - File descriptor for the socket connection.
  _robotNum: int - The robot number.
  _line: int - The line number where the job instruction will be inserted.
  _id: int - The craft ID.
  _posName: const std::string - The variable name to store the position in.
```

----------------------------------------

TITLE: Add ARCON Command to Queue
DESCRIPTION: Adds an ARCON (arc welding start) instruction to the local queue for initiating welding operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_arc_on(SOCKETFD socketFd, int id)
// 队列运动模式的本地队列最后插入一条ARCON(焊接开始)指令
```

----------------------------------------

TITLE: Get Connection Status
DESCRIPTION: Retrieves the connection status of the controller. It requires a socket file descriptor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_connection_status(SOCKETFD _socketFd_)
  Description: Obtains the controller connection status.
  Parameters:
    _socketFd_: File descriptor for the socket connection.
```

----------------------------------------

TITLE: Get Motion Queue Size
DESCRIPTION: Queries the current length of the motion queue.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: APIDOC
CODE:
```
queue_motion_size(SOCKETFD socketFd, int &size)
  - Queries the current length of the motion queue.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - size: Output parameter to store the queue length.
  - Returns: Result status of the operation.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result queue_motion_size(SOCKETFD socketFd, int &size)
// 查询当前运动队列的长度
```

----------------------------------------

TITLE: Vision System Parameters
DESCRIPTION: Configuration settings for the vision system, including protocol and position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_p

LANGUAGE: APIDOC
CODE:
```
VisionParam:
  protocol: Defines the communication protocol for the vision system.
```

LANGUAGE: APIDOC
CODE:
```
VisionPositionParam:
  protocol: Defines the communication protocol for vision-based positioning.
```

----------------------------------------

TITLE: Vision Basic Parameter Functions
DESCRIPTION: Functions to get and set basic parameters for the vision system. Includes general and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_v

LANGUAGE: APIDOC
CODE:
```
vision_get_basic_parameter()
  Description: Retrieves the basic parameters of the vision system.
  Source: nrc_craft_vision.h

vision_get_basic_parameter_robot()
  Description: Retrieves the basic parameters of the vision system for robot operations.
  Source: nrc_craft_vision.h

vision_set_basic_parameter(params)
  Description: Sets the basic parameters for the vision system.
  Parameters:
    params: The basic parameters to set.
  Source: nrc_craft_vision.h

vision_set_basic_parameter_robot(params)
  Description: Sets the basic parameters for the vision system for robot operations.
  Parameters:
    params: The basic parameters to set.
  Source: nrc_craft_vision.h
```

----------------------------------------

TITLE: User Coordinate Parameter Functions
DESCRIPTION: Provides functions to get user coordinate parameters. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_user_coord_para()
  Retrieves the user coordinate parameters.
  Source: nrc_interface.h

get_user_coord_para_robot()
  Retrieves the user coordinate parameters for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Laser Cutting Parameters
DESCRIPTION: Configuration parameters for laser cutting equipment and processes.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_p

LANGUAGE: APIDOC
CODE:
```
LaserCuttingParam:
  power: Sets the power level for the laser cutter.
  pressure: Configures the pressure settings for the laser cutting process.
```

LANGUAGE: APIDOC
CODE:
```
LaserCuttingEquipment:
  preAspiratedTime: Duration for pre-aspiration before laser cutting begins.
```

LANGUAGE: APIDOC
CODE:
```
LaserCuttingIOParam:
  pressure_fault: Parameter indicating a fault condition related to pressure.
```

LANGUAGE: APIDOC
CODE:
```
LaserCuttingAnalogMatch:
  pressure: Analog match configuration for pressure readings.
```

----------------------------------------

TITLE: Get Robot Motion Status
DESCRIPTION: Retrieves the motion status of a specific robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: APIDOC
CODE:
```
queue_motion_get_status_robot(SOCKETFD socketFd, int robotNum, bool &status)
  - Retrieves the motion status of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number.
    - status: Output parameter to store the robot's status (true if moving, false otherwise).
  - Returns: Result status of the operation.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result queue_motion_get_status_robot(SOCKETFD socketFd, int robotNum, bool &status)
```

----------------------------------------

TITLE: Class Members - Z
DESCRIPTION: Lists class members starting with 'Z', linking to their respective class definitions. Includes 'ToolParam' and 'Excursion'.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_z

LANGUAGE: APIDOC
CODE:
```
Z : ToolParam
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_tool_param.html#a4d32310588c176e77192efe49424d171

Zexcursion : Excursion
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_excursion.html#ad532e84c7215f236abeee5b2a3804086
```

----------------------------------------

TITLE: Laser Cutting I/O Parameter Functions
DESCRIPTION: Provides functions to set and get I/O parameters for laser cutting. These functions support setting parameters for a single robot or a specified robot number, and retrieving parameters. They require a socket file descriptor and the relevant I/O parameter structure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam param)
  Sets the I/O parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: The LaserCuttingIOParam structure containing the parameters.

laser_cutting_set_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam param)
  Sets the I/O parameters for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
    param: The LaserCuttingIOParam structure containing the parameters.

laser_cutting_get_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam &param)
  Retrieves the I/O parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: A reference to the LaserCuttingIOParam structure to store the retrieved parameters.

laser_cutting_get_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam &param)
  Retrieves the I/O parameters for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
    param: A reference to the LaserCuttingIOParam structure to store the retrieved parameters.
```

----------------------------------------

TITLE: Digital Output Functions
DESCRIPTION: Functions for setting and getting digital output states. `set_digital_output` configures a specific digital output, while `get_digital_output` retrieves the status of all digital outputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
set_digital_output(SOCKETFD socketFd, int port, int value)
  - Sets a digital output to a specified value.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - port: The digital output port number.
    - value: The desired state for the digital output (e.g., 0 or 1).
  - Returns: Result indicating success or failure.

get_digital_output(SOCKETFD socketFd, std::vector< int > &out)
  - Retrieves the current state of all digital outputs.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - out: A reference to a vector that will be populated with the states of the digital outputs.
  - Returns: Result indicating success or failure.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result set_digital_output(SOCKETFD socketFd, int port, int value);
EXPORT_API Result get_digital_output(SOCKETFD socketFd, std::vector< int > &out);
```

----------------------------------------

TITLE: Vision Basic Parameter Functions
DESCRIPTION: Provides functions to get basic parameters for vision systems. Supports both general vision and robot-specific configurations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h_source

LANGUAGE: APIDOC
CODE:
```
vision_get_basic_parameter(SOCKETFD socketFd, int visionNum, VisionParam& vsPamrm)
  Retrieves basic parameters for a specified vision system.
  Parameters:
    socketFd: The socket file descriptor for communication.
    visionNum: The identifier for the vision system.
    vsPamrm: A reference to a VisionParam structure to store the retrieved parameters.
  Returns: Result code indicating success or failure.

vision_get_basic_parameter_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionParam& vsPamrm)
  Retrieves basic parameters for a specified vision system associated with a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The identifier for the robot.
    visionNum: The identifier for the vision system.
    vsPamrm: A reference to a VisionParam structure to store the retrieved parameters.
  Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: Navigation and Search Functionality
DESCRIPTION: This snippet demonstrates common JavaScript functions used for navigation and search within the documentation interface. These include closing search windows and handling search results.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_q

LANGUAGE: javascript
CODE:
```
javascript:searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: Get Analog Output
DESCRIPTION: Retrieves the value of an analog output. This function is part of the I/O module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_analog_output()
  - Retrieves the value of an analog output.
  - Source: nrc_io.h
```

----------------------------------------

TITLE: Set Servo Power On
DESCRIPTION: Powers up the robot's servos. Before calling this, the servo state must be set to 'Ready' (1) using `set_servo_state_c`. Successful power-on results in a 'Running' state (3).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
set_servo_poweron_c(SOCKETFD _socketFd_)

Parameters:
  _socketFd_: File descriptor for the socket connection.

Description:
  Powers up the robot's servos.

Attention:
  Before calling this function, ensure the servo state is set to 1 (Ready) using set_servo_state(SOCKETFD socketFd, 1).
  After successful power-on, calling get_servo_state(SOCKETFD socketFd) should return 3 (Servo Running state).

Returns:
  The current servo status. This function is effective only when the servo status is 1 (Ready state).
```

----------------------------------------

TITLE: Navigation Functions
DESCRIPTION: Provides navigation links within the documentation. Includes links to search functionality.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_n

LANGUAGE: javascript
CODE:
```
searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: Get Connection Status
DESCRIPTION: Retrieves the connection status of the interface. This function is part of the interface module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_connection_status()
  - Retrieves the connection status of the interface.
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Get Analog Input
DESCRIPTION: Retrieves the value of an analog input. This function is part of the I/O module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_analog_input()
  - Retrieves the value of an analog input.
  - Source: nrc_io.h
```

----------------------------------------

TITLE: Laser Cutting Parameters
DESCRIPTION: Configuration parameters for laser cutting equipment and processes.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_p

LANGUAGE: APIDOC
CODE:
```
LaserCuttingParam:
  power: Sets the power level for the laser cutter.
  pressure: Configures the pressure settings for the laser cutting process.
```

LANGUAGE: APIDOC
CODE:
```
LaserCuttingEquipment:
  preAspiratedTime: Duration for pre-aspiration before laser cutting begins.
```

LANGUAGE: APIDOC
CODE:
```
LaserCuttingIOParam:
  pressure_fault: Parameter indicating a fault condition related to pressure.
```

LANGUAGE: APIDOC
CODE:
```
LaserCuttingAnalogMatch:
  pressure: Analog match configuration for pressure readings.
```

----------------------------------------

TITLE: Analog Output Functions
DESCRIPTION: Functions for setting and getting analog output values. `set_analog_output` configures a specific analog output, while `get_analog_output` retrieves the values of all analog outputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
set_analog_output(SOCKETFD socketFd, int port, double value)
  - Sets an analog output to a specified value.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - port: The analog output port number.
    - value: The desired analog value for the output.
  - Returns: Result indicating success or failure.

get_analog_output(SOCKETFD socketFd, std::vector< double > &aout)
  - Retrieves the current values of all analog outputs.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - aout: A reference to a vector that will be populated with the values of the analog outputs.
  - Returns: Result indicating success or failure.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result set_analog_output(SOCKETFD socketFd, int port, double value);
EXPORT_API Result get_analog_output(SOCKETFD socketFd, std::vector< double > &aout);
```

----------------------------------------

TITLE: ConveyorTrackRangeParams Structure
DESCRIPTION: Defines parameters for a conveyor track range, including the starting X-point of the track.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track__parameter_8h_source

LANGUAGE: cpp
CODE:
```
struct ConveyorTrackRangeParams {
    double position_trackStartXPoint; // 轨道起始X点
};
```

----------------------------------------

TITLE: Calibration and Configuration Functions
DESCRIPTION: APIs for performing various calibration routines for tool hands and robots, as well as setting and retrieving robot configurations and parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
tool_hand_2_or_20_point_calibrate_robot(SOCKETFD socketFd, int robotNum, int point)
  Performs a 2 or 20 point calibration for a tool hand on a specific robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The robot number.
    point: The calibration point number.

set_tool_hand_param(SOCKETFD socketFd, int toolNum, ToolParam param)
  Sets parameters for a tool hand.
  Parameters:
    socketFd: The socket file descriptor for communication.
    toolNum: The tool hand number.
    param: Structure containing tool hand parameters.

set_tool_hand_param_robot(SOCKETFD socketFd, int robotNum, int toolNum, ToolParam param)
  Sets parameters for a tool hand on a specific robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The robot number.
    toolNum: The tool hand number.
    param: Structure containing tool hand parameters.

set_four_point_mark(SOCKETFD socketFd, int point, int status)
  Sets or clears a four-point mark.
  Parameters:
    socketFd: The socket file descriptor for communication.
    point: The mark point identifier.
    status: The status to set (e.g., enable/disable).

tool_hand_7_point_calibrate(SOCKETFD socketFd, int point, int toolNum)
  Performs a 7-point calibration for the currently selected tool hand.
  Requires `set_tool_hand_number` to be called beforehand to select the tool hand.
  Parameters:
    socketFd: The socket file descriptor for communication.
    point: The calibration point number.
    toolNum: The tool hand number.

tool_hand_7_point_calibrate_clear(SOCKETFD socketFd, int pointNum, int toolNum)
  Clears a specific calibration point for a tool hand.
  Parameters:
    socketFd: The socket file descriptor for communication.
    pointNum: The calibration point number to clear.
    toolNum: The tool hand number.

get_robot_configuration(SOCKETFD socketFd, int &configuration)
  Retrieves the configuration of a 4-axis Sacra robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
  Outputs:
    configuration: An integer representing the robot's configuration.

set_global_position_robot(SOCKETFD socketFd, int robotNum, std::string posName, std::vector< double > posInfo)
  Sets the global position for a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The robot number.
    posName: The name of the position.
    posInfo: A vector of doubles representing the position information.
```

----------------------------------------

TITLE: Laser Cutting Parameters
DESCRIPTION: This section details the data structures used for configuring laser cutting operations. It includes parameters for craft-specific settings, global configurations, and input/output handling.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: APIDOC
CODE:
```
LaserCuttingCraftParam:
  Definition: nrc_craft_laser_cutting_parameter.h:52
  Link: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_craft_param.html

LaserCuttingGlobalParam:
  Definition: nrc_craft_laser_cutting_parameter.h:36
  Link: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_global_param.html

LaserCuttingIOParam:
  Definition: nrc_craft_laser_cutting_parameter.h:22
  Link: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_i_o_param.html

Related Files:
  cpp_interface: https://doc.hmilib.inexbot.coision.cn/dir_d582843885a898eddf4308e9dd5ad1b3.html
  nrc_craft_laser_cutting.h: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h.html

Generated By: Doxygen 1.11.0
```

----------------------------------------

TITLE: Start Robot Jogging (Multi-Robot)
DESCRIPTION: Initiates jogging for a specific axis of a specified robot in a multi-robot system. Requires a socket file descriptor, the robot number, the axis number, and a boolean indicating the direction of movement.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: c++
CODE:
```
Result robot_start_jogging_robot(SOCKETFD _socketFd_, int _robotNum_, int _axis_, bool _dir_)
```

----------------------------------------

TITLE: Servo State Functions
DESCRIPTION: Provides functions to get the state of the servo motors. Includes a C interface and a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_servo_state()
  Retrieves the current state of the servo motors.
  Source: nrc_interface.h

get_servo_state_c()
  Retrieves the current state of the servo motors via C interface.
  Source: nrc_c_interface.h

get_servo_state_robot()
  Retrieves the current state of the servo motors for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Laser Cutting I/O Parameter Management
DESCRIPTION: APIs for managing I/O parameters for laser cutting. Includes functions to set and get I/O parameters for specific robots and general settings. These functions control the input and output signals related to the laser cutting process.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam param)
  - Sets the I/O parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A LaserCuttingIOParam structure containing the I/O parameters.
  - Returns: Result status of the operation.

laser_cutting_get_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam &param)
  - Retrieves the I/O parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A reference to a LaserCuttingIOParam structure to store the retrieved I/O parameters.
  - Returns: Result status of the operation.

laser_cutting_set_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam param)
  - Sets the I/O parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to configure.
    - param: A LaserCuttingIOParam structure containing the I/O parameters.
  - Returns: Result status of the operation.

laser_cutting_get_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam &param)
  - Retrieves the I/O parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to retrieve parameters from.
    - param: A reference to a LaserCuttingIOParam structure to store the retrieved I/O parameters.
  - Returns: Result status of the operation.

Related Types:
  - SOCKETFD: Integer representing a socket file descriptor.
  - LaserCuttingIOParam: Structure holding I/O parameters for laser cutting.
  - Result: Enum or type indicating the success or failure of an operation.
```

----------------------------------------

TITLE: SafeIO Class Variables
DESCRIPTION: This section lists the variables associated with the SafeIO class within the net_lib library. These variables are related to the quick stop functionality, including enable status, port configurations, shield settings, and time parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_q

LANGUAGE: APIDOC
CODE:
```
SafeIO Class Variables:

- quickStopEnable: Type boolean. Indicates if quick stop is enabled.
- quickStopPort1: Type integer. Configuration for quick stop port 1.
- quickStopPort2: Type integer. Configuration for quick stop port 2.
- quickStopShied1: Type integer. Shield setting for quick stop 1.
- quickStopShied2: Type integer. Shield setting for quick stop 2.
- quickStopShiedTime: Type integer. Time duration for quick stop shielding.
- quickStopTime: Type integer. General time parameter for quick stop.
- quickStopValue1: Type integer. Value associated with quick stop 1.
- quickStopValue2: Type integer. Value associated with quick stop 2.
```

----------------------------------------

TITLE: Global Position Management Functions
DESCRIPTION: These functions allow setting and retrieving global positions. They support both general users and robots, requiring a socket file descriptor, a position name, and position data (for setting) or a reference to store position data (for getting).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_global_position(SOCKETFD socketFd, std::string posName, std::vector<double> posInfo)
  Sets a global position for a user.
  Parameters:
    socketFd: The socket file descriptor for communication.
    posName: The name of the position to set.
    posInfo: A vector of doubles representing the position coordinates.
  Returns: Result of the set operation.

set_global_position_robot(SOCKETFD socketFd, int robotNum, std::string posName, std::vector<double> posInfo)
  Sets a global position for a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The identifier for the robot.
    posName: The name of the position to set.
    posInfo: A vector of doubles representing the position coordinates.
  Returns: Result of the set operation.

get_global_position(SOCKETFD socketFd, std::string posName, std::vector<double>& pos)
  Retrieves a global position for a user.
  Parameters:
    socketFd: The socket file descriptor for communication.
    posName: The name of the position to retrieve.
    pos: A reference to a vector of doubles where the position coordinates will be stored.
  Returns: Result of the get operation.

get_global_position_robot(SOCKETFD socketFd, int robotNum, std::string posName, std::vector<double>& pos)
  Retrieves a global position for a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The identifier for the robot.
    posName: The name of the position to retrieve.
    pos: A reference to a vector of doubles where the position coordinates will be stored.
  Returns: Result of the get operation.
```

----------------------------------------

TITLE: Static Search Position Functions
DESCRIPTION: Provides functions to get the static search position. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_static_search_position()
  Retrieves the static search position.
  Source: nrc_interface.h

get_static_search_position_robot()
  Retrieves the static search position for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Robot Configuration and Control
DESCRIPTION: Functions for configuring robot settings, such as setting user coordinates, global variables, and controlling servo power.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_position_dragParams_robot(SOCKETFD socketFd, int robotNum, double dragInPosMaxVel, double dragInPosMaxAngleVel)
  - Sets drag parameters for position control of a robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number.
    - dragInPosMaxVel: Maximum velocity for drag in position.
    - dragInPosMaxAngleVel: Maximum angular velocity for drag in position.

set_user_coordinate_data_robot(SOCKETFD socketFd, int robotNum, int userNum, std::vector< double > pos)
  - Sets user coordinate data for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number.
    - userNum: The user coordinate number.
    - pos: A vector of doubles representing the position data.

set_global_variant(SOCKETFD socketFd, const std::string &varName, double varValue)
  - Sets a global variable.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - varName: The name of the global variable.
    - varValue: The value to set for the global variable.

set_servo_poweron(SOCKETFD socketFd)
  - Turns on the servo power for the robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
```

----------------------------------------

TITLE: Set Teach Type
DESCRIPTION: Configures the teaching mode or type for the robot. This function is used to set how the robot is programmed or guided.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_s

LANGUAGE: C
CODE:
```
void set_teach_type();
```

LANGUAGE: C
CODE:
```
void set_teach_type_robot();
```

----------------------------------------

TITLE: job_create API
DESCRIPTION: Creates a new job file. Requires a socket file descriptor and job name. The job name must start with a letter and can be alphanumeric.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_create(_socketFd: SOCKETFD, _jobName: const std::string &)
  - Creates a new job file.
  - Parameters:
    - _socketFd: File descriptor for the socket connection.
    - _jobName: The name of the job file. Must start with a letter and can be alphanumeric.
  - Example:
    新建QQQ.JBR job_create(SOCKETFD socketFd,"QQQ");
```

----------------------------------------

TITLE: Robot Jogging Functions
DESCRIPTION: Functions to start and stop robot jogging operations, allowing for manual control of robot movement.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_r

LANGUAGE: APIDOC
CODE:
```
robot_start_jogging()
  - Initiates robot jogging mode.
  - Parameters: (Details not provided in source)
  - Returns: (Details not provided in source)

robot_start_jogging_robot()
  - Initiates robot jogging mode with robot-specific parameters.
  - Parameters: (Details not provided in source)
  - Returns: (Details not provided in source)

robot_stop_jogging()
  - Stops the current robot jogging operation.
  - Parameters: (Details not provided in source)
  - Returns: (Details not provided in source)

robot_stop_jogging_robot()
  - Stops the current robot jogging operation with robot-specific parameters.
  - Parameters: (Details not provided in source)
  - Returns: (Details not provided in source)
```

----------------------------------------

TITLE: Insert Conveyor On Command
DESCRIPTION: Inserts a CONVEYOR_ON (conveyor tracking start) instruction into the job file. This function initiates conveyor tracking with specified position, velocity, and acceleration.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_on(SOCKETFD socketFd, int line, int id, int postype, std::vector< double > pos, int vel, int acc)
  - Inserts a CONVEYOR_ON instruction.
  - Parameters:
    - socketFd: File descriptor for the socket.
    - line: The line number in the job file.
    - id: The job ID.
    - postype: Type of position data.
    - pos: Position vector.
    - vel: Velocity.
    - acc: Acceleration.
```

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_on_robot(SOCKETFD socketFd, int robotNum, int line, int id, int postype, std::vector< double > pos, int vel, int acc)
  - Inserts a CONVEYOR_ON instruction for a specific robot.
  - Parameters:
    - socketFd: File descriptor for the socket.
    - robotNum: The robot number.
    - line: The line number in the job file.
    - id: The job ID.
    - postype: Type of position data.
    - pos: Position vector.
    - vel: Velocity.
    - acc: Acceleration.
```

----------------------------------------

TITLE: nrc_craft_laser_cutting.h File Reference
DESCRIPTION: This snippet provides information about the nrc_craft_laser_cutting.h file, including its include directives for parameter definitions and specific laser cutting parameters. It also links to the source code of the file.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h

LANGUAGE: c++
CODE:
```
#include "parameter/nrc_define.h"
#include "parameter/nrc_craft_laser_cutting_parameter.h"

[Go to the source code of this file.](https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source.html)
```

----------------------------------------

TITLE: Get Library Version
DESCRIPTION: Retrieves the current version of the net_lib library. This function is part of the core interface for the library.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
std::string get_library_version();
```

----------------------------------------

TITLE: Robot Return to Reset Position
DESCRIPTION: Commands the robot to move to its predefined reset position. This is a common operation for homing or initial setup.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
robot_go_to_reset_position(SOCKETFD socketFd)
  - Commands the robot to move to its reset position.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
  - Returns: Result status of the operation.
  - Usage: 回到设定的复位点
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result robot_go_to_reset_position(SOCKETFD socketFd);
// 回到设定的复位点
```

----------------------------------------

TITLE: Parameter Header Files
DESCRIPTION: This section lists the header files related to parameter definitions within the net_lib library. These files define various parameters used for different functionalities like conveyor belts, laser cutting, vision systems, welding, and general I/O operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/dir_dee0f2b0fd08629afce5df5bd936d2dc

LANGUAGE: c++
CODE:
```
#include "nrc_craft_conveyor_belt_track_parameter.h"
#include "nrc_craft_laser_cutting_parameter.h"
#include "nrc_craft_vision_parameter.h"
#include "nrc_craft_weld_parameter.h"
#include "nrc_define.h"
#include "nrc_interface_parameter.h"
#include "nrc_io_parameter.h"
#include "nrc_modbus_parameter.h"
#include "nrc_parameter.h"
```

----------------------------------------

TITLE: Get Robot Queue Length
DESCRIPTION: Retrieves the current length of the motion queue for a specific robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: APIDOC
CODE:
```
queue_motion_get_queuelen_robot(SOCKETFD socketFd, int robotNum, int &len)
  - Retrieves the current length of the motion queue for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number.
    - len: Output parameter to store the queue length.
  - Returns: Result status of the operation.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result queue_motion_get_queuelen_robot(SOCKETFD socketFd, int robotNum, int &len)
```

----------------------------------------

TITLE: Interface Parameters and Definitions
DESCRIPTION: This section details various interface parameters and definitions used within the project, referencing specific elements from header files.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_s

LANGUAGE: APIDOC
CODE:
```
SIX_AXLE_ONE_GENERAL:
  Description: Represents a parameter related to a six-axle configuration.
  Reference: nrc_interface_parameter.h
  Link: https://doc.hmilib.inexbot.coision.cn/nrc__interface__parameter_8h.html#a78d284d08fd22d809fd436256f2cbc39a400fff9cacf30d152a2c346c85f99cc4

SIX_AXLE_SPRAY:
  Description: Represents a parameter related to a six-axle spray configuration.
  Reference: nrc_interface_parameter.h
  Link: https://doc.hmilib.inexbot.coision.cn/nrc__interface__parameter_8h.html#a78d284d08fd22d809fd436256f2cbc39a60216b3197f8fdd7661d856b0fb3dbb8

SOCKETFD:
  Description: Defines a socket file descriptor.
  Reference: nrc_define.h
  Link: https://doc.hmilib.inexbot.coision.cn/nrc__define_8h.html#a1175bf3b72c7b72ab80c23e3435a962f

SUCCESS:
  Description: Represents a success status or code.
  Reference: nrc_define.h
  Link: https://doc.hmilib.inexbot.coision.cn/nrc__define_8h.html#a28287671eaf7406afd604bd055ba4066ac7f69f7c9e5aea9b8f54cf02870e2bf8
```

----------------------------------------

TITLE: Get Teach Type
DESCRIPTION: Retrieves the teach type, indicating whether the mode is jog (0) or drag (1).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_teach_type(SOCKETFD _socketFd_, int & _type_)
  - Retrieves the teach type.
  - Description: Gets the type of teaching mode. 0 for jog, 1 for drag.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _type_: A reference to an integer where the teach type will be stored (0 for jog, 1 for drag).
```

LANGUAGE: C++
CODE:
```
Result get_teach_type(SOCKETFD _socketFd_, int & _type_)
```

----------------------------------------

TITLE: Conveyor Belt Basic Parameters
DESCRIPTION: Functions to get basic parameters for the conveyor belt, with separate implementations for direct interface and robot interaction.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_c

LANGUAGE: c
CODE:
```
conveyor_belt_get_basic_paramters();
conveyor_belt_get_basic_paramters_robot();
```

----------------------------------------

TITLE: HMI Vision Basic Parameter Configuration
DESCRIPTION: Provides API documentation for setting and getting basic parameters for vision systems. Includes functions for direct vision control and robot-integrated control.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h

LANGUAGE: APIDOC
CODE:
```
vision_set_basic_parameter(SOCKETFD socketFd, int visionNum, VisionParam vsPamrm)
  - Sets the basic parameters for a vision system.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionParam structure containing the parameters to set.

vision_set_basic_parameter_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionParam vsPamrm)
  - Sets the basic parameters for a vision system integrated with a robot.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - robotNum: Identifier for the robot.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionParam structure containing the parameters to set.

vision_get_basic_parameter(SOCKETFD socketFd, int visionNum, VisionParam &vsPamrm)
  - Queries the basic parameters of a vision system.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionParam structure to store the retrieved parameters.
  - Returns: Result status.

vision_get_basic_parameter_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionParam &vsPamrm)
  - Queries the basic parameters of a vision system integrated with a robot.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - robotNum: Identifier for the robot.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionParam structure to store the retrieved parameters.
  - Returns: Result status.
```

----------------------------------------

TITLE: Vision Range Functions
DESCRIPTION: Functions to get and set the range for the vision system. Includes general and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_v

LANGUAGE: APIDOC
CODE:
```
vision_get_range()
  Description: Retrieves the range settings of the vision system.
  Source: nrc_craft_vision.h

vision_get_range_robot()
  Description: Retrieves the range settings of the vision system for robot operations.
  Source: nrc_craft_vision.h

vision_set_range(range_settings)
  Description: Sets the range for the vision system.
  Parameters:
    range_settings: The range settings to apply.
  Source: nrc_craft_vision.h

vision_set_range_robot(range_settings)
  Description: Sets the range for the vision system for robot operations.
  Parameters:
    range_settings: The range settings to apply.
  Source: nrc_craft_vision.h
```

----------------------------------------

TITLE: Get Robot Queue Size
DESCRIPTION: Retrieves the current number of commands in the motion queue for a specific robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_size_robot(SOCKETFD socketFd, int robotNum, int &size)
```

----------------------------------------

TITLE: Search Functionality
DESCRIPTION: Demonstrates the search functionality within the documentation interface, including closing search results and handling search queries.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_w

LANGUAGE: javascript
CODE:
```
searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: Get Robot Configuration
DESCRIPTION: Fetches the configuration settings for a robot. This function returns an integer representing the robot's configuration state. It requires a socket file descriptor and a reference to an integer to store the configuration.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_robot_configuration(SOCKETFD socketFd, int& configuration)
  - Retrieves the configuration of the robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - configuration: A reference to an integer to store the robot's configuration.
  - Returns: Result code indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_robot_configuration_robot(SOCKETFD socketFd, int robotNum, int& configuration)
  - Retrieves the configuration of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier of the robot.
    - configuration: A reference to an integer to store the robot's configuration.
  - Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: Laser Cutting Analog Parameter Functions
DESCRIPTION: Provides functions to set and get analog parameters for laser cutting operations. Includes methods for specific robots and general settings.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam param)
  Sets the analog parameters for a specific robot in laser cutting.
  Parameters:
    socketFd: File descriptor for the socket connection.
    robotNum: The number of the robot to configure.
    param: A LaserCuttingAnalogParam structure containing the parameters to set.
  Returns: Result indicating success or failure.

laser_cutting_get_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam& param)
  Retrieves the current analog parameters for laser cutting.
  Parameters:
    socketFd: File descriptor for the socket connection.
    param: A reference to a LaserCuttingAnalogParam structure to store the retrieved parameters.
  Returns: Result indicating success or failure.

laser_cutting_get_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam& param)
  Retrieves the analog parameters for a specific robot in laser cutting.
  Parameters:
    socketFd: File descriptor for the socket connection.
    robotNum: The number of the robot to query.
    param: A reference to a LaserCuttingAnalogParam structure to store the retrieved parameters.
  Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Search Functionality
DESCRIPTION: Demonstrates the search functionality within the documentation interface, including closing search results and handling search queries.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_t

LANGUAGE: javascript
CODE:
```
searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: Track Record Status Functions
DESCRIPTION: Provides functions to get the track record status. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_track_record_status()
  Retrieves the status of track recording.
  Source: nrc_track.h

get_track_record_status_robot()
  Retrieves the status of track recording for the robot.
  Source: nrc_track.h
```

----------------------------------------

TITLE: Search Functionality
DESCRIPTION: Demonstrates the search functionality within the documentation interface, including closing search results and handling search queries.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_v

LANGUAGE: javascript
CODE:
```
searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: System Backup API
DESCRIPTION: Provides a function to back up the system. The backup is saved to the current execution directory.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result backup_system(SOCKETFD socketFd)
// 一键备份系统，会保存至当前执行程序目录下
```

----------------------------------------

TITLE: Laser Cutting Global Parameters
DESCRIPTION: Functions to set and get global parameters for laser cutting operations. Supports configurations for single or multiple robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_global_parameter(SOCKETFD socketFd, LaserCuttingGlobalParam param)
  Sets the global parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: The LaserCuttingGlobalParam structure containing global parameters.

laser_cutting_set_global_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingGlobalParam param)
  Sets the global parameters for laser cutting for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The robot number.
    param: The LaserCuttingGlobalParam structure containing global parameters.

laser_cutting_get_global_parameter(SOCKETFD socketFd, LaserCuttingGlobalParam &param)
  Queries the global parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: A reference to a LaserCuttingGlobalParam structure to store the queried global parameters.

laser_cutting_get_global_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingGlobalParam &param)
  Queries the global parameters for laser cutting for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The robot number.
    param: A reference to a LaserCuttingGlobalParam structure to store the queried global parameters.
```

----------------------------------------

TITLE: User Coordinate Number Functions
DESCRIPTION: Provides functions to get the user coordinate number. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_user_coord_number()
  Retrieves the user coordinate number.
  Source: nrc_interface.h

get_user_coord_number_robot()
  Retrieves the user coordinate number for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Search Functionality
DESCRIPTION: Demonstrates the search functionality within the documentation interface, including closing search results and handling search queries.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: javascript
CODE:
```
searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: Get Digital Output
DESCRIPTION: Retrieves the status of a digital output. Functions are provided for standard I/O and C interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_digital_output()
  - Retrieves the status of a digital output.
  - Source: nrc_io.h

get_digital_output_c()
  - Retrieves the status of a digital output using C interface.
  - Source: nrc_c_io.h
```

----------------------------------------

TITLE: Vision Range Functions
DESCRIPTION: Functions to get and set the range for the vision system. Includes general and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_v

LANGUAGE: APIDOC
CODE:
```
vision_get_range()
  Description: Retrieves the range settings of the vision system.
  Source: nrc_craft_vision.h

vision_get_range_robot()
  Description: Retrieves the range settings of the vision system for robot operations.
  Source: nrc_craft_vision.h

vision_set_range(range_settings)
  Description: Sets the range for the vision system.
  Parameters:
    range_settings: The range settings to apply.
  Source: nrc_craft_vision.h

vision_set_range_robot(range_settings)
  Description: Sets the range for the vision system for robot operations.
  Parameters:
    range_settings: The range settings to apply.
  Source: nrc_craft_vision.h
```

----------------------------------------

TITLE: Laser Cutting Equipment Parameters
DESCRIPTION: Contains parameters specific to laser cutting equipment, including IO settings and retreat distances.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_r

LANGUAGE: APIDOC
CODE:
```
LaserCuttingEquipment:
  rePerforate : Setting to re-perforate during laser cutting.
  RetreatDistance : The distance the laser head retreats after cutting.
```

----------------------------------------

TITLE: Search Functionality
DESCRIPTION: Demonstrates the search functionality within the documentation interface, including closing search results and handling search queries.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_q

LANGUAGE: javascript
CODE:
```
searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: Add Wave On Command to Queue
DESCRIPTION: Adds a WVON (wave on start) command to the local motion queue. This function is for single robot operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: APIDOC
CODE:
```
queue_motion_push_back_wave_on(SOCKETFD socketFd, int id)
  - Adds a WVON (wave on start) command to the local motion queue.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - id: An identifier for the command.
  - Returns: Result status of the operation.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result queue_motion_push_back_wave_on(SOCKETFD socketFd, int id)
// 队列运动模式的本地队列最后插入一条WVON(摆焊开始)指令
```

----------------------------------------

TITLE: Get Digital Input
DESCRIPTION: Retrieves the status of a digital input. Functions are provided for standard I/O and C interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_digital_input()
  - Retrieves the status of a digital input.
  - Source: nrc_io.h

get_digital_input_c()
  - Retrieves the status of a digital input using C interface.
  - Source: nrc_c_io.h
```

----------------------------------------

TITLE: Laser Cutting Craft Parameters
DESCRIPTION: Functions to set and get craft-specific parameters for laser cutting operations. Supports configurations for single or multiple robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam param)
  Sets the craft parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: The LaserCuttingCraftParam structure containing craft parameters.

laser_cutting_set_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam param)
  Sets the craft parameters for laser cutting for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The robot number.
    param: The LaserCuttingCraftParam structure containing craft parameters.

laser_cutting_get_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam &param)
  Queries the craft parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: A reference to a LaserCuttingCraftParam structure to store the queried craft parameters.

laser_cutting_get_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam &param)
  Queries the craft parameters for laser cutting for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The robot number.
    param: A reference to a LaserCuttingCraftParam structure to store the queried craft parameters.
```

----------------------------------------

TITLE: Laser Cutting Craft Parameter Functions
DESCRIPTION: Provides functions to get and set craft parameters for laser cutting. These functions are used to manage specific settings related to the crafting process and include robot-specific versions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_l

LANGUAGE: APIDOC
CODE:
```
laser_cutting_get_craft_parameter()
  - Retrieves the current craft parameter for laser cutting.
  - Related functions: laser_cutting_get_craft_parameter_robot(), laser_cutting_set_craft_parameter()

laser_cutting_get_craft_parameter_robot()
  - Retrieves the current craft parameter for laser cutting, specifically for robot control.
  - Related functions: laser_cutting_get_craft_parameter(), laser_cutting_set_craft_parameter_robot()

laser_cutting_set_craft_parameter(parameter_value)
  - Sets the craft parameter for laser cutting.
  - Parameters:
    - parameter_value: The value to set for the craft parameter.
  - Related functions: laser_cutting_get_craft_parameter(), laser_cutting_set_craft_parameter_robot()

laser_cutting_set_craft_parameter_robot(parameter_value)
  - Sets the craft parameter for laser cutting, specifically for robot control.
  - Parameters:
    - parameter_value: The value to set for the craft parameter.
  - Related functions: laser_cutting_get_craft_parameter_robot(), laser_cutting_set_craft_parameter()
```

----------------------------------------

TITLE: Get Single Cycle Data
DESCRIPTION: Retrieves single cycle data for the current configuration. The data is returned in a vector of doubles.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_single_cycle(SOCKETFD socketFd, std::vector<double>& single_cycle)
  - Retrieves single cycle data.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - single_cycle: A reference to a vector of doubles to store the cycle data.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Vision Position Parameter Functions
DESCRIPTION: Functions to get and set positional parameters for the vision system. Includes general and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_v

LANGUAGE: APIDOC
CODE:
```
vision_get_position_parameter()
  Description: Retrieves the positional parameters of the vision system.
  Source: nrc_craft_vision.h

vision_get_position_parameter_robot()
  Description: Retrieves the positional parameters of the vision system for robot operations.
  Source: nrc_craft_vision.h

vision_set_position_parameter(params)
  Description: Sets the positional parameters for the vision system.
  Parameters:
    params: The positional parameters to set.
  Source: nrc_craft_vision.h

vision_set_position_parameter_robot(params)
  Description: Sets the positional parameters for the vision system for robot operations.
  Parameters:
    params: The positional parameters to set.
  Source: nrc_craft_vision.h
```

----------------------------------------

TITLE: Get Digital Output Data
DESCRIPTION: Retrieves all digital output values at once. The results are stored in an integer vector of size 64.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_digital_output(_socketFd_: SOCKETFD, _out_: std::vector< int > &)
  - Retrieves all digital output values simultaneously.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _out_: Vector to store digital output values (size 64).
```

LANGUAGE: C++
CODE:
```
Result get_digital_output(SOCKETFD _socketFd_, std::vector< int > & _out_)
```

----------------------------------------

TITLE: Tool Hand Number Functions
DESCRIPTION: Provides functions to get the tool hand number. Includes a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_tool_hand_number()
  Retrieves the tool hand number.
  Source: nrc_interface.h

get_tool_hand_number_robot()
  Retrieves the tool hand number for the robot.
  Source: nrc_interface.h
```

----------------------------------------

TITLE: Job Step Execution
DESCRIPTION: Executes a specific step within a job. Requires a socket file descriptor, job name, and the line number of the step.

SOURCE: https://doc.hmilib.inexbot.coision.cn/test

LANGUAGE: cpp
CODE:
```
job_step(SOCKETFD socketFd, const std::string &jobName, int line)
```

----------------------------------------

TITLE: Laser Cutting Functions
DESCRIPTION: Provides functions to get and set various parameters for laser cutting operations, including analog, craft, global, and I/O parameters. Some functions are specifically designed for robot integration.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_l

LANGUAGE: c
CODE:
```
int laser_cutting_get_analog_parameter();
int laser_cutting_get_analog_parameter_robot();
int laser_cutting_get_craft_parameter();
int laser_cutting_get_craft_parameter_robot();
int laser_cutting_get_global_parameter();
int laser_cutting_get_global_parameter_robot();
int laser_cutting_get_io_parameter();
int laser_cutting_get_io_parameter_robot();
int laser_cutting_set_analog_parameter(int parameter);
int laser_cutting_set_analog_parameter_robot(int parameter);
int laser_cutting_set_craft_parameter(int parameter);
int laser_cutting_set_craft_parameter_robot(int parameter);
int laser_cutting_set_global_parameter(int parameter);
int laser_cutting_set_global_parameter_robot(int parameter);
int laser_cutting_set_io_parameter(int parameter);
int laser_cutting_set_io_parameter_robot(int parameter);
```

----------------------------------------

TITLE: Get Digital Input Data
DESCRIPTION: Retrieves all digital input values at once. The results are stored in an integer vector of size 64.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_digital_input(_socketFd_: SOCKETFD, _in_: std::vector< int > &)
  - Retrieves all digital input values simultaneously.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _in_: Vector to store digital input values (size 64).
```

LANGUAGE: C++
CODE:
```
Result get_digital_input(SOCKETFD _socketFd_, std::vector< int > & _in_)
```

----------------------------------------

TITLE: Get Force Digital Input
DESCRIPTION: Retrieves the forced status of a digital input. This function is part of the I/O module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_force_digital_input()
  - Retrieves the forced status of a digital input.
  - Source: nrc_io.h
```

----------------------------------------

TITLE: Robot Reset and Home Positioning Commands
DESCRIPTION: API documentation for functions that move the robot to its predefined reset or home positions. This includes commands for both single robots and specific robots within a multi-robot system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
robot_go_to_reset_position(SOCKETFD socketFd)
  Moves the robot to its configured reset position.
  Parameters:
    socketFd: The socket file descriptor for communication.
  Returns: Result code indicating success or failure.

robot_go_to_reset_position_robot(SOCKETFD socketFd, int robotNum)
  Moves a specific robot to its configured reset position.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The number of the robot to control.
  Returns: Result code indicating success or failure.

robot_go_home(SOCKETFD socketFd)
  Moves the robot to its configured home position.
  Parameters:
    socketFd: The socket file descriptor for communication.
  Returns: Result code indicating success or failure.

robot_go_home_robot(SOCKETFD socketFd, int robotNum)
  Moves a specific robot to its configured home position.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The number of the robot to control.
  Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: Vision Position Parameter Functions
DESCRIPTION: Functions to get and set positional parameters for the vision system. Includes general and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_v

LANGUAGE: APIDOC
CODE:
```
vision_get_position_parameter()
  Description: Retrieves the positional parameters of the vision system.
  Source: nrc_craft_vision.h

vision_get_position_parameter_robot()
  Description: Retrieves the positional parameters of the vision system for robot operations.
  Source: nrc_craft_vision.h

vision_set_position_parameter(params)
  Description: Sets the positional parameters for the vision system.
  Parameters:
    params: The positional parameters to set.
  Source: nrc_craft_vision.h

vision_set_position_parameter_robot(params)
  Description: Sets the positional parameters for the vision system for robot operations.
  Parameters:
    params: The positional parameters to set.
  Source: nrc_craft_vision.h
```

----------------------------------------

TITLE: Get Current Position
DESCRIPTION: Retrieves the current position of the robot. Functions are provided for standard, C, and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_current_position()
  - Retrieves the current position of the robot.
  - Source: nrc_interface.h

get_current_position_c()
  - Retrieves the current position of the robot using C interface.
  - Source: nrc_c_interface.h

get_current_position_robot()
  - Retrieves the current position of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Laser Cutting Global Parameter Functions
DESCRIPTION: Provides functions to get and set global parameters for laser cutting. These functions manage system-wide settings and include robot-specific variants for robot control.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_l

LANGUAGE: APIDOC
CODE:
```
laser_cutting_get_global_parameter()
  - Retrieves the current global parameter for laser cutting.
  - Related functions: laser_cutting_get_global_parameter_robot(), laser_cutting_set_global_parameter()

laser_cutting_get_global_parameter_robot()
  - Retrieves the current global parameter for laser cutting, specifically for robot control.
  - Related functions: laser_cutting_get_global_parameter(), laser_cutting_set_global_parameter_robot()

laser_cutting_set_global_parameter(parameter_value)
  - Sets the global parameter for laser cutting.
  - Parameters:
    - parameter_value: The value to set for the global parameter.
  - Related functions: laser_cutting_get_global_parameter(), laser_cutting_set_global_parameter_robot()

laser_cutting_set_global_parameter_robot(parameter_value)
  - Sets the global parameter for laser cutting, specifically for robot control.
  - Parameters:
    - parameter_value: The value to set for the global parameter.
  - Related functions: laser_cutting_get_global_parameter_robot(), laser_cutting_set_global_parameter()
```

----------------------------------------

TITLE: Add Vision Craft Start Robot Command to Queue
DESCRIPTION: Appends a 'vision_craft_start_robot' command to the motion queue for a specific robot, initiating a vision process. Requires a socket file descriptor, robot number, and ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c++
CODE:
```
EXPORT_API Result queue_motion_push_back_vision_craft_start_robot(SOCKETFD socketFd, int robotNum, int id)
```

----------------------------------------

TITLE: Get Vision Craft Position
DESCRIPTION: Retrieves the position data for a vision craft instruction, identified by line and ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_insert_vision_craft_get_pos(SOCKETFD socketFd, int line, int id, const std::string posName);
```

----------------------------------------

TITLE: Get Current Mode
DESCRIPTION: Retrieves the current operating mode of the robot. Functions are provided for standard and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_current_mode()
  - Retrieves the current operating mode of the robot.
  - Source: nrc_interface.h

get_current_mode_robot()
  - Retrieves the current operating mode of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Get Current Coordinate
DESCRIPTION: Retrieves the current coordinate of the robot. Functions are provided for standard, C, and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_current_coord()
  - Retrieves the current coordinate of the robot.
  - Source: nrc_interface.h

get_current_coord_c()
  - Retrieves the current coordinate of the robot using C interface.
  - Source: nrc_c_interface.h

get_current_coord_robot()
  - Retrieves the current coordinate of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: IO and Remote Control Functions
DESCRIPTION: Provides functions for interacting with the robot's Input/Output (IO) system and remote control functionalities. This includes getting hard enable ports, IO reset functions, and accessing remote parameters and programs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_hard_enable_port()
  Retrieves the status of the hard enable port.

get_IO_reset_function()
  Resets the IO functions of the robot.

get_remote_function()
  Retrieves information about a remote function.

get_remote_param()
  Retrieves a remote parameter.

get_remote_program()
  Retrieves a remote program.

get_remote_status_tips()
  Retrieves status tips for remote operations.

get_safe_IO_function()
  Retrieves information about safe IO functions.
```

----------------------------------------

TITLE: backup_system Function
DESCRIPTION: This function, `backup_system`, is part of the net_lib library. Its specific functionality and parameters are detailed in the `nrc_job_operate.h` header file.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals

LANGUAGE: javascript
CODE:
```
backup_system()
```

----------------------------------------

TITLE: Laser Cutting Global Parameter Management
DESCRIPTION: APIs for managing global laser cutting parameters. Includes functions to set and get global parameters for specific robots and general settings. These functions interact with the laser cutting system to configure its overall behavior.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_global_parameter(SOCKETFD socketFd, LaserCuttingGlobalParam param)
  - Sets the global parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A LaserCuttingGlobalParam structure containing the global parameters.
  - Returns: Result status of the operation.

laser_cutting_get_global_parameter(SOCKETFD socketFd, LaserCuttingGlobalParam &param)
  - Retrieves the global parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A reference to a LaserCuttingGlobalParam structure to store the retrieved global parameters.
  - Returns: Result status of the operation.

laser_cutting_set_global_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingGlobalParam param)
  - Sets the global parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to configure.
    - param: A LaserCuttingGlobalParam structure containing the global parameters.
  - Returns: Result status of the operation.

laser_cutting_get_global_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingGlobalParam &param)
  - Retrieves the global parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to retrieve parameters from.
    - param: A reference to a LaserCuttingGlobalParam structure to store the retrieved global parameters.
  - Returns: Result status of the operation.

Related Types:
  - SOCKETFD: Integer representing a socket file descriptor.
  - LaserCuttingGlobalParam: Structure holding global laser cutting parameters.
  - Result: Enum or type indicating the success or failure of an operation.
```

----------------------------------------

TITLE: I/O and Timer Commands Insertion
DESCRIPTION: Functions for inserting I/O output commands and timer commands into a job. Robot-specific versions are available for both.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_j

LANGUAGE: C++
CODE:
```
job_insert_io_out_command()
job_insert_io_out_command_robot()
job_insert_timer_command()
job_insert_timer_command_robot()
```

----------------------------------------

TITLE: Conveyor Belt Identification Parameters
DESCRIPTION: Functions to get identification parameters for the conveyor belt, with separate implementations for direct interface and robot interaction.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_c

LANGUAGE: c
CODE:
```
conveyor_belt_get_identification_paramters();
conveyor_belt_get_identification_paramters_robot();
```

----------------------------------------

TITLE: Class Members - i
DESCRIPTION: Lists class members starting with 'i', including their associated structures and links to their definitions. This section details variables like identification_communication, identification_sensorTrg, identification_type, initialDir, input_port, intervals, io, IOPort, IP, and isWait.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_i

LANGUAGE: APIDOC
CODE:
```
identification_communication : ConveyorIdentificationParams
identification_sensorTrg : ConveyorIdentificationParams
identification_type : ConveyorIdentificationParams
initialDir : WaveParam
input_port : Fault
intervals : Trigger
io : LaserCuttingIOParam
IOPort : Trigger
IP : ModbusTCPParameter, Socket
isWait : ConveyorWaitPointParams
```

----------------------------------------

TITLE: Get Current Speed for Robot
DESCRIPTION: Retrieves the current speed for a specific robot. The speed is returned as an integer between 1 and 100.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_speed_robot(SOCKETFD _socketFd_, int _robotNum_, int & _speed_)
  - Retrieves the current speed for a specific robot.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _robotNum_: The robot number.
    - _speed_: A reference to an integer where the speed will be stored. Parameter range: 0 < speed <= 100.
```

LANGUAGE: C++
CODE:
```
Result get_speed_robot(SOCKETFD _socketFd_, int _robotNum_, int & _speed_)
```

----------------------------------------

TITLE: Conveyor Belt Sensor Parameters
DESCRIPTION: Functions to get sensor parameters for the conveyor belt, with separate implementations for direct interface and robot interaction.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_c

LANGUAGE: c
CODE:
```
conveyor_belt_get_sensor_paramters();
conveyor_belt_get_sensor_paramters_robot();
```

----------------------------------------

TITLE: IO Command Parameters
DESCRIPTION: Parameters related to Input/Output commands, potentially used for controlling robot actions or receiving status. Includes parameters for grouping numerical values, time, and general values.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_p

LANGUAGE: APIDOC
CODE:
```
IOCommandParams:
  paraGroupNum: Parameters for a group of numerical IO commands.
  paraGroupTime: Parameters for a group of time-based IO commands.
  paraGroupValue: Parameters for a group of general IO commands.
```

----------------------------------------

TITLE: Callback and Message Handling Functions
DESCRIPTION: APIs for setting up callback functions to receive error or warning messages from the robot system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_receive_error_or_warnning_message_callback(SOCKETFD socketFd, void(*function)(int messageType, const char *message, int messageCode))
  Sets a callback function to receive error or warning messages.
  Parameters:
    socketFd: The socket file descriptor for communication.
    function: A pointer to the callback function. The function should accept:
      - messageType: Type of the message (e.g., error, warning).
      - message: The message string.
      - messageCode: The code associated with the message.
```

----------------------------------------

TITLE: Robot State and Configuration Functions
DESCRIPTION: Functions for managing robot servo states, current modes, and global position synchronization.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_servo_state(socketFd, state)
  - Sets the servo state for the robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - state: The desired servo state (e.g., ON, OFF).
  - Returns: Result status.

set_current_mode_robot(socketFd, robotNum, mode)
  - Sets the current operating mode for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - mode: The desired operating mode (e.g., TEACH, RUN, REMOTE).
  - Returns: Result status.

set_global_sync_position(socketFd, posName, posInfo)
  - Sets a global synchronized position.
  - Parameters:
    - socketFd: The socket file descriptor.
    - posName: The name of the position.
    - posInfo: The position data.
  - Returns: Result status.

set_global_sync_position_robot(socketFd, robotNum, posName, posInfo)
  - Sets a global synchronized position for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - posName: The name of the position.
    - posInfo: The position data.
  - Returns: Result status.

set_servo_poweron_robot(socketFd, robotNum)
  - Powers on the servos for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
  - Returns: Result status.
```

----------------------------------------

TITLE: SafeIO Structure Fields
DESCRIPTION: Defines the members of the SafeIO structure, which manages safety-related input and output configurations. Includes various quick stop and screen enable/disable parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h_source

LANGUAGE: APIDOC
CODE:
```
SafeIO:
  quickStopShied2: bool
    Description: Shielding status for quick stop, second instance.
    Definition: nrc_io_parameter.h:48
  quickStopValue1: int
    Description: Value for quick stop condition 1.
    Definition: nrc_io_parameter.h:44
  quickStopEnable: bool
    Description: Enables or disables the quick stop functionality.
    Definition: nrc_io_parameter.h:46
  quickStopTime: double
    Description: Duration for the quick stop action.
    Definition: nrc_io_parameter.h:49
  screenEnable: bool
    Description: Enables or disables screen output.
    Definition: nrc_io_parameter.h:56
  screenPort1: int
    Description: Port number for screen output 1.
    Definition: nrc_io_parameter.h:52
  screenValue2: int
    Description: Value for screen output 2.
    Definition: nrc_io_parameter.h:55
  quickStopPort2: int
    Description: Port number for quick stop condition 2.
    Definition: nrc_io_parameter.h:43
  screenValue1: int
    Description: Value for screen output 1.
    Definition: nrc_io_parameter.h:54
  quickStopShied1: bool
    Description: Shielding status for quick stop, first instance.
    Definition: nrc_io_parameter.h:47
  quickStopPort1: int
    Description: Port number for quick stop condition 1.
    Definition: nrc_io_parameter.h:42
  quickStopValue2: int
    Description: Value for quick stop condition 2.
    Definition: nrc_io_parameter.h:45
  quickStopShiedTime: int
    Description: Time duration for quick stop shielding.
    Definition: nrc_io_parameter.h:50
  screenPort2: int
    Description: Port number for screen output 2.
    Definition: nrc_io_parameter.h:53
```

----------------------------------------

TITLE: Calibration Methods and Members
DESCRIPTION: Details the members and methods of the Calibration structure, including accessors for calibration points, adding calibration points, and the constructor. It also lists the definition locations within the header file.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision__parameter_8h_source

LANGUAGE: APIDOC
CODE:
```
Calibration:
  Definition: nrc_craft_vision_parameter.h:94

  Members:
    calibrated: bool - Indicates if calibration is complete. Defined in nrc_craft_vision_parameter.h:95.
    point_num: int - The number of calibration points. Defined in nrc_craft_vision_parameter.h:97.
    point: std::vector<CalibrationPoint> - A vector storing calibration points. Defined in nrc_craft_vision_parameter.h:96.

  Methods:
    Calibration(int num_points = 6): Constructor for the Calibration structure. Initializes with a specified number of points. Defined in nrc_craft_vision_parameter.h:110.
    addCalibrationPoint(const CalibrationPoint &pos): void - Adds a calibration point to the structure. Defined in nrc_craft_vision_parameter.h:99.
    getPoint(int index) const: CalibrationPoint - Retrieves a calibration point by its index. Defined in nrc_craft_vision_parameter.h:103.

```

----------------------------------------

TITLE: Get Current Motor Torque
DESCRIPTION: Retrieves the current motor torque of the robot. Functions are provided for standard and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_torque()
  - Retrieves the current motor torque of the robot.
  - Source: nrc_interface.h

get_curretn_motor_torque_robot()
  - Retrieves the current motor torque of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Get Current Robot Coordinate System
DESCRIPTION: Retrieves the current coordinate system of the robot. Requires a valid socket file descriptor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_current_coord_c(SOCKETFD _socketFd_, int & _coord_)
  - Gets the robot's current coordinate system.
  - Parameters:
    - _socketFd_: The socket file descriptor for communication.
    - _coord_: Output parameter to store the coordinate system (0: Joint, 1: Cartesian, 2: Tool, 3: User).
  - Returns: An integer status code.
```

----------------------------------------

TITLE: nrc_job_operate.h API Reference
DESCRIPTION: This section provides the API documentation for the nrc_job_operate.h file, detailing functions related to job operations within the net_lib. It includes function signatures, parameter descriptions, and return values.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
nrc_job_operate.h File Reference

Includes:
- parameter/nrc_define.h
- <string>

Provides a link to the source code of the file.
```

----------------------------------------

TITLE: Get Current Motor Speed
DESCRIPTION: Retrieves the current motor speed of the robot. Functions are provided for standard and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_speed()
  - Retrieves the current motor speed of the robot.
  - Source: nrc_interface.h

get_curretn_motor_speed_robot()
  - Retrieves the current motor speed of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Get Current Motor Payload
DESCRIPTION: Retrieves the current motor payload of the robot. Functions are provided for standard and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_payload()
  - Retrieves the current motor payload of the robot.
  - Source: nrc_interface.h

get_curretn_motor_payload_robot()
  - Retrieves the current motor payload of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Laser Cutting Parameter References
DESCRIPTION: References to structures related to laser cutting operations, including fault status, power settings, and analog matching.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_l

LANGUAGE: APIDOC
CODE:
```
LaserCuttingIOParam:
  - laser_fault: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_i_o_param.html#ac6d5ed5ba3ba963d9496d7ab1ce9fe29

LaserCuttingAnalogMatch:
  - laserPower: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_analog_match.html#a23f6b31f71a909af71789131d7803261
```

----------------------------------------

TITLE: Get Controller ID
DESCRIPTION: Retrieves the unique identifier for the controller. This function is available in both standard and C# interfaces, with robot-specific variants.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_controller_id()
  - Retrieves the controller's unique identifier.
  - Source: nrc_interface.h

get_controller_id_csharp()
  - Retrieves the controller's unique identifier using C# interface.
  - Source: nrc_interface.h

get_controller_id_csharp_robot()
  - Retrieves the controller's unique identifier for a robot using C# interface.
  - Source: nrc_interface.h

get_controller_id_robot()
  - Retrieves the controller's unique identifier for a robot.
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Global Variable Management
DESCRIPTION: APIs for getting and setting global variables within the system. These variables can store and retrieve double-precision floating-point values.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_global_variant(SOCKETFD socketFd, const std::string &varName, double &vaule)
  查询全局变量

set_global_variant_robot(SOCKETFD socketFd, int robotNum, const std::string &varName, double varValue)
  Sets a global variable for a specific robot.
```

----------------------------------------

TITLE: Robot Jogging Control
DESCRIPTION: Functions to start and stop robot jogging for a specific axis. Supports single robot or multi-robot configurations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: c
CODE:
```
Result robot_start_jogging(SOCKETFD socketFd, int axis, bool dir);
Result robot_start_jogging_robot(SOCKETFD socketFd, int robotNum, int axis, bool dir);
Result robot_stop_jogging(SOCKETFD socketFd, int axis);
Result robot_stop_jogging_robot(SOCKETFD socketFd, int robotNum, int axis);
```

----------------------------------------

TITLE: Get Remote Status Tips
DESCRIPTION: Retrieves status tips for a remote system. It takes a socket file descriptor, robot number, and output parameters for the number of programs, outage port, and outage value. It also returns a vector of RemoteProgram objects.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result get_remote_status_tips(SOCKETFD socketFd, int robotNum, int& num, int& outagePort, int& outageValue, std::vector<RemoteProgram>& program);
```

----------------------------------------

TITLE: Laser Cutting Analog Parameter Functions
DESCRIPTION: Provides functions to get and set analog parameters for laser cutting. These functions interact with the laser cutting hardware and may have robot-specific variants.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_l

LANGUAGE: APIDOC
CODE:
```
laser_cutting_get_analog_parameter()
  - Retrieves the current analog parameter for laser cutting.
  - Related functions: laser_cutting_get_analog_parameter_robot(), laser_cutting_set_analog_parameter()

laser_cutting_get_analog_parameter_robot()
  - Retrieves the current analog parameter for laser cutting, specifically for robot control.
  - Related functions: laser_cutting_get_analog_parameter(), laser_cutting_set_analog_parameter_robot()

laser_cutting_set_analog_parameter(parameter_value)
  - Sets the analog parameter for laser cutting.
  - Parameters:
    - parameter_value: The value to set for the analog parameter.
  - Related functions: laser_cutting_get_analog_parameter(), laser_cutting_set_analog_parameter_robot()

laser_cutting_set_analog_parameter_robot(parameter_value)
  - Sets the analog parameter for laser cutting, specifically for robot control.
  - Parameters:
    - parameter_value: The value to set for the analog parameter.
  - Related functions: laser_cutting_get_analog_parameter_robot(), laser_cutting_set_analog_parameter()
```

----------------------------------------

TITLE: Weld Parameter Functions
DESCRIPTION: Provides functions to get and set welding parameters, specifically focusing on wave welding parameters. These functions allow for fine-tuning the welding process by adjusting specific wave parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_w

LANGUAGE: APIDOC
CODE:
```
weld_get_wave_weld_param()
  - Retrieves the general wave welding parameters.
  - Returns: General wave welding parameters.

weld_get_wave_weld_param_robot()
  - Retrieves the robot-specific wave welding parameters.
  - Returns: Robot-specific wave welding parameters.

weld_set_wave_weld_param(params)
  - Sets the general wave welding parameters.
  - Parameters:
    - params: The wave welding parameters to set.
  - Returns: Status of the operation.

weld_set_wave_weld_param_robot(params)
  - Sets the robot-specific wave welding parameters.
  - Parameters:
    - params: The robot-specific wave welding parameters to set.
  - Returns: Status of the operation.
```

----------------------------------------

TITLE: IO Command Parameters
DESCRIPTION: Parameters related to Input/Output commands, potentially used for controlling robot actions or receiving status. Includes parameters for grouping numerical values, time, and general values.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_p

LANGUAGE: APIDOC
CODE:
```
IOCommandParams:
  paraGroupNum: Parameters for a group of numerical IO commands.
  paraGroupTime: Parameters for a group of time-based IO commands.
  paraGroupValue: Parameters for a group of general IO commands.
```

----------------------------------------

TITLE: Robot Speed Control
DESCRIPTION: Functions to get and set the speed of a robot. Requires a socket file descriptor, robot number, and speed value.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_speed_robot(SOCKETFD socketFd, int robotNum, int &speed)
  - Retrieves the current speed of a specified robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - speed: Output parameter to store the retrieved speed.
  - Returns: Result indicating success or failure.
```

LANGUAGE: C++
CODE:
```
int get_speed_robot(SOCKETFD socketFd, int robotNum, int &speed);
```

----------------------------------------

TITLE: SafeIO Structure Parameters
DESCRIPTION: Defines parameters related to the SafeIO structure, including quick stop and safety light curtain configurations. These parameters control the behavior and enable/disable states of safety features.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h_source

LANGUAGE: c++
CODE:
```
struct SafeIO {
    int quickStopValue1; // Emergency stop parameter 1 (0/1)
    int quickStopValue2; // Emergency stop parameter 2 (0/1)
    bool quickStopEnable; // Emergency stop enable
    bool quickStopShied1; // Shield emergency stop 1
    bool quickStopShied2; // Shield emergency stop 2
    double quickStopTime; // Quick stop time, unit milliseconds (ms) range [50,100]
    int quickStopShiedTime; // Shield emergency stop time, unit seconds (s)
    int screenPort1; // Safety light curtain port 1
    int screenPort2; // Safety light curtain port 2
    int screenValue1; // Safety light curtain parameter 1 (0/1)
    int screenValue2; // Safety light curtain parameter 2 (0/1)
    bool screenEnable; // Safety light curtain enable
};
```

----------------------------------------

TITLE: Robot Configuration Retrieval
DESCRIPTION: Retrieves the configuration of a robot. This function takes a socket file descriptor and returns the robot's configuration via an integer reference. An example specifies configuration values for a 4-axis Sacra robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_robot_configuration(_socketFd: int, _configuration: int &)
  - Retrieves the configuration of a robot.
  - Parameters:
    - _socketFd: File descriptor for the socket connection.
    - _configuration: An integer reference to store the robot's configuration.
  - Example:
    - For a 4-axis Sacra robot, configuration can be 1 (left hand) or 2 (right hand).
```

----------------------------------------

TITLE: Add WAVON command to queue
DESCRIPTION: Appends a WAVON (wave soldering start) command to the local motion queue. Requires a socket file descriptor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_wave_on(SOCKETFD socketFd, int id)
```

----------------------------------------

TITLE: Tool/Hand Assignment Functions
DESCRIPTION: Manages the assignment of tool or hand numbers for the robot. Supports setting and getting these assignments for both general and robot-specific configurations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_tool_hand_number(SOCKETFD socketFd, int toolNum)
  - Sets the tool/hand number.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - toolNum: The tool/hand number to set.
  - Returns: Result code indicating success or failure.

set_tool_hand_number_robot(SOCKETFD socketFd, int robotNum, int toolNum)
  - Sets the tool/hand number for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier of the robot.
    - toolNum: The tool/hand number to set.
  - Returns: Result code indicating success or failure.

get_tool_hand_number(SOCKETFD socketFd, int& toolNum)
  - Retrieves the current tool/hand number.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - toolNum: An integer reference to store the tool/hand number.
  - Returns: Result code indicating success or failure.

get_tool_hand_number_robot(SOCKETFD socketFd, int robotNum, int& toolNum)
  - Retrieves the current tool/hand number for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier of the robot.
    - toolNum: An integer reference to store the tool/hand number.
  - Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: Class Members - LaserCuttingEquipment
DESCRIPTION: Lists variables associated with the LaserCuttingEquipment class, including waitFollowTime and waitLiftUpTime.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_w

LANGUAGE: APIDOC
CODE:
```
waitFollowTime: LaserCuttingEquipment
  - Description: Related to the waiting time for following.
  - Type: (Not specified)

waitLiftUpTime: LaserCuttingEquipment
  - Description: Related to the waiting time for lifting up.
  - Type: (Not specified)
```

----------------------------------------

TITLE: Get Four Point Data
DESCRIPTION: Retrieves four-point data, likely related to calibration or measurement. The results are stored in a vector of doubles.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_four_point(SOCKETFD socketFd, std::vector<double>& result)
  - Retrieves four-point data.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - result: A reference to a vector of doubles to store the four-point data.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Interface Parameters
DESCRIPTION: Defines various interface parameters for robot configuration and operation.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_s

LANGUAGE: APIDOC
CODE:
```
SEVEN_AXLE_GENERAL:
  Description: Represents a general configuration for a seven-axle robot.
  Type: Enum or Constant
  Usage: Used in functions that require specifying the robot's axle configuration.

SIX_AXLE_ABNORMITY:
  Description: Indicates an abnormality or specific state for a six-axle robot.
  Type: Enum or Constant
  Usage: Likely used for error handling or specific operational modes.

SIX_AXLE_ABNORMITY_3:
  Description: Another specific abnormality or state for a six-axle robot, possibly a variant of SIX_AXLE_ABNORMITY.
  Type: Enum or Constant
  Usage: Used for detailed abnormality reporting or handling.

SIX_AXLE_GENERAL:
  Description: Represents a general configuration for a six-axle robot.
  Type: Enum or Constant
  Usage: Used in functions that require specifying the robot's axle configuration.
```

----------------------------------------

TITLE: Get Current Motor Speed
DESCRIPTION: Retrieves the current motor speed values. This function provides real-time speed data for all motors.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_speed(SOCKETFD socketFd, std::vector< int > &motorSpeed, std::vector< int > &motorSpeedSync)
  - Retrieves the current motor speed values.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - motorSpeed: A reference to a vector of integers to store the primary motor speed values.
    - motorSpeedSync: A reference to a vector of integers to store synchronized motor speed values.
  - Returns: void (Speed values are populated in the 'motorSpeed' and 'motorSpeedSync' parameters)
```

----------------------------------------

TITLE: Get Sensor Data
DESCRIPTION: Fetches sensor data from a controller via the given socket file descriptor. The sensor data is returned as a vector of integers.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_sensor_data(SOCKETFD socketFd, std::vector<int>& data);
```

----------------------------------------

TITLE: HMI Library Structures and Parameters
DESCRIPTION: This section lists various structures and their associated parameters used within the HMI library. It covers configurations for tools, movement, calibration, protocols, robot kinematics, laser cutting, welding, and collision detection.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions

LANGUAGE: APIDOC
CODE:
```
ToolParam:
  - A: Reference to ToolParam structure.

MoveCmd:
  - acc: Acceleration parameter for movement commands.

Calibration:
  - addCalibrationPoint(): Function to add a calibration point.

Protocol:
  - addDataInitialPara: Parameter for initial data in a protocol.
  - addDataNum: Parameter for the number of data points in a protocol.
  - angleUnit: Specifies the unit for angles in the protocol.

RobotDHParam:
  - amplificationRatio: Amplification ratio for robot's Denavit-Hartenberg parameters.

LaserCuttingAnalogParam:
  - analogMatch: Analog matching parameter for laser cutting.

Excursion:
  - angle: Angle measurement related to excursion.

Position:
  - angleDirection: Direction of the angle in a position.

IO:
  - AO_laserPower: Analog output for laser power.
  - AO_pressure: Analog output for pressure.

WeldState:
  - arcingSuccess: Indicates the success status of arcing.

ArcParam:
  - arcOffCurrent: Current setting when arc is off.
  - arcOffRampEnable: Enable/disable ramp for arc off.
  - arcOffRampMode: Mode for arc off ramp.
  - arcOffRampTime: Time duration for arc off ramp.
  - arcOffTime: Duration for which the arc is off.
  - arcOffVoltage: Voltage setting when arc is off.
  - arcOnCurrent: Current setting when arc is on.
  - arcOnRampEnable: Enable/disable ramp for arc on.
  - arcOnRampMode: Mode for arc on ramp.
  - arcOnRampTime: Time duration for arc on ramp.
  - arcOnTime: Duration for which the arc is on.
  - arcOnVoltage: Voltage setting when arc is on.

LaserCuttingEquipment:
  - arrivalOutLightMode: Light mode upon arrival at the output.

CollisionPara:
  - axisum: Sum of axis values related to collision.
```

----------------------------------------

TITLE: Get Current Motor Torque
DESCRIPTION: Retrieves the current motor torque values. This function provides real-time torque data for all motors.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_torque(SOCKETFD socketFd, std::vector< int > &motorTorque, std::vector< int > &motorTorqueSync)
  - Retrieves the current motor torque values.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - motorTorque: A reference to a vector of integers to store the primary motor torque values.
    - motorTorqueSync: A reference to a vector of integers to store synchronized motor torque values.
  - Returns: void (Torque values are populated in the 'motorTorque' and 'motorTorqueSync' parameters)
```

----------------------------------------

TITLE: Laser Cutting Craft Parameter Management
DESCRIPTION: APIs for managing craft-specific laser cutting parameters. Includes functions to set and get craft parameters for specific robots and general settings. These functions are used to configure parameters related to the cutting process itself.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam param)
  - Sets the craft parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A LaserCuttingCraftParam structure containing the craft parameters.
  - Returns: Result status of the operation.

laser_cutting_get_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam &param)
  - Retrieves the craft parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A reference to a LaserCuttingCraftParam structure to store the retrieved craft parameters.
  - Returns: Result status of the operation.

laser_cutting_set_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam param)
  - Sets the craft parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to configure.
    - param: A LaserCuttingCraftParam structure containing the craft parameters.
  - Returns: Result status of the operation.

laser_cutting_get_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam &param)
  - Retrieves the craft parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to retrieve parameters from.
    - param: A reference to a LaserCuttingCraftParam structure to store the retrieved craft parameters.
  - Returns: Result status of the operation.

Related Types:
  - SOCKETFD: Integer representing a socket file descriptor.
  - LaserCuttingCraftParam: Structure holding craft-specific laser cutting parameters.
  - Result: Enum or type indicating the success or failure of an operation.
```

----------------------------------------

TITLE: Connect to Robot
DESCRIPTION: Establishes a connection to a robot using its IP address and port. This function returns a socket file descriptor for the established connection.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
SOCKETFD connect_robot(const std::string& ip, const std::string& port);
```

----------------------------------------

TITLE: Vision Range Functions
DESCRIPTION: Provides functions to set and get the range parameters for vision systems. Supports both general vision and robot-specific configurations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h_source

LANGUAGE: APIDOC
CODE:
```
vision_set_range(SOCKETFD socketFd, int visionNum, VisionRange vsPamrm)
  Sets the range parameters for a specified vision system.
  Parameters:
    socketFd: The socket file descriptor for communication.
    visionNum: The identifier for the vision system.
    vsPamrm: A VisionRange structure containing the range parameters to set.
  Returns: Result code indicating success or failure.

vision_set_range_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionRange vsPamrm)
  Sets the range parameters for a specified vision system associated with a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The identifier for the robot.
    visionNum: The identifier for the vision system.
    vsPamrm: A VisionRange structure containing the range parameters to set.
  Returns: Result code indicating success or failure.

vision_get_range(SOCKETFD socketFd, int visionNum, VisionRange &vsPamrm)
  Gets the range parameters for a specified vision system.
  Parameters:
    socketFd: The socket file descriptor for communication.
    visionNum: The identifier for the vision system.
    vsPamrm: A reference to a VisionRange structure to store the retrieved range parameters.
  Returns: Result code indicating success or failure.

vision_get_range_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionRange &vsPamrm)
  Gets the range parameters for a specified vision system associated with a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The identifier for the robot.
    visionNum: The identifier for the vision system.
    vsPamrm: A reference to a VisionRange structure to store the retrieved range parameters.
  Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: Class Members - 'e'
DESCRIPTION: This section lists class members that start with the letter 'e'. Each entry includes the member name, its type (implicitly a variable or property), and a link to the class definition where it is declared. This helps in understanding the context and usage of each member.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_e

LANGUAGE: APIDOC
CODE:
```
enable : AlarmdIO
  - Description: Represents the enable status within the AlarmdIO structure.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_alarmd_i_o.html#ac797bb50986f18393007837ea5071aa4

encoderResolution : RobotJointParam
  - Description: Specifies the encoder resolution for a robot joint parameter.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_robot_joint_param.html#aee2904ce76d56859495ba6b60e5098fd

endMark : Protocol
  - Description: Defines the end mark within the Protocol structure.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_protocol.html#a7462e321f9548fed29b4187aa00ffc3a

equipment : LaserCuttingGlobalParam
  - Description: Refers to equipment settings within the LaserCuttingGlobalParam structure.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_global_param.html#a3fd414c1f1101ff20d35c927fe042dab

error_enable_time_ms_value : CollisionPara
  - Description: Indicates the error enable time in milliseconds within the CollisionPara structure.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_collision_para.html#a22faab085e241ad8a60a551723accace

errorHanding : IOCommandParams
  - Description: Pertains to error handling within the IOCommandParams structure.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_i_o_command_params.html#ac832ba6c3f4004672f08744170e0e36e

excursion : Position
  - Description: Represents excursion in position data.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_position.html#a827caeae0a27cfe05f6f514e77549997

extMove : ServoMovePara
  - Description: Denotes external movement parameters in ServoMovePara.
  - Link: https://doc.hmilib.inexbot.coision.cn/struct_servo_move_para.html#a781329258dda3fcfbc56db11b2b65813
```

----------------------------------------

TITLE: nrc_craft_conveyor_belt_track.h File Reference
DESCRIPTION: This section provides details about the nrc_craft_conveyor_belt_track.h file, including its source code link and included header files. It serves as an entry point for understanding the conveyor belt track functionalities.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h

LANGUAGE: c++
CODE:
```
#include "parameter/nrc_define.h"
#include "parameter/nrc_craft_conveyor_belt_track_parameter.h"

[Go to the source code of this file.](https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h_source.html)
```

----------------------------------------

TITLE: Weld Configuration and Status Functions
DESCRIPTION: Provides functions to get welding configuration and status. This includes general configuration, robot-specific configuration, feed wire status, and monitoring status. These functions are essential for understanding the current state of the welding equipment.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_w

LANGUAGE: APIDOC
CODE:
```
weld_get_config()
  - Retrieves the general welding configuration.
  - Returns: Welding configuration details.

weld_get_config_robot()
  - Retrieves the robot-specific welding configuration.
  - Returns: Robot-specific welding configuration details.

weld_get_feed_wire_status()
  - Retrieves the status of the feed wire.
  - Returns: Feed wire status information.

weld_get_feed_wire_status_robot()
  - Retrieves the robot-specific status of the feed wire.
  - Returns: Robot-specific feed wire status information.

weld_get_monitor_status()
  - Retrieves the general monitoring status of the welding process.
  - Returns: General monitoring status.

weld_get_monitor_status_robot()
  - Retrieves the robot-specific monitoring status of the welding process.
  - Returns: Robot-specific monitoring status.
```

----------------------------------------

TITLE: Conveyor Belt Wait Point Parameters
DESCRIPTION: Functions to get wait point parameters for the conveyor belt, with separate implementations for direct interface and robot interaction.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_c

LANGUAGE: c
CODE:
```
conveyor_belt_get_wait_point_paramters();
conveyor_belt_get_wait_point_paramters_robot();
```

----------------------------------------

TITLE: SafeIO Struct Members
DESCRIPTION: This section lists all members of the SafeIO struct, including inherited members. It details various quick stop and screen-related configurations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_safe_i_o-members

LANGUAGE: APIDOC
CODE:
```
SafeIO Member List

This is the complete list of members for [SafeIO](https://doc.hmilib.inexbot.coision.cn/struct_safe_i_o.html), including all inherited members.

quickStopEnable | SafeIO | 
quickStopPort1 | SafeIO | 
quickStopPort2 | SafeIO | 
quickStopShied1 | SafeIO | 
quickStopShied2 | SafeIO | 
quickStopShiedTime | SafeIO | 
quickStopTime | SafeIO | 
quickStopValue1 | SafeIO | 
quickStopValue2 | SafeIO | 
screenEnable | SafeIO | 
screenPort1 | SafeIO | 
screenPort2 | SafeIO | 
screenValue1 | SafeIO | 
screenValue2 | SafeIO | 

Generated by [Doxygen](https://www.doxygen.org/index.html) 1.11.0
```

----------------------------------------

TITLE: Get Teach Type for Robot
DESCRIPTION: Retrieves the teach type for a specific robot, indicating whether the mode is jog (0) or drag (1).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_teach_type_robot(SOCKETFD _socketFd_, int _robotNum_, int & _type_)
  - Retrieves the teach type for a specific robot.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _robotNum_: The robot number.
    - _type_: A reference to an integer where the teach type will be stored (0 for jog, 1 for drag).
```

LANGUAGE: C++
CODE:
```
Result get_teach_type_robot(SOCKETFD _socketFd_, int _robotNum_, int & _type_)
```

----------------------------------------

TITLE: Robot Mode Management
DESCRIPTION: Functions to set and get the current operating mode of robots. These functions can target the default robot or a specific robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
set_current_mode(SOCKETFD socketFd, int mode)
  - Sets the current operating mode for the default robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - mode: The desired operating mode identifier.
  - Returns: Result indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
set_current_mode_robot(SOCKETFD socketFd, int robotNum, int mode)
  - Sets the current operating mode for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - mode: The desired operating mode identifier.
  - Returns: Result indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_current_mode(SOCKETFD socketFd, int &mode)
  - Retrieves the current operating mode of the default robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - mode: Output parameter to store the retrieved mode.
  - Returns: Result indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_current_mode_robot(SOCKETFD socketFd, int robotNum, int &mode)
  - Retrieves the current operating mode of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - mode: Output parameter to store the retrieved mode.
  - Returns: Result indicating success or failure.
```

LANGUAGE: C++
CODE:
```
int set_current_mode(SOCKETFD socketFd, int mode);
int set_current_mode_robot(SOCKETFD socketFd, int robotNum, int mode);
int get_current_mode(SOCKETFD socketFd, int &mode);
int get_current_mode_robot(SOCKETFD socketFd, int robotNum, int &mode);
```

----------------------------------------

TITLE: Job Configuration and Upload Functions
DESCRIPTION: Includes functions for setting local positions within a job and uploading job files or directories. These are essential for configuring job parameters and transferring job data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_j

LANGUAGE: APIDOC
CODE:
```
job_set_local_position()
  - Sets the local position for a job.
  - Related to: job_set_local_position_robot()

job_set_local_position_robot()
  - Sets the local position for a job with robot integration.
  - Related to: job_set_local_position()

job_sync_job_file()
  - Synchronizes a job file to the system.

job_upload_by_directory()
  - Uploads a job by specifying a directory.

job_upload_by_file()
  - Uploads a job by specifying a file.

```

----------------------------------------

TITLE: IO Structure Definition
DESCRIPTION: Defines the input and output signals for various operations. Includes digital inputs like DI_capacitance_ and DI_followArrival, and digital outputs like DO_aspiration and DO_backMiddle. Also includes PWM port definitions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting__parameter_8h_source

LANGUAGE: c++
CODE:
```
struct IO {
    int DI_capacitance_;
    int DI_followArrival;
    int DI_liftUpArrival;
    int DI_perforateArrival;
    int DO_aspiration;
    int DO_backMiddle;
    int DO_capacitance_;
    int DO_follow;
    int DO_highPressgas;
    int DO_liftUp;
    int DO_lightGate;
    int DO_lowPressgas;
    int DO_redLight;
    int pwm_port_;
};
```

----------------------------------------

TITLE: Class Members - Variables
DESCRIPTION: Lists variables within the net_lib library, categorized by their starting letter. Each variable includes a link to the class it belongs to.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_h

LANGUAGE: APIDOC
CODE:
```
Variables:

- h:
  - handWireFeed: Type [WeldState]. Belongs to WeldState class.
  - hasTCS: Type boolean. Belongs to Protocol class.
  - hasUCS: Type boolean. Belongs to Protocol class.
  - horizontalDeflection: Type float. Belongs to WaveParam class.
```

----------------------------------------

TITLE: Get Remote Program
DESCRIPTION: Retrieves the program configuration for a remote system. It takes a socket file descriptor, robot number, and output parameters for the number of programs and a vector of RemoteProgramSetting objects.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result get_remote_program(SOCKETFD socketFd, int robotNum, int& num, std::vector<RemoteProgramSetting>& program);
```

----------------------------------------

TITLE: Job Operation Functions
DESCRIPTION: Provides functions for job operations, such as downloading logs based on quantity.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_l

LANGUAGE: c
CODE:
```
int log_download_by_quantity(int quantity);
```

----------------------------------------

TITLE: Laser Cutting Parameter References
DESCRIPTION: References to structures related to laser cutting operations, including fault status, power settings, and analog matching.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_l

LANGUAGE: APIDOC
CODE:
```
LaserCuttingIOParam:
  - laser_fault: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_i_o_param.html#ac6d5ed5ba3ba963d9496d7ab1ce9fe29

LaserCuttingAnalogMatch:
  - laserPower: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_analog_match.html#a23f6b31f71a909af71789131d7803261
```

----------------------------------------

TITLE: Weld Parameter and Status Functions
DESCRIPTION: Functions for getting and setting weld parameters, including arc parameters and wave weld parameters, as well as retrieving robot monitoring status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__weld_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result weld_set_config(SOCKETFD socketFd, int index, ArcParam &param)
EXPORT_API Result weld_set_wave_weld_param(SOCKETFD socketFd, int num, const WaveParam &param)
EXPORT_API Result weld_get_monitor_status_robot(SOCKETFD socketFd, int robotNum, WeldState &status)
EXPORT_API Result weld_get_monitor_status(SOCKETFD socketFd, WeldState &status)
EXPORT_API Result weld_get_wave_weld_param_robot(SOCKETFD socketFd, int robotNum, int num, WaveParam &param)
EXPORT_API Result weld_get_wave_weld_param(SOCKETFD socketFd, int num, WaveParam &param)
```

----------------------------------------

TITLE: Get Hard Enable Port
DESCRIPTION: Retrieves the status of the hard enable switch and its associated ports. This function is part of the IO control system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_hard_enable_port(SOCKETFD _socketFd_, int & _enable_, int & _port1_, int & _port2_)
  - Retrieves the status of the hard enable switch and its associated ports.
  - Parameters:
    - _socketFd_: Socket file descriptor for communication.
    - _enable_: Output parameter, indicates if the hard enable is on.
    - _port1_: Output parameter, the first associated port.
    - _port2_: Output parameter, the second associated port.
  - Returns: Result status.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_hard_enable_port(SOCKETFD _socketFd_, int & _enable_, int & _port1_, int & _port2_);
```

----------------------------------------

TITLE: User Coordinate Parameter API
DESCRIPTION: Functions for getting and setting user coordinate parameters, which are represented as a vector of doubles. These are used for calibration and data storage.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_user_coord_para(SOCKETFD socketFd, int userNum, std::vector< double > &pos)
  Retrieves the parameters for the current user coordinate.
  Parameters:
    socketFd: The socket file descriptor for communication.
    userNum: The identifier for the user coordinate.
    pos: Reference to a vector of doubles to store the coordinate parameters.
  Returns: Result of the operation.

get_user_coord_para_robot(SOCKETFD socketFd, int robotNum, int userNum, std::vector< double > &pos)
  Retrieves the parameters for a specific robot's user coordinate.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The identifier for the robot.
    userNum: The identifier for the user coordinate.
    pos: Reference to a vector of doubles to store the coordinate parameters.
  Returns: Result of the operation.

set_user_coordinate_data(SOCKETFD socketFd, int userNum, std::vector< double > pos)
  Sets the data for a user coordinate (calibration).
  Parameters:
    socketFd: The socket file descriptor for communication.
    userNum: The identifier for the user coordinate.
    pos: A vector of doubles representing the coordinate parameters.
  Returns: Result of the operation.

set_user_coordinate_data_robot(SOCKETFD socketFd, int robotNum, int userNum, std::vector< double > pos)
  Sets the data for a specific robot's user coordinate (calibration).
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The identifier for the robot.
    userNum: The identifier for the user coordinate.
    pos: A vector of doubles representing the coordinate parameters.
  Returns: Result of the operation.
```

----------------------------------------

TITLE: nrc_parameter.h Includes
DESCRIPTION: This snippet lists the header files included by nrc_parameter.h. These dependencies are crucial for understanding the context and functionality provided by this header.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__parameter_8h

LANGUAGE: c
CODE:
```
#include "nrc_define.h"
#include "nrc_interface_parameter.h"
#include "nrc_io_parameter.h"
#include "nrc_modbus_parameter.h"
#include "nrc_craft_conveyor_belt_track_parameter.h"
#include "nrc_craft_laser_cutting_parameter.h"
#include "nrc_craft_vision_parameter.h"
#include "nrc_craft_weld_parameter.h"
```

----------------------------------------

TITLE: Robot Teach Type Retrieval
DESCRIPTION: Retrieves the teaching mode type of a specific robot. This function is used to get the current teaching mode.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_teach_type_robot(SOCKETFD socketFd, int robotNum, int &type)
  - Retrieves the teaching mode type for a specified robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - type: Reference to an integer to store the returned teaching type.
  - Returns: Result status of the operation.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_teach_type_robot(SOCKETFD socketFd, int robotNum, int &type);

```

----------------------------------------

TITLE: Weld Get Monitor Status
DESCRIPTION: Retrieves the current monitoring status of the welding process. This function is essential for tracking the real-time state of the weld.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__weld_8h_source

LANGUAGE: APIDOC
CODE:
```
weld_get_monitor_status(SOCKETFD socketFd, WeldState& status)
  - Gets the monitoring status of the weld.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - status: A reference to a WeldState structure to store the retrieved status.
```

LANGUAGE: APIDOC
CODE:
```
weld_get_monitor_status_robot(SOCKETFD socketFd, int robotNum, WeldState& status)
  - Gets the monitoring status of the weld for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - status: A reference to a WeldState structure to store the retrieved status.
```

----------------------------------------

TITLE: Conveyor Belt Get Basic Parameters
DESCRIPTION: Retrieves the basic parameters of a conveyor belt. The retrieved parameters are stored in the provided ConveyorBasicParams structure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h_source

LANGUAGE: APIDOC
CODE:
```
conveyor_belt_get_basic_paramters(SOCKETFD socketFd, int conveyorID, ConveyorBasicParams &param)
  - Retrieves basic parameters for a conveyor belt.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - conveyorID: The ID of the conveyor belt.
    - param: A reference to a ConveyorBasicParams structure to store the retrieved parameters.
  - Returns: Result status of the operation.
```

----------------------------------------

TITLE: Get Analog Output
DESCRIPTION: Queries the analog output values of a system. It requires a socket file descriptor and returns a vector of double values representing the analog outputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result get_analog_output(SOCKETFD socketFd, std::vector< double > &aout);
```

----------------------------------------

TITLE: Weld Parameter Functions
DESCRIPTION: Provides functions to get and set welding parameters, specifically focusing on wave welding parameters. These functions allow for fine-tuning the welding process by adjusting specific wave parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_w

LANGUAGE: APIDOC
CODE:
```
weld_get_wave_weld_param()
  - Retrieves the general wave welding parameters.
  - Returns: General wave welding parameters.

weld_get_wave_weld_param_robot()
  - Retrieves the robot-specific wave welding parameters.
  - Returns: Robot-specific wave welding parameters.

weld_set_wave_weld_param(params)
  - Sets the general wave welding parameters.
  - Parameters:
    - params: The wave welding parameters to set.
  - Returns: Status of the operation.

weld_set_wave_weld_param_robot(params)
  - Sets the robot-specific wave welding parameters.
  - Parameters:
    - params: The robot-specific wave welding parameters to set.
  - Returns: Status of the operation.
```

----------------------------------------

TITLE: C++ Laser Cutting Function Declarations
DESCRIPTION: Declarations for C++ functions related to laser cutting operations, including setting and getting analog and I/O parameters for robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: C++
CODE:
```
EXPORT_API Result laser_cutting_set_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam param);
EXPORT_API Result laser_cutting_get_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam& param);
EXPORT_API Result laser_cutting_get_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam& param);
EXPORT_API Result laser_cutting_set_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam param);
EXPORT_API Result laser_cutting_set_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam param);
EXPORT_API Result laser_cutting_get_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam& param);
EXPORT_API Result laser_cutting_get_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam& param);
```

----------------------------------------

TITLE: Tool and Hand Parameters
DESCRIPTION: Retrieves information about the tool and hand number and parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_tool_hand_number()
  - Retrieves the tool and hand number.
  - Source: nrc_interface.h

get_tool_hand_number_robot()
  - Retrieves the tool and hand number from the robot.
  - Source: nrc_interface.h

get_tool_hand_param()
  - Retrieves the tool and hand parameters.
  - Source: nrc_interface.h

get_tool_hand_param_robot()
  - Retrieves the tool and hand parameters from the robot.
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Robot Configuration APIs
DESCRIPTION: APIs to get the configuration of robots, including their morphology. These functions require a socket file descriptor and may specify a robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_robot_configuration(SOCKETFD socketFd, int &configuration)
  Retrieves the configuration of the 4-axis sacra robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    configuration: A reference to an integer to store the robot's configuration.

get_robot_configuration_robot(SOCKETFD socketFd, int robotNum, int &configuration)
  Retrieves the configuration of a specified robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The number of the robot.
    configuration: A reference to an integer to store the robot's configuration.
```

----------------------------------------

TITLE: Job Execution Functions
DESCRIPTION: Provides functions to control the execution of jobs, including starting a job with a specific robot, executing a single step of a job, and executing a step for a specific robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_run_robot(SOCKETFD socketFd, int robotNum, const std::string& jobName);
EXPORT_API Result job_step(SOCKETFD socketFd, const std::string& jobName, int line);
EXPORT_API Result job_step_robot(SOCKETFD socketFd, int robotNum, const std::string& jobName, int line);
```

----------------------------------------

TITLE: Get Current Job Line
DESCRIPTION: Retrieves the current line number within the job file. Requires a socket file descriptor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_get_current_line(SOCKETFD socketFd, int& line);
```

----------------------------------------

TITLE: Configure IO Board Alarm Messages
DESCRIPTION: Sets alarm messages for digital output ports on IO boards. The `msg` parameter is a structure containing message settings. It's crucial to set all ports, as unassigned ports will overwrite previous configurations. For multiple IO boards, port order is sequential.

SOURCE: https://doc.hmilib.inexbot.coision.cn/_xE5_xA6_x82_xE6_x9C_x89_xE4_xB8_xA4_xE5_x9D_x97_i_o_xE6_x9D_xBF_xEF_xBC_x8C_xE6_xAF_x8F_xE5_x9D5ff3f2d7408555d39fac416b2f9c4fd7

LANGUAGE: APIDOC
CODE:
```
APIDOC:
  setIOAlarm(msg: std::vector<AlarmdIO>)
    Parameters:
      msg: A structure containing message settings for IO boards. Each element in the vector corresponds to a port. The size of the vector should match the number of IO board ports.

    Attention:
      std::vector<AlarmdIO> msg size must be consistent with the number of IO board ports. Each port setting must be complete to avoid overwriting previous configurations. For multiple IO boards, port order is a continuation of the previous board's last port.
      Example:
        If IO board 1 output port 1 is set with message "QQQ", message type 2, parameter 1, enabled 1; and IO board 2 output port 2 is set with message "YYY", message type 2, parameter 1, enabled 1:
        The container should be set as:
        msg[0].msgType = 2;
        msg[0].enable = 1;
        msg[0].value = 1;
        msg[0].msg = "QQQ";
        Then, leave an interval of 15 elements (e.g., msg[i].msgType = 0; msg[i].enable = 0; msg[i].value = 0; msg[i].msg = "");
        Finally, set:
        msg[16].msgType = 2;
        msg[16].enable = 1;
        msg[16].value = 1;
        msg[16].msg = "YYY";
```

----------------------------------------

TITLE: Inexbot Track Recording Functions
DESCRIPTION: Manages track recording operations for the robot, including starting, stopping, saving, playing back, and deleting recorded tracks. Includes robot-specific variants for these operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_t

LANGUAGE: APIDOC
CODE:
```
track_record_delete()
  - Deletes a recorded track.
  - Source: nrc_track.h

track_record_delete_robot()
  - Deletes a recorded track with robot-specific considerations.
  - Source: nrc_track.h

track_record_playback()
  - Plays back a recorded track.
  - Source: nrc_track.h

track_record_playback_robot()
  - Plays back a recorded track with robot-specific considerations.
  - Source: nrc_track.h

track_record_save()
  - Saves the current track recording.
  - Source: nrc_track.h

track_record_save_robot()
  - Saves the current track recording with robot-specific considerations.
  - Source: nrc_track.h

track_record_start()
  - Starts a new track recording.
  - Source: nrc_track.h

track_record_start_robot()
  - Starts a new track recording with robot-specific considerations.
  - Source: nrc_track.h

track_record_stop()
  - Stops the current track recording.
  - Source: nrc_track.h

track_record_stop_robot()
  - Stops the current track recording with robot-specific considerations.
  - Source: nrc_track.h
```

----------------------------------------

TITLE: HMI Library Interface Parameters
DESCRIPTION: Defines parameters for different robot configurations, including three-axle and two-axle SCARA setups. These parameters are crucial for defining the robot's kinematic and operational characteristics.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_t

LANGUAGE: APIDOC
CODE:
```
THREE_AXLE_ABNORMITY: Defined in nrc_interface_parameter.h
THREE_AXLE_ANGLE: Defined in nrc_interface_parameter.h
THREE_AXLE_SCARA: Defined in nrc_interface_parameter.h
TWO_AXLE_SCARA: Defined in nrc_interface_parameter.h
```

----------------------------------------

TITLE: Insert Robot Move Command with Parameters
DESCRIPTION: Inserts a move command for a specific robot with detailed motion parameters. Requires socket file descriptor, robot number, line number, move type, velocities, accelerations, decelerations, time, and planning points.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result job_insert_moveComm_rbobt(SOCKETFD socketFd, int robotNum, int line, std::string moveType, double m_vel, double m_acc, double m_dec, int m_time, int m_pl);
```

----------------------------------------

TITLE: I/O Parameter Structures in nrc_io_parameter.h
DESCRIPTION: This file defines various structures related to I/O parameters, including alarm states, remote program settings, and safe I/O configurations. These structures are essential for managing device inputs and outputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h

LANGUAGE: c++
CODE:
```
#include <string>
#include <vector>
#include "nrc_define.h"

struct AlarmdIO {};
struct RemoteProgram {};
struct RemoteControl {};
struct RemoteProgramSetting {};
struct SafeIO {};
```

----------------------------------------

TITLE: Conveyor Belt Track Range Parameters
DESCRIPTION: Functions to get track range parameters for the conveyor belt, with separate implementations for direct interface and robot interaction.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_c

LANGUAGE: c
CODE:
```
conveyor_belt_get_track_range_paramters();
conveyor_belt_get_track_range_paramters_robot();
```

----------------------------------------

TITLE: Current Position and Coordinate Functions
DESCRIPTION: These functions are used to get the current position or coordinates of the robot or system. Variants exist for C interfaces and robot-specific contexts.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_current_coord()
  - Retrieves the current coordinate.
  - Defined in: nrc_interface.h

get_current_coord_c()
  - Retrieves the current coordinate using a C interface.
  - Defined in: nrc_c_interface.h

get_current_coord_robot()
  - Retrieves the current coordinate for robot applications.
  - Defined in: nrc_interface.h

get_current_position()
  - Retrieves the current position.
  - Defined in: nrc_interface.h

get_current_position_c()
  - Retrieves the current position using a C interface.
  - Defined in: nrc_c_interface.h

get_current_position_robot()
  - Retrieves the current position for robot applications.
  - Defined in: nrc_interface.h
```

----------------------------------------

TITLE: Analog Output Functions
DESCRIPTION: Functions to set and get analog output values. `set_analog_output` takes a socket file descriptor, port, and a double value to set the output. `get_analog_output` retrieves a vector of double values representing analog outputs using a socket file descriptor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
Result set_analog_output(SOCKETFD socketFd, int port, double value);
Result get_analog_output(SOCKETFD socketFd, std::vector<double>& aout);
```

----------------------------------------

TITLE: Get Four-Point Calibration Data for Robot
DESCRIPTION: Queries the four-point calibration data for a specific robot. This function requires the robot number and a valid SOCKETFD.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_four_point_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< double > & _result_)
  - Queries four-point calibration data for a specific robot.
  - Parameters:
    - _socketFd_: File descriptor for socket communication.
    - _robotNum_: The number of the robot.
    - _result_: Output vector containing the calibration results.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_four_point_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< double > & _result_);
```

----------------------------------------

TITLE: Robot Configuration and Status Functions
DESCRIPTION: Provides functions to retrieve robot configuration details, library version, and I/O related status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_robot_configuration()
  Retrieves the configuration of the robot.

get_robot_configuration_robot()
  Retrieves the configuration of the robot (robot-specific).

get_library_version()
  Retrieves the version of the HMI library.

get_hard_enable_port()
  Retrieves the status of the hard enable port.

get_safe_IO_function()
  Retrieves information about safe I/O functions.

get_IO_reset_function()
  Retrieves information about I/O reset functions.

get_remote_function()
  Retrieves information about remote functions.

get_remote_param()
  Retrieves information about remote parameters.

get_remote_program()
  Retrieves information about remote programs.

get_remote_status_tips()
  Retrieves status tips for remote operations.
```

----------------------------------------

TITLE: Get Current Line and Joint Speed
DESCRIPTION: Retrieves the current linear and joint speeds of the robot. Functions are provided for standard and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_curretn_line_speed_and_joint_speed()
  - Retrieves the current linear and joint speeds of the robot.
  - Source: nrc_interface.h

get_curretn_line_speed_and_joint_speed_robot()
  - Retrieves the current linear and joint speeds of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Tool Hand Parameter Management
DESCRIPTION: Functions to set and get parameters for tool hands. These functions allow for the configuration and retrieval of specific parameters associated with a tool, with overloaded versions available for robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_tool_hand_param(SOCKETFD socketFd, int toolNum, ToolParam param)
  Sets a parameter for a specific tool hand.
  Parameters:
    socketFd: The socket file descriptor for communication.
    toolNum: The number of the tool.
    param: The ToolParam structure containing the parameters to set.
  Returns: Result indicating success or failure.

set_tool_hand_param_robot(SOCKETFD socketFd, int robotNum, int toolNum, ToolParam param)
  Sets a parameter for a specific tool hand associated with a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The number of the robot.
    toolNum: The number of the tool.
    param: The ToolParam structure containing the parameters to set.
  Returns: Result indicating success or failure.

get_tool_hand_param(SOCKETFD socketFd, int toolNum, ToolParam& param)
  Retrieves parameters for a specific tool hand.
  Parameters:
    socketFd: The socket file descriptor for communication.
    toolNum: The number of the tool.
    param: A reference to a ToolParam structure to store the retrieved parameters.
  Returns: Result indicating success or failure.

get_tool_hand_param_robot(SOCKETFD socketFd, int robotNum, int toolNum, ToolParam& param)
  Retrieves parameters for a specific tool hand associated with a robot.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The number of the robot.
    toolNum: The number of the tool.
    param: A reference to a ToolParam structure to store the retrieved parameters.
  Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Set Remote Function and Parameters
DESCRIPTION: Configures remote IO functionalities, including general control parameters and program settings. It allows setting speed, start conditions, time delays, and the number of remote IOs. This function is crucial for managing robot operations remotely.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
set_remote_function(SOCKETFD _socketFd_, int _robotNum_, RemoteControl _general_, std::vector<RemoteProgram> _program_, int _num_ = 10)
  - Sets the remote IO function.
  - Parameters:
    - _socketFd_: The socket file descriptor.
    - _robotNum_: The robot number (1-4).
    - _general_: Structure for general remote IO parameter settings (e.g., start, pause, stop, clear alarm). Refer to RemoteControl documentation.
    - _program_: Vector of RemoteProgram structures for remote control program settings. The size must match _num_.
    - _num_: The number of remote IOs (defaults to 10). For version 24.03, this must match the num in set_remote_param. Version 22.07 does not have this parameter.
```

LANGUAGE: APIDOC
CODE:
```
set_remote_param(SOCKETFD _socketFd_, int _robotNum_, int _speed_, bool _start_, int _time_, int _startTime_, int _num_ = 10)
  - Sets remote parameters.
  - Parameters:
    - _socketFd_: The socket file descriptor.
    - _robotNum_: The robot number (1-4).
    - _speed_: Default speed for remote mode [1, 100].
    - _start_: Boolean indicating whether to auto-start.
    - _time_: IO repeat trigger shield time in milliseconds.
    - _startTime_: Start confirmation time.
    - _num_: Number of remote IOs. For version 22.07, this parameter is absent. For versions 24.03 and above, this parameter can be modified (defaults to 10).
```

----------------------------------------

TITLE: Get Safe IO Function
DESCRIPTION: Retrieves the configuration of the safe IO function for a remote system. It takes a socket file descriptor, robot number, and an output SafeIO object.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result get_safe_IO_function(SOCKETFD socketFd, int robotNum, SafeIO& safeIO);
```

----------------------------------------

TITLE: Download Log by Quantity
DESCRIPTION: Downloads a specified quantity of log data. This function takes a socket file descriptor, the number of log entries to download, and the directory path where the logs should be saved.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result log_download_by_quantity(SOCKETFD socketFd, int counts, const std::string& directoryPath);
```

----------------------------------------

TITLE: Get Error Message for Digital Input
DESCRIPTION: Retrieves the error message associated with a digital input operation. This function is part of the I/O module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_error_msg_of_digital_input()
  - Retrieves the error message for a digital input.
  - Source: nrc_io.h
```

----------------------------------------

TITLE: Weld Configuration and Status Functions
DESCRIPTION: Provides functions to get welding configuration and status. This includes general configuration, robot-specific configuration, feed wire status, and monitoring status. These functions are essential for understanding the current state of the welding equipment.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_w

LANGUAGE: APIDOC
CODE:
```
weld_get_config()
  - Retrieves the general welding configuration.
  - Returns: Welding configuration details.

weld_get_config_robot()
  - Retrieves the robot-specific welding configuration.
  - Returns: Robot-specific welding configuration details.

weld_get_feed_wire_status()
  - Retrieves the status of the feed wire.
  - Returns: Feed wire status information.

weld_get_feed_wire_status_robot()
  - Retrieves the robot-specific status of the feed wire.
  - Returns: Robot-specific feed wire status information.

weld_get_monitor_status()
  - Retrieves the general monitoring status of the welding process.
  - Returns: General monitoring status.

weld_get_monitor_status_robot()
  - Retrieves the robot-specific monitoring status of the welding process.
  - Returns: Robot-specific monitoring status.
```

----------------------------------------

TITLE: Get Error Message for Digital Output
DESCRIPTION: Retrieves the error message associated with a digital output operation. This function is part of the I/O module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_error_msg_of_digital_output()
  - Retrieves the error message for a digital output.
  - Source: nrc_io.h
```

----------------------------------------

TITLE: Calibration and Utility Functions
DESCRIPTION: Provides functions for calibration tasks like retrieving four-point calibration data and accessing data within PosType.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_four_point(SOCKETFD socketFd, std::vector< double > &result)
  - Retrieves four-point calibration data.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - result: A reference to a vector of doubles to store the calibration data.

PosType::data
  - Accessor for data within the PosType structure.
```

----------------------------------------

TITLE: Insert Conveyor ON Instruction
DESCRIPTION: Inserts a CONVEYOR_ON instruction into the job file. This instruction is used to start conveyor tracking. It supports specifying position, velocity, and acceleration parameters. The `posType` parameter determines whether to use manual position data or default workpiece positions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_on(SOCKETFD socketFd, int line, int id, int posType, std::vector<double> pos, int vel, int acc)
  Inserts a CONVEYOR_ON (conveyor tracking start) instruction into the job file.
  Parameters:
    socketFd: File descriptor for the socket connection.
    line: The line number where the instruction should be inserted.
    id: The process ID.
    posType: Specifies how to provide position data: 0 for manual, 1 for default workpiece.
    pos: Position data (14 elements: coordinate system, angle/radian, coordinate values).
    vel: Velocity parameter (range [2, 2000] mm/s).
    acc: Acceleration parameter (range [1, 100]).
  Returns: Result code indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_on_robot(SOCKETFD socketFd, int robotNum, int line, int id, int posType, std::vector<double> pos, int vel, int acc)
  Inserts a CONVEYOR_ON instruction for a specific robot.
  Parameters:
    socketFd: File descriptor for the socket connection.
    robotNum: The robot number.
    line: The line number where the instruction should be inserted.
    id: The process ID.
    posType: Specifies how to provide position data: 0 for manual, 1 for default workpiece.
    pos: Position data (14 elements: coordinate system, angle/radian, coordinate values).
    vel: Velocity parameter (range [2, 2000] mm/s).
    acc: Acceleration parameter (range [1, 100]).
  Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: net_lib Class List
DESCRIPTION: This section lists all the classes available in the net_lib library. Each class is presented with a brief description, facilitating quick understanding of the library's structure and components.

SOURCE: https://doc.hmilib.inexbot.coision.cn/annotated

LANGUAGE: APIDOC
CODE:
```
AlarmdIO:
  Represents alarm input/output status.

ArcParam:
  Defines parameters for arc movements.

Calibration:
  Contains calibration data.

CalibrationPoint:
  Represents a single calibration point.

CameraList:
  Manages a list of cameras.

CollisionPara:
  Parameters related to collision detection.

Condition:
  Defines a condition.

ConveyorBasicParams:
  Basic parameters for conveyor control.

ConveyorIdentificationParams:
  Parameters for conveyor identification.

ConveyorSensorParams:
  Sensor parameters for conveyor systems.

ConveyorTrackRangeParams:
  Parameters defining the track range for conveyors.

ConveyorWaitPointParams:
  Parameters for conveyor wait points.

Curve:
  Represents a curve path.

Excursion:
  Defines excursion parameters.

Fault:
  Represents a fault condition.

IO:
  General Input/Output structure.

IOCommandParams:
  Parameters for IO commands.

LaserCuttingAnalogMatch:
  Analog matching parameters for laser cutting.

LaserCuttingAnalogParam:
  Analog parameters for laser cutting.

LaserCuttingCraftParam:
  Craft parameters for laser cutting.

LaserCuttingEquipment:
  Represents laser cutting equipment.

LaserCuttingGlobalParam:
  Global parameters for laser cutting.

LaserCuttingIOParam:
  IO parameters for laser cutting.

LaserCuttingParam:
  General parameters for laser cutting.

ModbusMasterParameter:
  Parameters for Modbus master configuration.

ModbusRTUParameter:
  Parameters for Modbus RTU communication.

ModbusTCPParameter:
  Parameters for Modbus TCP communication.

MoveCmd:
  Represents a movement command.

OffsetCommandParam:
  Parameters for offset commands.

ParaGroup:
  A group of parameters.

Position:
  Represents a 3D position.

PositionData:
  Data associated with a position.

Protocol:
  Defines communication protocol details.

RemoteControl:
  Settings for remote control.

RemoteProgram:
  Information about remote programs.

RemoteProgramSetting:
  Settings for remote programs.

RobotDHParam:
  Denavit-Hartenberg parameters for robots.

RobotJointParam:
  Parameters for robot joints.

SafeIO:
  Represents safety-related IO.

ServoMovePara:
  Parameters for servo motor movements.

Socket:
  Represents a network socket.
```

----------------------------------------

TITLE: Get Current Extra Position
DESCRIPTION: Retrieves the current extra position data of the robot. Functions are provided for standard, C, and robot-specific interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_current_extra_position()
  - Retrieves the current extra position data of the robot.
  - Source: nrc_interface.h

get_current_extra_position_c()
  - Retrieves the current extra position data of the robot using C interface.
  - Source: nrc_c_interface.h

get_current_extra_position_robot()
  - Retrieves the current extra position data of the robot (robot-specific).
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Get Four-Point Calibration Data
DESCRIPTION: Queries the four-point calibration data. This function requires a valid SOCKETFD and returns the calibration results in a double vector.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_four_point(SOCKETFD _socketFd_, std::vector< double > & _result_)
  - Queries four-point calibration data.
  - Parameters:
    - _socketFd_: File descriptor for socket communication.
    - _result_: Output vector containing the calibration results.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_four_point(SOCKETFD _socketFd_, std::vector< double > & _result_);
```

----------------------------------------

TITLE: HMILIB Interface Parameters
DESCRIPTION: This section details the various interface parameters available within the HMILIB library. It covers different aspects of parameter handling and their associated functionalities.

SOURCE: https://doc.hmilib.inexbot.coision.cn/doxygen_crawl

LANGUAGE: c
CODE:
```
#include <nrc_interface_parameter.h>

// Example usage of interface parameters would go here, but the provided text only contains links to definitions.
```

----------------------------------------

TITLE: Robot Coordinate System Management
DESCRIPTION: Functions to set and get the current coordinate system for robots. These functions operate on a specified robot or the default robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
set_current_coord(SOCKETFD socketFd, int coord)
  - Sets the current coordinate system for the default robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - coord: The desired coordinate system identifier.
  - Returns: Result indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
set_current_coord_robot(SOCKETFD socketFd, int robotNum, int coord)
  - Sets the current coordinate system for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - coord: The desired coordinate system identifier.
  - Returns: Result indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_current_coord(SOCKETFD socketFd, int &coord)
  - Retrieves the current coordinate system of the default robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - coord: Output parameter to store the retrieved coordinate system.
  - Returns: Result indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_current_coord_robot(SOCKETFD socketFd, int robotNum, int &coord)
  - Retrieves the current coordinate system of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - coord: Output parameter to store the retrieved coordinate system.
  - Returns: Result indicating success or failure.
```

LANGUAGE: C++
CODE:
```
int set_current_coord(SOCKETFD socketFd, int coord);
int set_current_coord_robot(SOCKETFD socketFd, int robotNum, int coord);
int get_current_coord(SOCKETFD socketFd, int &coord);
int get_current_coord_robot(SOCKETFD socketFd, int robotNum, int &coord);
```

----------------------------------------

TITLE: LaserCuttingEquipment Structure
DESCRIPTION: Defines parameters for laser cutting equipment, including focus compensation, follow settings, and timing.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting__parameter_8h_source

LANGUAGE: cpp
CODE:
```
struct LaserCuttingEquipment {
  double focusCompensationPower{0.0};
  double focusCompensationTime{0.0};
  int focusFormula{0};
  int follow{0};
  double preAspiratedTime{0.0};
  int rePerforate{1};
  double waitFollowTime{1.0};
  double waitLiftUpTime{1.0};
};
```

----------------------------------------

TITLE: Get Single Cycle Data
DESCRIPTION: Retrieves single cycle data. This function populates a vector with 7 double values representing the cycle data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_single_cycle(SOCKETFD _socketFd_, std::vector< double > & _single_cycle_)
  - Retrieves single cycle data.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _single_cycle_: A reference to a std::vector<double> to store the cycle data. The vector is expected to have a length of 7.
```

LANGUAGE: C++
CODE:
```
Result get_single_cycle(SOCKETFD _socketFd_, std::vector< double > & _single_cycle_)
```

----------------------------------------

TITLE: Robot Connection Functions
DESCRIPTION: Functions to establish a connection with the robot, with implementations for the C interface and direct interface.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_c

LANGUAGE: c
CODE:
```
connect_robot();
connect_robot_c();
```

----------------------------------------

TITLE: Get Hard Enable Port
DESCRIPTION: Retrieves the status of the hard enable ports. It takes a socket file descriptor and output parameters for the enable status and values of two ports.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result get_hard_enable_port(SOCKETFD socketFd, int& enable, int& port1, int& port2);
```

----------------------------------------

TITLE: Servo Move Parameters
DESCRIPTION: Parameters for controlling servo motor movement, including the run mode.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_r

LANGUAGE: APIDOC
CODE:
```
ServoMovePara:
  runMode : The operational mode for the servo motor.
```

----------------------------------------

TITLE: Weld Configuration Functions
DESCRIPTION: Provides functions to get and set welding parameters. These functions interact with a socket descriptor and an index to specify the configuration. An ArcParam structure is used to pass welding parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__weld_8h

LANGUAGE: APIDOC
CODE:
```
weld_get_config(SOCKETFD socketFd, int index, ArcParam &param)
  - Retrieves welding parameters.
  - Parameters:
    - socketFd: The socket descriptor for communication.
    - index: The index specifying the configuration.
    - param: A reference to an ArcParam structure to store the retrieved parameters.

weld_get_config_robot(SOCKETFD socketFd, int robotNum, int index, ArcParam &param)
  - Retrieves welding parameters for a specific robot.
  - Parameters:
    - socketFd: The socket descriptor for communication.
    - robotNum: The number of the robot.
    - index: The index specifying the configuration.
    - param: A reference to an ArcParam structure to store the retrieved parameters.

weld_set_config(SOCKETFD socketFd, int index, ArcParam &param)
  - Sets welding parameters.
  - Parameters:
    - socketFd: The socket descriptor for communication.
    - index: The index specifying the configuration.
    - param: An ArcParam structure containing the parameters to set.

weld_set_config_robot(SOCKETFD socketFd, int robotNum, int index, ArcParam &param)
  - Sets welding parameters for a specific robot.
  - Parameters:
    - socketFd: The socket descriptor for communication.
    - robotNum: The number of the robot.
    - index: The index specifying the configuration.
    - param: An ArcParam structure containing the parameters to set.
```

----------------------------------------

TITLE: Get Forced Digital Input Status
DESCRIPTION: Retrieves the status of currently enabled forced digital input ports. It returns the ports and their corresponding statuses.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_force_digital_input(_socketFd_: SOCKETFD, _port_: std::vector< int > &, _status_: std::vector< double > &)
  - Retrieves currently enabled forced digital input ports and their statuses.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _port_: Vector to store the ports with forced input enabled.
    - _status_: Vector to store the statuses of the corresponding ports.
```

LANGUAGE: C++
CODE:
```
Result get_force_digital_input(SOCKETFD _socketFd_, std::vector< int > & _port_, std::vector< double > & _status_)
```

----------------------------------------

TITLE: HMI System Backup Function
DESCRIPTION: API documentation for the system backup function. This function creates a backup of the system and saves it in the current execution directory. It requires a socket file descriptor for communication.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
backup_system(SOCKETFD socketFd)
  - Performs a one-click system backup.
  - The backup is saved in the current execution directory.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Get Current Speed
DESCRIPTION: Retrieves the current speed of the system. The speed is returned as an integer between 1 and 100, indicating the speed for teaching, running, or remote modes.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_speed(SOCKETFD _socketFd_, int & _speed_)
  - Retrieves the current speed.
  - Description: Gets the current speed in three modes: teaching, running, and remote.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _speed_: A reference to an integer where the speed will be stored. Parameter range: 0 < speed <= 100.
```

LANGUAGE: C++
CODE:
```
Result get_speed(SOCKETFD _socketFd_, int & _speed_)
```

----------------------------------------

TITLE: Get Four Point Data for Robot
DESCRIPTION: Retrieves four-point data for a specific robot. This function enables fetching four-point measurements on a per-robot basis.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_four_point_robot(SOCKETFD socketFd, int robotNum, std::vector<double>& result)
  - Retrieves four-point data for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number identifier (integer).
    - result: A reference to a vector of doubles to store the four-point data.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Hard Enable Port Functions
DESCRIPTION: Manages the hard enable functionality and associated ports for robots. These functions allow setting and getting the enable status and the ports that are bound to this feature.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
set_hard_enable_port(SOCKETFD socketFd, int enable, int port1, int port2)
  - Sets whether the hard enable is active and configures related ports.
  - Parameters:
    - socketFd: The socket file descriptor.
    - enable: An integer indicating the enable status (e.g., 1 for enabled, 0 for disabled).
    - port1: The first associated port.
    - port2: The second associated port.
  - Returns: Result (likely indicating success or failure).

get_hard_enable_port(SOCKETFD socketFd, int &enable, int &port1, int &port2)
  - Retrieves the status of the hard enable switch and its bound ports.
  - Parameters:
    - socketFd: The socket file descriptor.
    - enable: Reference to an integer to store the enable status.
    - port1: Reference to an integer to store the first associated port.
    - port2: Reference to an integer to store the second associated port.
  - Returns: Result (likely indicating success or failure).
```

----------------------------------------

TITLE: Tool Hand Number Management
DESCRIPTION: Functions to set and get the currently used tool or hand number. This is essential for robots equipped with multiple end-effectors.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
set_tool_hand_number(SOCKETFD socketFd, int toolNum)
  - Sets the number for the currently used tool or hand.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - toolNum: The number of the tool/hand to set.
  - Description: 设置工具手编号
```

LANGUAGE: APIDOC
CODE:
```
set_tool_hand_number_robot(SOCKETFD socketFd, int robotNum, int toolNum)
  - Sets the number for the currently used tool or hand for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number identifying the robot.
    - toolNum: The number of the tool/hand to set.
```

LANGUAGE: APIDOC
CODE:
```
get_tool_hand_number(SOCKETFD socketFd, int &toolNum)
  - Retrieves the number of the currently used tool or hand.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - toolNum: An output parameter to store the retrieved tool/hand number.
  - Description: 获取当前使用的工具手编号
```

----------------------------------------

TITLE: net_lib Class Index
DESCRIPTION: This section lists all the classes available in the net_lib library, categorized alphabetically. Each class name links to its detailed documentation.

SOURCE: https://doc.hmilib.inexbot.coision.cn/classes

LANGUAGE: APIDOC
CODE:
```
Class Index:

Alphabetical listing of all classes in the net_lib library.

- AlarmdIO
- ArcParam
- Calibration
- CalibrationPoint
- CameraList
- CollisionPara
- Condition
- ConveyorBasicParams
- ConveyorIdentificationParams
- ConveyorSensorParams
- ConveyorTrackRangeParams
- ConveyorWaitPointParams
- Curve
- Excursion
- Fault
- IO
- IOCommandParams
- LaserCuttingAnalogMatch
- LaserCuttingAnalogParam
- LaserCuttingCraftParam
- LaserCuttingEquipment
- LaserCuttingGlobalParam
- LaserCuttingIOParam
- LaserCuttingParam
- ModbusMasterParameter
- ModbusRTUParameter
- ModbusTCPParameter
- MoveCmd
- OffsetCommandParam
```

----------------------------------------

TITLE: Get Current Line Number
DESCRIPTION: Retrieves the current line number being executed in the opened job file. This is useful for tracking program execution flow.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: cpp
CODE:
```
Result job_get_current_line(SOCKETFD _socketFd_, int& _line_)
```

----------------------------------------

TITLE: Get Controller ID (C#)
DESCRIPTION: Retrieves the serial number ID of the controller. This function is typically used in C# applications interacting with the HMI library.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_controller_id_csharp(SOCKETFD socketFd, std::vector< char > &id)
  - Retrieves the serial number ID of the controller.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - id: A reference to a vector of characters to store the controller ID.
  - Returns: void (ID is populated in the 'id' parameter)
```

----------------------------------------

TITLE: Add WAVON command to robot queue
DESCRIPTION: Appends a WAVON (wave soldering start) command to the specified robot's local motion queue. Requires a socket file descriptor, robot number, and command ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_wave_on_robot(SOCKETFD socketFd, int robotNum, int id)
```

----------------------------------------

TITLE: Get Digital Output Error Messages
DESCRIPTION: Retrieves alarm information for digital output ports. The error messages are stored in a vector of AlarmdIO structures.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_error_msg_of_digital_output(_socketFd_: SOCKETFD, _msg_: std::vector< AlarmdIO > &)
  - Retrieves alarm information for digital output ports.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _msg_: Vector to store AlarmdIO structures containing error details.
```

LANGUAGE: C++
CODE:
```
Result get_error_msg_of_digital_output(SOCKETFD _socketFd_, std::vector< AlarmdIO > & _msg_)
```

----------------------------------------

TITLE: Job Insertion Functions
DESCRIPTION: Provides functions for inserting various types of jobs, including timed insertions, robot-assisted insertions, and vision-guided craft operations. These functions are crucial for setting up and initiating job sequences.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_j

LANGUAGE: APIDOC
CODE:
```
job_insert_until()
  - Inserts a job that continues until a specified condition is met.
  - Related to: job_insert_until_robot()

job_insert_until_robot()
  - Inserts a job that continues until a specified condition is met, with robot integration.
  - Related to: job_insert_until()

job_insert_vision_craft_get_pos()
  - Inserts a job to get the position for a vision-guided craft operation.
  - Related to: job_insert_vision_craft_get_pos_robot()

job_insert_vision_craft_get_pos_robot()
  - Inserts a job to get the position for a vision-guided craft operation with robot integration.
  - Related to: job_insert_vision_craft_get_pos()

job_insert_vision_craft_start()
  - Inserts a job to start a vision-guided craft operation.
  - Related to: job_insert_vision_craft_start_robot()

job_insert_vision_craft_start_robot()
  - Inserts a job to start a vision-guided craft operation with robot integration.
  - Related to: job_insert_vision_craft_start()

job_insert_vision_craft_visual_end()
  - Inserts a job to signal the end of a vision-guided craft operation.
  - Related to: job_insert_vision_craft_visual_end_robot()

job_insert_vision_craft_visual_end_robot()
  - Inserts a job to signal the end of a vision-guided craft operation with robot integration.
  - Related to: job_insert_vision_craft_visual_end()

job_insert_vision_craft_visual_trigger()
  - Inserts a job to trigger a visual event in a craft operation.
  - Related to: job_insert_vision_craft_visual_trigger_robot()

job_insert_vision_craft_visual_trigger_robot()
  - Inserts a job to trigger a visual event in a craft operation with robot integration.
  - Related to: job_insert_vision_craft_visual_trigger()

job_insert_while()
  - Inserts a job that executes a block of code repeatedly while a condition is true.
  - Related to: job_insert_while_robot()

job_insert_while_robot()
  - Inserts a job that executes a block of code repeatedly while a condition is true, with robot integration.
  - Related to: job_insert_while()

```

----------------------------------------

TITLE: Queue Motion Push Back IMove
DESCRIPTION: Pushes back an 'immediate move' motion command to the queue. This is typically used for point-to-point movements.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_q

LANGUAGE: c++
CODE:
```
queue_motion_push_back_imove()
```

----------------------------------------

TITLE: Get Safe IO Function
DESCRIPTION: Retrieves the safe IO settings for a given robot. This function is essential for configuring safety parameters related to IO operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_safe_IO_function(_socketFd: SOCKETFD, _robotNum: int, _safeIO: SafeIO &)
  获取IO安全设置参数
  Parameters:
    robot: 机器人编号(1-4)
    safeIO: 详见结构体参数说明
```

----------------------------------------

TITLE: Laser Cutting Global Parameter Management
DESCRIPTION: Functions to get global laser cutting parameters. These functions retrieve the current global settings for the laser cutting system. They require a socket file descriptor and optionally a robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result laser_cutting_get_global_parameter(SOCKETFD socketFd, LaserCuttingGlobalParam& param);
EXPORT_API Result laser_cutting_get_global_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingGlobalParam& param);
```

----------------------------------------

TITLE: Get Single Cycle Data for Robot
DESCRIPTION: Retrieves single cycle data for a specific robot. This function allows fetching cycle data on a per-robot basis.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_single_cycle_robot(SOCKETFD socketFd, int robotNum, std::vector<double>& single_cycle)
  - Retrieves single cycle data for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number identifier (integer).
    - single_cycle: A reference to a vector of doubles to store the cycle data.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Get Digital Input Error Messages
DESCRIPTION: Retrieves alarm information for digital input ports. The error messages are stored in a vector of AlarmdIO structures.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_error_msg_of_digital_input(_socketFd_: SOCKETFD, _msg_: std::vector< AlarmdIO > &)
  - Retrieves alarm information for digital input ports.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _msg_: Vector to store AlarmdIO structures containing error details.
```

LANGUAGE: C++
CODE:
```
Result get_error_msg_of_digital_input(SOCKETFD _socketFd_, std::vector< AlarmdIO > & _msg_)
```

----------------------------------------

TITLE: Get Four Point
DESCRIPTION: Retrieves four specific points, likely related to robot calibration or workspace definition. This function is part of the interface module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_four_point()
  - Retrieves four specific points.
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Get Current Job File Name
DESCRIPTION: Retrieves the name of the currently opened job file. Overloads are provided for general use, robot-specific use, and C# integration.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_get_current_file(SOCKETFD socketFd, std::string &jobName)
  - Retrieves the name of the currently opened job file.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - jobName: Output parameter to store the job file name.
  - Returns: Result of the operation.

job_get_current_file_robot(SOCKETFD socketFd, int robotNum, std::string &jobName)
  - Retrieves the name of the currently opened job file for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number.
    - jobName: Output parameter to store the job file name.
  - Returns: Result of the operation.

job_get_current_file_csharp(SOCKETFD socketFd, std::vector< char > &jobName)
  - Retrieves the name of the currently opened job file for C# integration.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - jobName: Output parameter to store the job file name (as a char vector).
  - Returns: Result of the operation.

job_get_current_file_csharp_robot(SOCKETFD socketFd, int robotNum, std::vector< char > &jobName)
  - Retrieves the name of the currently opened job file for a specific robot for C# integration.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number.
    - jobName: Output parameter to store the job file name (as a char vector).
  - Returns: Result of the operation.
```

----------------------------------------

TITLE: Robot State and Type Functions
DESCRIPTION: Functions to get the current running state and type of the robot. These functions are available through the main interface, robot interface, and C interface.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_robot_running_state()
  Retrieves the current running state of the robot.

get_robot_running_state_c()
  Retrieves the current running state of the robot using the C interface.

get_robot_running_state_robot()
  Retrieves the current running state of the robot via the robot interface.

get_robot_type()
  Retrieves the type of the robot.

get_robot_type_robot()
  Retrieves the type of the robot via the robot interface.
```

----------------------------------------

TITLE: Remote Status Tips Configuration
DESCRIPTION: Function to set remote status tips for a specified robot, including outage port, outage value, and a list of remote programs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
Result set_remote_status_tips(SOCKETFD socketFd, int robotNum, int outagePort, int outageValue, std::vector<RemoteProgram> program);
```

----------------------------------------

TITLE: HMI Vision Range Configuration
DESCRIPTION: Provides API documentation for setting and getting the range parameters for vision systems. Includes functions for direct vision control and robot-integrated control.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h

LANGUAGE: APIDOC
CODE:
```
vision_set_range(SOCKETFD socketFd, int visionNum, VisionRange vsPamrm)
  - Sets the range parameters for a vision system.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionRange structure containing the range parameters to set.

vision_set_range_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionRange vsPamrm)
  - Sets the range parameters for a vision system integrated with a robot.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - robotNum: Identifier for the robot.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionRange structure containing the range parameters to set.

vision_get_range(SOCKETFD socketFd, int visionNum, VisionRange &vsPamrm)
  - Queries the range data of a vision system.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionRange structure to store the retrieved range data.
  - Returns: Result status.

vision_get_range_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionRange &vsPamrm)
  - Queries the range data of a vision system integrated with a robot.
  - Parameters:
    - socketFd: Socket file descriptor for communication.
    - robotNum: Identifier for the robot.
    - visionNum: Identifier for the vision system.
    - vsPamrm: VisionRange structure to store the retrieved range data.
  - Returns: Result status.
```

----------------------------------------

TITLE: RemoteControl Structure Fields
DESCRIPTION: Defines the members of the RemoteControl structure, which are used to control remote operations. Includes values for starting, stopping, and pausing, as well as program data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h_source

LANGUAGE: APIDOC
CODE:
```
RemoteControl:
  stopValue: int
    Description: Value associated with stopping a remote operation.
    Definition: nrc_io_parameter.h:31
  pauseValue: int
    Description: Value associated with pausing a remote operation.
    Definition: nrc_io_parameter.h:29
  program: std::vector< RemoteProgram >
    Description: A vector containing remote program data.
    Definition: nrc_io_parameter.h:33
  startPort: int
    Description: Port number for starting a remote operation.
    Definition: nrc_io_parameter.h:24
  pausePort: int
    Description: Port number for pausing a remote operation.
    Definition: nrc_io_parameter.h:23
  startValue: int
    Description: Value associated with starting a remote operation.
    Definition: nrc_io_parameter.h:30
```

----------------------------------------

TITLE: Remote Function Configuration
DESCRIPTION: Functions to set and get remote functions for a specified robot. This involves configuring general remote control settings and a list of remote programs, with a default count.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
Result set_remote_function(SOCKETFD socketFd, int robotNum, RemoteControl general, std::vector<RemoteProgram> program, int num = 10);
Result get_remote_function(SOCKETFD socketFd, int robotNum, int& num, int& time, RemoteControl& general, std::vector<RemoteProgram>& program);
```

----------------------------------------

TITLE: Send Queue Data to Controller
DESCRIPTION: Transmits a specified number of elements from the local queue to the robot controller. It requires a socket file descriptor and the size of the data to send. A warning indicates that the controller starts motion immediately upon receiving the queue, so status should be set beforehand.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__queue__operate_8h

LANGUAGE: c
CODE:
```
int queue_motion_send_to_controller_c(SOCKETFD _socketFd_, int _size_);

/*
Parameters:
     size: The number of queue elements to send.

Returns:
    0-Success, -1-Failure, -2-Robot not connected, -3-Provided size exceeds queue length.

Warning:
    The controller will start motion immediately after receiving the queue. Call queue_motion_set_status(true) before this function.
*/
```

----------------------------------------

TITLE: Robot Linear Movement
DESCRIPTION: Executes a linear movement for the robot. Requires a valid socket file descriptor, coordinate system, velocity, acceleration, deceleration, and target position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
robot_movel_c(SOCKETFD socketFd, int coord, double vel, double acc, double dec, double *targetPos)
  - Performs a linear movement.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - coord: The coordinate system for the movement.
    - vel: The velocity for the movement.
    - acc: The acceleration for the movement.
    - dec: The deceleration for the movement.
    - targetPos: Pointer to the target position array.
  - Returns: An integer status code.
```

----------------------------------------

TITLE: Add TIGWELDON command to queue
DESCRIPTION: Appends a TIGWELDON (TIG welding start) command to the local motion queue. Requires a socket file descriptor, type, and two double-precision values (l1, l2).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_tigweld_on(SOCKETFD socketFd, int type, double l1, double l2)
```

----------------------------------------

TITLE: Set Remote Functions and Parameters
DESCRIPTION: Functions to set remote functions, parameters, and programs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_s

LANGUAGE: APIDOC
CODE:
```
set_remote_function(function_name, args)
  - Sets a remote function to be executed.
  - Parameters:
    - function_name: The name of the remote function.
    - args: Arguments for the remote function.

set_remote_param(param_name, value)
  - Sets a remote parameter.
  - Parameters:
    - param_name: The name of the remote parameter.
    - value: The value for the parameter.

set_remote_program(program_name, code)
  - Sets a remote program.
  - Parameters:
    - program_name: The name of the remote program.
    - code: The code for the remote program.
```

----------------------------------------

TITLE: Get Sensor Data for Specific Robot
DESCRIPTION: Retrieves sensor data for a particular robot, identified by its number, using the socket file descriptor. The data is returned as a vector of integers.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_sensor_data_robot(SOCKETFD socketFd, int robotNum, std::vector<int>& data);
```

----------------------------------------

TITLE: Add end_while Command to Queue
DESCRIPTION: Adds an ENDWHILE instruction to the local queue, marking the end of a conditional execution block started by a WHILE command.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_end_while(SOCKETFD socketFd)
// 队列运动模式的本地队列最后插入一条ENDWHILE(结束直到)指令
```

----------------------------------------

TITLE: Set Conveyor Belt Identification Parameters
DESCRIPTION: Configures identification parameters for conveyor belt tracking. This function is used for general conveyor belt tracking setups.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h_source

LANGUAGE: APIDOC
CODE:
```
conveyor_belt_tracking_set_identification_parameter(SOCKETFD socketFd, int conveyorID, int detectSrcType, int capturePos, int visionID, int visionIoFilterType, int visionLatchEncoderValueType, int communication, int sensorTrg, int type)
  Parameters:
    socketFd: File descriptor for the socket connection.
    conveyorID: Identifier for the conveyor belt.
    detectSrcType: Type of detection source.
    capturePos: Capture position.
    visionID: Vision system identifier.
    visionIoFilterType: Vision I/O filter type.
    visionLatchEncoderValueType: Vision latch encoder value type.
    communication: Communication setting.
    sensorTrg: Sensor trigger setting.
    type: Type parameter.
  Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: HMI Library Structures Overview
DESCRIPTION: This section lists the primary data structures available in the HMI library. Each structure is linked to its detailed documentation. These structures are fundamental for configuring and interacting with robotic systems and their peripherals.

SOURCE: https://doc.hmilib.inexbot.coision.cn/classes

LANGUAGE: APIDOC
CODE:
```
ParaGroup:
  Structure for parameter grouping.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_para_group.html

Position:
  Structure defining a position.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_position.html

PositionData:
  Structure for position-related data.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_position_data.html

Protocol:
  Structure defining communication protocols.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_protocol.html

RemoteControl:
  Structure for remote control functionalities.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_remote_control.html

RemoteProgram:
  Structure for remote program management.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_remote_program.html

RemoteProgramSetting:
  Structure for remote program settings.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_remote_program_setting.html

RobotDHParam:
  Structure for Robot's Denavit-Hartenberg parameters.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_robot_d_h_param.html

RobotJointParam:
  Structure for Robot's joint parameters.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_robot_joint_param.html

SafeIO:
  Structure for safety input/output configurations.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_safe_i_o.html

ServoMovePara:
  Structure for servo motor movement parameters.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_servo_move_para.html

Socket:
  Structure for socket communication.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_socket.html

ToolParam:
  Structure for tool parameters.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_tool_param.html

Trigger:
  Structure for trigger configurations.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_trigger.html

VisionCalibrationData:
  Structure for vision calibration data.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_vision_calibration_data.html

VisionParam:
  Structure for vision system parameters.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_vision_param.html

VisionPositionParam:
  Structure for vision-based position parameters.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_vision_position_param.html

VisionRange:
  Structure defining vision detection ranges.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_vision_range.html

WaveParam:
  Structure for wave-related parameters.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_wave_param.html

WeldState:
  Structure representing the welding state.
  Link: https://doc.hmilib.inexbot.coision.cn/struct_weld_state.html
```

----------------------------------------

TITLE: Get General Speed
DESCRIPTION: Retrieves the current speed for the general connection. Requires a socket file descriptor. The speed is returned via a reference parameter. Returns a Result status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
Result get_speed(SOCKETFD socketFd, int& speed);
```

----------------------------------------

TITLE: Conveyor Belt Get Wait Point Parameters
DESCRIPTION: Retrieves the wait point parameters for a conveyor belt. The retrieved parameters are stored in the provided ConveyorWaitPointParams structure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h_source

LANGUAGE: APIDOC
CODE:
```
conveyor_belt_get_wait_point_paramters(SOCKETFD socketFd, int conveyorID, ConveyorWaitPointParams &param)
  - Retrieves wait point parameters for a conveyor belt.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - conveyorID: The ID of the conveyor belt.
    - param: A reference to a ConveyorWaitPointParams structure to store the retrieved parameters.
  - Returns: Result status of the operation.
```

----------------------------------------

TITLE: End Motion While
DESCRIPTION: Signals the end of a motion sequence that was previously started with a 'while' condition. This function is used to manage the lifecycle of motion queues.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_end_while(SOCKETFD socketFd);
```

----------------------------------------

TITLE: End Motion Until
DESCRIPTION: Signals the end of a motion sequence that was previously started with an 'until' condition. This function is used in conjunction with other queue management functions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_end_until(SOCKETFD socketFd);
```

----------------------------------------

TITLE: Insert Conveyor Check Position Command
DESCRIPTION: Inserts a CONVEYOR_CHECKPOS (conveyor workpiece detection start) instruction into the job file. This function initiates the workpiece detection process on a conveyor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_check_pos(SOCKETFD socketFd, int line, int id)
  - Inserts a CONVEYOR_CHECKPOS instruction.
  - Parameters:
    - socketFd: File descriptor for the socket.
    - line: The line number in the job file.
    - id: The job ID.
```

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_check_pos_robot(SOCKETFD socketFd, int robotNum, int line, int id)
  - Inserts a CONVEYOR_CHECKPOS instruction for a specific robot.
  - Parameters:
    - socketFd: File descriptor for the socket.
    - robotNum: The robot number.
    - line: The line number in the job file.
    - id: The job ID.
```

----------------------------------------

TITLE: C++ Function Declarations for Laser Cutting
DESCRIPTION: Declarations for C++ functions used in laser cutting operations. These functions handle setting and getting various parameters for laser cutting, including global, craft, I/O, and analog parameters, with specific overloads for individual robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: C++
CODE:
```
EXPORT_API Result laser_cutting_get_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam &param);
EXPORT_API Result laser_cutting_set_global_parameter(SOCKETFD socketFd, LaserCuttingGlobalParam param);
EXPORT_API Result laser_cutting_get_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam &param);
EXPORT_API Result laser_cutting_get_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam &param);
EXPORT_API Result laser_cutting_set_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam param);
EXPORT_API Result laser_cutting_get_global_parameter(SOCKETFD socketFd, LaserCuttingGlobalParam &param);
EXPORT_API Result laser_cutting_get_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam &param);
EXPORT_API Result laser_cutting_set_global_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingGlobalParam param);
EXPORT_API Result laser_cutting_set_io_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingIOParam param);
EXPORT_API Result laser_cutting_set_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam param);
EXPORT_API Result laser_cutting_set_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam param);
EXPORT_API Result laser_cutting_get_global_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingGlobalParam &param);
EXPORT_API Result laser_cutting_set_io_parameter(SOCKETFD socketFd, LaserCuttingIOParam param);
EXPORT_API Result laser_cutting_get_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam &param);
EXPORT_API Result laser_cutting_set_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam param);
EXPORT_API Result laser_cutting_get_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam &param);
```

----------------------------------------

TITLE: Laser Cutting IO Parameters
DESCRIPTION: Details input/output parameters for laser cutting operations, such as regulator faults.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_r

LANGUAGE: APIDOC
CODE:
```
LaserCuttingIOParam:
  regulator_fault : Status indicating a fault in the regulator.
```

----------------------------------------

TITLE: Get Current Coordinate System
DESCRIPTION: Retrieves the current coordinate system of the robot. It requires a socket file descriptor and an integer reference to store the coordinate system type.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_current_coord(SOCKETFD _socketFd_, int & _coord_)
  Description: Obtains the robot's current coordinate system.
  Parameters:
    _socketFd_: File descriptor for the socket connection.
    _coord_: Reference to an integer to store the coordinate system type.
      0: Joint
      1: Cartesian
      2: Tool
      3: User
```

----------------------------------------

TITLE: Get Static Search Position
DESCRIPTION: Retrieves the coordinates for static search positioning. This function requires file ID, table ID, and a delay time to fetch the position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_static_search_position(SOCKETFD _socketFd_, int _fileid_, int _tableid_, int _delaytime_, std::vector< double > & _pos_)
  - Retrieves coordinates for static search positioning.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _fileid_: The file number for search.
    - _tableid_: The table number for search parameters.
    - _delaytime_: The delay time for the parameter table.
    - _pos_: A reference to a std::vector<double> to store the position coordinates.
```

LANGUAGE: C++
CODE:
```
Result get_static_search_position(SOCKETFD _socketFd_, int _fileid_, int _tableid_, int _delaytime_, std::vector< double > & _pos_)
```

----------------------------------------

TITLE: ModbusMasterParameter::startAddress Attribute
DESCRIPTION: The 'startAddress' attribute is a boolean flag that indicates whether the Modbus communication should start from a specific address. Its default value is false.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_modbus_master_parameter

LANGUAGE: cpp
CODE:
```
bool ModbusMasterParameter::startAddress = false;
```

----------------------------------------

TITLE: Laser Cutting Analog Parameter Functions
DESCRIPTION: Provides functions to set and get analog parameters for laser cutting. These functions can be used for a single robot or a specified robot number. They require a socket file descriptor and the relevant parameter structure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam param)
  Sets the analog parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: The LaserCuttingAnalogParam structure containing the parameters.

laser_cutting_set_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam param)
  Sets the analog parameters for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
    param: The LaserCuttingAnalogParam structure containing the parameters.

laser_cutting_get_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam &param)
  Retrieves the analog parameters for laser cutting.
  Parameters:
    socketFd: The socket file descriptor.
    param: A reference to the LaserCuttingAnalogParam structure to store the retrieved parameters.

laser_cutting_get_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam &param)
  Retrieves the analog parameters for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
    param: A reference to the LaserCuttingAnalogParam structure to store the retrieved parameters.
```

----------------------------------------

TITLE: HMILIB Interface Functions
DESCRIPTION: This section lists various functions related to the HMILIB interface, likely for interacting with hardware or system components. The specific functionalities are not detailed but are referenced via links.

SOURCE: https://doc.hmilib.inexbot.coision.cn/doxygen_crawl

LANGUAGE: c++
CODE:
```
// Function declarations and definitions would be here, linked from the provided URLs.
// Example placeholder for a function:
// void process_interface_data(InterfaceData* data);

```

----------------------------------------

TITLE: Get IO Reset Function
DESCRIPTION: Configures IO reset functions, including remote IO reset, mode switching, and error handling. It allows specifying the robot number and reset type.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_IO_reset_function(SOCKETFD _socketFd_, int _robotNum_, int _type_, std::vector< int > & _enable_, std::vector< int > & _value_)
  - Retrieves IO reset related parameters.
  - Parameters:
    - _socketFd_: Socket file descriptor for communication.
    - _robotNum_: Robot number (1-4).
    - _type_: Reset type: 1 for remote IO reset, 2 for mode switch stop, 3 for program error.
    - _enable_: Vector indicating whether to reset containers; size corresponds to all IO board output ports.
    - _value_: Vector for reset values; size corresponds to all IO board output ports.
  - Returns: Result status.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_IO_reset_function(SOCKETFD _socketFd_, int _robotNum_, int _type_, std::vector< int > & _enable_, std::vector< int > & _value_);
```

----------------------------------------

TITLE: nrc_parameter.h Header File
DESCRIPTION: This C++ header file, nrc_parameter.h, includes definitions and parameters for various NRC (likely Network Resource Controller) functionalities. It aggregates other header files related to define, interface parameters, I/O parameters, Modbus parameters, and craft-specific parameters for conveyor belts, laser cutting, vision, and welding.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__parameter_8h_source

LANGUAGE: c++
CODE:
```
#ifndef INTERFACE_PARAMETER_NRC_PARAMETER_H_
#define INTERFACE_PARAMETER_NRC_PARAMETER_H_

#include "nrc_define.h"
#include "nrc_interface_parameter.h"
#include "nrc_io_parameter.h"
#include "nrc_modbus_parameter.h"
#include "nrc_craft_conveyor_belt_track_parameter.h"
#include "nrc_craft_laser_cutting_parameter.h"
#include "nrc_craft_vision_parameter.h"
#include "nrc_craft_weld_parameter.h"

#endif /* INTERFACE_PARAMETER_NRC_PARAMETER_H_ */
```

----------------------------------------

TITLE: Job Execution
DESCRIPTION: Executes a job identified by its name. Requires a socket file descriptor and the job name.

SOURCE: https://doc.hmilib.inexbot.coision.cn/test

LANGUAGE: cpp
CODE:
```
job_run(SOCKETFD socketFd, const std::string &jobName)
```

----------------------------------------

TITLE: WaveParam Structure Members
DESCRIPTION: This section lists all members of the WaveParam structure, including inherited members. It provides details on each member's name and its corresponding documentation link.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_wave_param-members

LANGUAGE: APIDOC
CODE:
```
WaveParam Member List

This is the complete list of members for WaveParam, including all inherited members.

- horizontalDeflection: Represents horizontal deflection.
- initialDir: Represents the initial direction.
- leftStayTime: Represents the time spent staying on the left.
- LTypeAngle: Represents the angle of type L.
- moveWhenEdgeStay: Indicates movement when staying at an edge.
- radius: Represents the radius.
- rightStayTime: Represents the time spent staying on the right.
- swingAmplitude: Represents the amplitude of the swing.
- swingFreq: Represents the frequency of the swing.
- type: Represents the type.
- verticalDeflection: Represents vertical deflection.
```

----------------------------------------

TITLE: Robot Position Management
DESCRIPTION: Functions for retrieving and setting global and user-defined robot positions. This includes getting the current position, setting named global positions, and managing user coordinate numbers.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_current_position_robot(socketFd: SOCKETFD, robotNum: int, coord: int, pos: std::vector<double>&) -> Result
  Retrieves the current position of a specific robot.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    robotNum: The number of the robot.
    coord: The coordinate system to use.
    pos: A reference to a vector to store the position data.
  Returns:
    A Result indicating success or failure.

get_global_position(socketFd: SOCKETFD, posName: std::string, pos: std::vector<double>&) -> Result
  Retrieves a named global position.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    posName: The name of the global position to retrieve.
    pos: A reference to a vector to store the position data.
  Returns:
    A Result indicating success or failure.

set_global_position(socketFd: SOCKETFD, posName: std::string, posInfo: std::vector<double>) -> Result
  Sets a named global position.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    posName: The name of the global position to set.
    posInfo: A vector containing the position data.
  Returns:
    A Result indicating success or failure.

get_user_coord_number_robot(socketFd: SOCKETFD, robotNum: int, userNum: int&) -> Result
  Retrieves the current user coordinate number for a robot.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    robotNum: The number of the robot.
    userNum: A reference to an integer to store the user coordinate number.
  Returns:
    A Result indicating success or failure.

set_user_coord_number_robot(socketFd: SOCKETFD, robotNum: int, userNum: int) -> Result
  Sets the user coordinate number for a robot.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    robotNum: The number of the robot.
    userNum: The user coordinate number to set.
  Returns:
    A Result indicating success or failure.

get_user_coord_number(socketFd: SOCKETFD, userNum: int&) -> Result
  Retrieves the current user coordinate number.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    userNum: A reference to an integer to store the user coordinate number.
  Returns:
    A Result indicating success or failure.

calculate_user_coordinate(socketFd: SOCKETFD, userNumber: int) -> Result
  Calculates a user coordinate.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    userNumber: The user coordinate number to calculate.
  Returns:
    A Result indicating success or failure.

get_current_position(socketFd: SOCKETFD)
  Retrieves the current position (details not fully specified in the provided snippet).
```

----------------------------------------

TITLE: Get Analog Output Data
DESCRIPTION: Retrieves analog output data from a specified socket. The data is stored in a vector of doubles. The maximum length of the output array is 64.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_analog_output(_socketFd_: SOCKETFD, _aout_: std::vector< double > &)
  - Queries analog output.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _aout_: Vector to store analog output values (max length 64).
```

LANGUAGE: C++
CODE:
```
Result get_analog_output(SOCKETFD _socketFd_, std::vector< double > & _aout_)
```

----------------------------------------

TITLE: Get Analog Input Data
DESCRIPTION: Retrieves analog input data from a specified socket. The data is stored in a vector of doubles. The maximum length of the input array is 64.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_analog_input(_socketFd_: SOCKETFD, _ain_: std::vector< double > &)
  - Queries analog input.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _ain_: Vector to store analog input values (max length 64).
```

LANGUAGE: C++
CODE:
```
Result get_analog_input(SOCKETFD _socketFd_, std::vector< double > & _ain_)
```

----------------------------------------

TITLE: Vision Basic Parameter Management
DESCRIPTION: Functions for setting and retrieving basic parameters of the vision system. These parameters are fundamental for the vision system's operation. They require a socket file descriptor, vision system identifier, and a VisionParam structure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h_source

LANGUAGE: APIDOC
CODE:
```
vision_set_basic_parameter(SOCKETFD socketFd, int visionNum, VisionParam vsPamrm)
  - Sets the basic parameters for a vision system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - visionNum: The identifier for the vision system.
    - vsPamrm: The VisionParam structure containing the parameters to set.
  - Returns: Result status of the operation.
```

LANGUAGE: APIDOC
CODE:
```
vision_get_basic_parameter(SOCKETFD socketFd, int visionNum, VisionParam &vsPamrm)
  - Retrieves the basic parameters of a vision system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - visionNum: The identifier for the vision system.
    - vsPamrm: A reference to a VisionParam structure to store the retrieved parameters.
  - Returns: Result status of the operation.
```

LANGUAGE: APIDOC
CODE:
```
vision_set_basic_parameter_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionParam vsPamrm)
  - Sets the basic parameters for a vision system associated with a robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - visionNum: The identifier for the vision system.
    - vsPamrm: The VisionParam structure containing the parameters to set.
  - Returns: Result status of the operation.
```

LANGUAGE: APIDOC
CODE:
```
vision_get_basic_parameter_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionParam &vsPamrm)
  - Retrieves the basic parameters of a vision system associated with a robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - visionNum: The identifier for the vision system.
    - vsPamrm: A reference to a VisionParam structure to store the retrieved parameters.
  - Returns: Result status of the operation.
```

----------------------------------------

TITLE: Samov Command Insertion
DESCRIPTION: Functions for inserting samov commands into a job. Includes a standard version and a robot-specific version.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_j

LANGUAGE: C++
CODE:
```
job_insert_samov_command()
job_insert_samov_command_robot()
```

----------------------------------------

TITLE: C++ HMI Library - Tracking Functions
DESCRIPTION: C++ declarations for robot trajectory recording functions, including saving, starting, and stopping.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h

LANGUAGE: C++
CODE:
```
#include "nrc_track.h"

// Example usage (conceptual):
// int socket_fd = ...;
// int robot_number = 1;
// std::string trajectory_name = "my_trajectory";
// double max_samples = 5000.0;
// double sample_interval = 0.05;

// track_record_save_robot(socket_fd, robot_number, trajectory_name);
// track_record_start(socket_fd, max_samples, sample_interval);
// track_record_start_robot(socket_fd, robot_number, max_samples, sample_interval);
// track_record_stop(socket_fd);
// track_record_stop_robot(socket_fd, robot_number);
```

----------------------------------------

TITLE: Get Single Cycle Data for Robot
DESCRIPTION: Retrieves single cycle data for a specific robot. This function populates a vector with 7 double values representing the cycle data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_single_cycle_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< double > & _single_cycle_)
  - Retrieves single cycle data for a specific robot.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _robotNum_: The robot number.
    - _single_cycle_: A reference to a std::vector<double> to store the cycle data. The vector is expected to have a length of 7.
```

LANGUAGE: C++
CODE:
```
Result get_single_cycle_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< double > & _single_cycle_)
```

----------------------------------------

TITLE: Get Current Motor Torque
DESCRIPTION: Retrieves the current motor torque for the robot and external axes. The torque is reported in percentage. This function requires a valid SOCKETFD for communication.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_torque(SOCKETFD _socketFd_, std::vector< int > & _motorTorque_, std::vector< int > & _motorTorqueSync_)
  - Retrieves current motor torque for robot and external axes.
  - Parameters:
    - _socketFd_: File descriptor for socket communication.
    - _motorTorque_: Output vector (size 7) for robot motor torque in [%].
    - _motorTorqueSync_: Output vector (size 5) for external axis motor torque in [%].
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_curretn_motor_torque(SOCKETFD _socketFd_, std::vector< int > & _motorTorque_, std::vector< int > & _motorTorqueSync_);
```

----------------------------------------

TITLE: Static Search Position Retrieval
DESCRIPTION: Retrieves the coordinates for a static search position. This function is used to get predefined positional data based on file and table IDs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_static_search_position(SOCKETFD socketFd, int fileid, int tableid, int delaytime, std::vector< double > &pos)
  - Retrieves the coordinates for a static search position.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - fileid: The identifier for the file containing the position data.
    - tableid: The identifier for the table within the file.
    - delaytime: A delay time parameter, its specific use may vary.
    - pos: Reference to a vector of doubles to store the retrieved position coordinates.
  - Returns: Result status of the operation.
  - Usage: 获取静态寻位坐标
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_static_search_position(SOCKETFD socketFd, int fileid, int tableid, int delaytime, std::vector< double > &pos);
// 获取静态寻位坐标
```

----------------------------------------

TITLE: Vision Craft Queue Operations
DESCRIPTION: Functions for managing vision craft operations within the queue system. These include starting the craft, retrieving its position, and triggering visual events. Some functions are robot-specific.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: c++
CODE:
```
queue_motion_push_back_vision_craft_start_robot(SOCKETFD socketFd, int robotNum, int id);
queue_motion_push_back_vision_craft_get_pos(SOCKETFD socketFd, int id, const std::string posName);
queue_motion_push_back_vision_craft_get_pos_robot(SOCKETFD socketFd, int robotNum, int id, const std::string posName);
queue_motion_push_back_vision_craft_visual_trigger(SOCKETFD socketFd, int id);
queue_motion_push_back_vision_craft_visual_trigger_robot(SOCKETFD socketFd, int robotNum, int id);
queue_motion_push_back_vision_craft_visual_end(SOCKETFD socketFd, int id);
queue_motion_push_back_vision_craft_visual_end_robot(SOCKETFD socketFd, int robotNum, int id);
```

----------------------------------------

TITLE: Get Current Job File
DESCRIPTION: Retrieves the current job file name for a specific robot. This function is part of the HMI library's job operation capabilities.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: cpp
CODE:
```
Result job_get_current_file_robot(SOCKETFD _socketFd_, int _robotNum_, std::string& _jobName_)
```

----------------------------------------

TITLE: Servo and Power Control
DESCRIPTION: Functions to manage the robot's servo state, including setting servo on/off and powering the robot on or off. These operations require a valid socket file descriptor.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_servo_state_c(SOCKETFD socketFd, int state)
  Sets the servo state of the robot.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
    state: The desired servo state.
  Returns: An integer status code.

set_servo_poweroff_c(SOCKETFD socketFd)
  Powers off the robot's servos.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
  Returns: An integer status code.

set_servo_poweron_c(SOCKETFD socketFd)
  Powers on the robot's servos.
  Parameters:
    socketFd: The socket file descriptor for the robot connection.
  Returns: An integer status code.
```

----------------------------------------

TITLE: Get General Coordinate
DESCRIPTION: Retrieves the current coordinate for the general connection. Requires a socket file descriptor. The coordinate is returned via a reference parameter. Returns a Result status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
Result get_current_coord(SOCKETFD socketFd, int& coord);
```

----------------------------------------

TITLE: Enumeration Values in net_lib
DESCRIPTION: This section lists all enumeration values defined within the net_lib library, categorized by their starting letter. Each enum value is linked to its definition in the respective header file.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_eval

LANGUAGE: c
CODE:
```
DISCONNECT : nrc_define.h
EQUAL_TO : nrc_define.h
EXCEPTION : nrc_define.h
FIVE_AXLE_GENERAL : nrc_interface_parameter.h
FIVE_AXLE_MIXED : nrc_interface_parameter.h
FOUR_AXLE_ANGLE : nrc_interface_parameter.h
FOUR_AXLE_GENERAL : nrc_interface_parameter.h
FOUR_AXLE_PALLET_1 : nrc_interface_parameter.h
FOUR_AXLE_POLAR_ABNORMITY : nrc_interface_parameter.h
FOUR_AXLE_SCARA : nrc_interface_parameter.h
FOUR_AXLE_SCARA_ABNORMITY : nrc_interface_parameter.h
FOUR_AXLE_SCARA_ABNORMITY_3_ : nrc_interface_parameter.h
FOUR_AXLE_STACK : nrc_interface_parameter.h
GANTRY_WELD : nrc_interface_parameter.h
GREATER : nrc_define.h
GREATER_EQUAL : nrc_define.h
```

----------------------------------------

TITLE: Weld Control and Setting Functions
DESCRIPTION: Provides functions to set various welding control parameters and states. This includes setting general configuration, enabling/disabling welding, controlling feed wire, performing hand spot welding, rewinding wire, and supplying gas. Robot-specific versions are also available for each function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_w

LANGUAGE: APIDOC
CODE:
```
weld_set_config(config)
  - Sets the general welding configuration.
  - Parameters:
    - config: The welding configuration to set.
  - Returns: Status of the operation.

weld_set_config_robot(config)
  - Sets the robot-specific welding configuration.
  - Parameters:
    - config: The robot-specific welding configuration to set.
  - Returns: Status of the operation.

weld_set_enable(enable)
  - Enables or disables the welding process.
  - Parameters:
    - enable: Boolean indicating whether to enable (true) or disable (false).
  - Returns: Status of the operation.

weld_set_enable_robot(enable)
  - Enables or disables the robot-specific welding process.
  - Parameters:
    - enable: Boolean indicating whether to enable (true) or disable (false).
  - Returns: Status of the operation.

weld_set_feed_wire(feed_wire_params)
  - Sets the feed wire parameters.
  - Parameters:
    - feed_wire_params: Parameters for the feed wire.
  - Returns: Status of the operation.

weld_set_feed_wire_robot(feed_wire_params)
  - Sets the robot-specific feed wire parameters.
  - Parameters:
    - feed_wire_params: Robot-specific parameters for the feed wire.
  - Returns: Status of the operation.

weld_set_hand_spot(spot_params)
  - Sets parameters for hand spot welding.
  - Parameters:
    - spot_params: Parameters for hand spot welding.
  - Returns: Status of the operation.

weld_set_hand_spot_robot(spot_params)
  - Sets robot-specific parameters for hand spot welding.
  - Parameters:
    - spot_params: Robot-specific parameters for hand spot welding.
  - Returns: Status of the operation.

weld_set_rewind_wire(rewind_params)
  - Sets parameters for rewinding the wire.
  - Parameters:
    - rewind_params: Parameters for wire rewinding.
  - Returns: Status of the operation.

weld_set_rewind_wire_robot(rewind_params)
  - Sets robot-specific parameters for rewinding the wire.
  - Parameters:
    - rewind_params: Robot-specific parameters for wire rewinding.
  - Returns: Status of the operation.

weld_set_supply_gas(gas_params)
  - Sets parameters for supplying gas.
  - Parameters:
    - gas_params: Parameters for gas supply.
  - Returns: Status of the operation.

weld_set_supply_gas_robot(gas_params)
  - Sets robot-specific parameters for supplying gas.
  - Parameters:
    - gas_params: Robot-specific parameters for gas supply.
  - Returns: Status of the operation.
```

----------------------------------------

TITLE: Robot Joint Parameters
DESCRIPTION: Details parameters for robot joints, including software limits.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_p

LANGUAGE: APIDOC
CODE:
```
RobotJointParam:
  posSWLimit: Software limit for the robot joint position.
```

----------------------------------------

TITLE: Execute 7-Point Tool Hand Calibration for Robot
DESCRIPTION: Executes the 7-point calibration process for a robot's tool hand. Requires a socket file descriptor, robot number, the calibration point, and the tool number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: c++
CODE:
```
EXPORT_API Result tool_hand_7_point_calibrate_robot(SOCKETFD _socketFd, int _robotNum, int _point, int _toolNum)
```

----------------------------------------

TITLE: Laser Cutting Craft Parameter Management
DESCRIPTION: Functions to set and get craft-specific laser cutting parameters. These functions allow modification and retrieval of parameters related to specific crafting processes. They require a socket file descriptor and optionally a robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result laser_cutting_set_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam param);
EXPORT_API Result laser_cutting_set_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam param);
EXPORT_API Result laser_cutting_get_craft_parameter(SOCKETFD socketFd, LaserCuttingCraftParam& param);
EXPORT_API Result laser_cutting_get_craft_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingCraftParam& param);
```

----------------------------------------

TITLE: C Interface Header Files
DESCRIPTION: This section lists the header files related to the C interface of the net_lib library. These files define the functions and structures for interacting with the library using C.

SOURCE: https://doc.hmilib.inexbot.coision.cn/dir_d0c6bf0de7ebd6965a32e997f6712374

LANGUAGE: c
CODE:
```
#include "nrc_c_interface.h"
#include "nrc_c_io.h"
#include "nrc_c_queue_operate.h"
```

----------------------------------------

TITLE: Robot Joint Parameters
DESCRIPTION: Details parameters for robot joints, including software limits.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_p

LANGUAGE: APIDOC
CODE:
```
RobotJointParam:
  posSWLimit: Software limit for the robot joint position.
```

----------------------------------------

TITLE: Conveyor Belt Tracking Set Tracking Range
DESCRIPTION: Sets the tracking range parameters for a conveyor belt. This function defines the boundaries and starting point for tracking operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h_source

LANGUAGE: APIDOC
CODE:
```
conveyor_belt_tracking_set_tracking_range(SOCKETFD socketFd, int conveyorID, double receLatestPos, double trackRangeXMax, double trackRangeYMax, double trackRangeYMin, double trackRangeZMax, double trackRangeZMin, double trackStartXPoint)
  - Sets the tracking range parameters for a conveyor belt.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - conveyorID: The ID of the conveyor belt.
    - receLatestPos: The latest received position.
    - trackRangeXMax: Maximum X-axis tracking range.
    - trackRangeYMax: Maximum Y-axis tracking range.
    - trackRangeYMin: Minimum Y-axis tracking range.
    - trackRangeZMax: Maximum Z-axis tracking range.
    - trackRangeZMin: Minimum Z-axis tracking range.
    - trackStartXPoint: The starting X-axis point for tracking.
```

----------------------------------------

TITLE: Get Current Job File (C# Robot)
DESCRIPTION: Retrieves the name of the current job file for a specific robot. Requires a socket file descriptor and robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_get_current_file_csharp_robot(SOCKETFD socketFd, int robotNum, std::vector<char>& jobName);
```

----------------------------------------

TITLE: Get Current Job Line Number
DESCRIPTION: Retrieves the current line number of execution within the opened job file. Overloads are provided for general use and robot-specific use.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_get_current_line(SOCKETFD socketFd, int &line)
  - Retrieves the current line number of the job file execution.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - line: Output parameter to store the current line number.
  - Returns: Result of the operation.

job_get_current_line_robot(SOCKETFD socketFd, int robotNum, int &line)
  - Retrieves the current line number of the job file execution for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The robot number.
    - line: Output parameter to store the current line number.
  - Returns: Result of the operation.
```

----------------------------------------

TITLE: Get Current Motor Torque (Robot Specific)
DESCRIPTION: Retrieves the current motor torque values for a specific robot. This function allows monitoring torque for individual robots in a system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_torque_robot(SOCKETFD socketFd, int robotNum, std::vector< int > &motorTorque, std::vector< int > &motorTorqueSync)
  - Retrieves the current motor torque values for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the target robot.
    - motorTorque: A reference to a vector of integers to store the primary motor torque values.
    - motorTorqueSync: A reference to a vector of integers to store synchronized motor torque values.
  - Returns: void (Torque values are populated in the 'motorTorque' and 'motorTorqueSync' parameters)
```

----------------------------------------

TITLE: Digital Input Functions
DESCRIPTION: Functions to set and get the status of digital inputs, including forcing functionality. `set_force_digital_input` allows setting a digital input with a specified force and value. `get_force_digital_input` retrieves the port and status of digital inputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
Result set_force_digital_input(SOCKETFD socketFd, int port, int force, int value);
Result get_force_digital_input(SOCKETFD socketFd, std::vector<int>& port, std::vector<double>& status);
```

----------------------------------------

TITLE: Queue Motion Push Back IMove Robot
DESCRIPTION: Pushes back an 'immediate move' motion command to the queue, specifically for robot operations. This function facilitates direct point-to-point movements for robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_q

LANGUAGE: c++
CODE:
```
queue_motion_push_back_imove_rbobt()
```

----------------------------------------

TITLE: Class Variables (t)
DESCRIPTION: Lists variables starting with 't' across various classes in the net_lib library. Each entry includes the variable name, its type, and a link to the class definition where it is found.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_t

LANGUAGE: APIDOC
CODE:
```
targetMode : ServoMovePara
  - Description: Mode of the servo movement.

targetPosName : MoveCmd
  - Description: Name of the target position.

targetPosType : MoveCmd
  - Description: Type of the target position.

targetPosValue : MoveCmd
  - Description: Value of the target position.

TCP : ModbusMasterParameter
  - Description: TCP configuration for Modbus master.

threeAxisConversionRatio : RobotDHParam
  - Description: Conversion ratio for three axes in Robot DH parameters.

threeAxisDirection : RobotDHParam
  - Description: Direction for three axes in Robot DH parameters.

time : MoveCmd
  - Description: Time associated with a move command.

timeOut : Protocol
  - Description: Timeout setting for a protocol.

times : RemoteProgramSetting
  - Description: Number of times a remote program setting is applied.

timeStamp : ServoMovePara
  - Description: Timestamp for servo movement parameters.

tool : OffsetCommandParam
  - Description: Tool information for offset commands.

toolNum : MoveCmd, PositionData
  - Description: Tool number associated with move commands and position data.

track_height : ConveyorBasicParams
  - Description: Height of the conveyor track.

track_on_run_mode_with_target_overrun : ConveyorBasicParams
  - Description: Mode for conveyor track operation with target overrun.

trigger : VisionParam
  - Description: Trigger setting for vision parameters.

triggerMode : Trigger
  - Description: Mode of the trigger.

triggerOnce : Trigger
  - Description: Flag to indicate if the trigger should fire only once.

triggerStr : Trigger
  - Description: String representation of the trigger.

twoAxisConversionRatio : RobotDHParam
  - Description: Conversion ratio for two axes in Robot DH parameters.

type : ModbusMasterParameter, PositionData, Protocol, WaveParam
  - Description: Type identifier for various parameters.
```

----------------------------------------

TITLE: Get Current Job File (C#)
DESCRIPTION: Retrieves the current job file name for a specific robot. This function is part of the HMI library's job operation capabilities.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: cpp
CODE:
```
Result job_get_current_file_csharp_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector<char>& _jobName_)
```

----------------------------------------

TITLE: Get Controller ID (Robot Specific)
DESCRIPTION: Retrieves the serial number ID of a specific robot controller. This function allows targeting a particular robot within a multi-robot system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_controller_id_csharp_robot(SOCKETFD socketFd, int robotNum, std::vector< char > &id)
  - Retrieves the serial number ID of a specific robot controller.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the target robot.
    - id: A reference to a vector of characters to store the controller ID.
  - Returns: void (ID is populated in the 'id' parameter)
```

----------------------------------------

TITLE: Insert Samov Command
DESCRIPTION: Inserts a Samov (point-to-point movement) command into a job file. Requires socket file descriptor, line number, movement command parameters, and position data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_samov_command(SOCKETFD _socketFd_, int _line_, MoveCmd _moveCmd_, PositionData _posData_)
  - Inserts a Samov command into the job file.
  - Description: Inserts a point-to-point movement command into the job file.
  - Parameters:
    - _socketFd_: The socket file descriptor.
    - _line_: The line number to insert the command at.
    - _moveCmd_: The movement command parameters. PositionData is used for point data.
    - _posData_: The position data for the movement.
```

LANGUAGE: C
CODE:
```
EXPORT_API Result job_insert_samov_command(SOCKETFD _socketFd_, int _line_, MoveCmd _moveCmd_, PositionData _posData_);
```

----------------------------------------

TITLE: Servo State Control
DESCRIPTION: Functions to set and get the state of servos. Includes options for controlling a single servo or servos associated with a specific robot. The `get_servo_state` functions retrieve the current status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_servo_state(SOCKETFD socketFd, int state)
  - Sets the state of a servo.
  - Parameters:
    - socketFd: The socket file descriptor.
    - state: The desired state for the servo.
  - Returns: Result code.

set_servo_state_robot(SOCKETFD socketFd, int robotNum, int state)
  - Sets the state of a servo for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The number of the robot.
    - state: The desired state for the servo.
  - Returns: Result code.

get_servo_state(SOCKETFD socketFd, int& status)
  - Retrieves the current state of a servo.
  - Parameters:
    - socketFd: The socket file descriptor.
    - status: Reference to an integer to store the servo status.
  - Returns: Result code.

get_servo_state_robot(SOCKETFD socketFd, int robotNum, int& status)
  - Retrieves the current state of a servo for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The number of the robot.
    - status: Reference to an integer to store the servo status.
  - Returns: Result code.
```

----------------------------------------

TITLE: Get Conveyor Belt Wait Point Parameters
DESCRIPTION: Retrieves the wait point parameters for a conveyor belt. Requires a socket file descriptor, conveyor ID, and a structure to store the parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result conveyor_belt_get_wait_point_paramters(SOCKETFD socketFd, int conveyorID, ConveyorWaitPointParams &param)
```

----------------------------------------

TITLE: Robot Servo Control API
DESCRIPTION: Provides functions to control the state and power of robot servos. Includes methods for individual robots and all robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
clear_error_robot(SOCKETFD socketFd, int robotNum)
  Clears errors for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
  Returns: Result of the operation.

set_servo_state(SOCKETFD socketFd, int state)
  Sets the servo state for all robots.
  Parameters:
    socketFd: The socket file descriptor.
    state: The desired servo state.
  Returns: Result of the operation.

set_servo_state_robot(SOCKETFD socketFd, int robotNum, int state)
  Sets the servo state for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
    state: The desired servo state.
  Returns: Result of the operation.

get_servo_state(SOCKETFD socketFd, int &status)
  Gets the servo state for all robots.
  Parameters:
    socketFd: The socket file descriptor.
    status: Reference to an integer to store the servo status.
  Returns: Result of the operation.

get_servo_state_robot(SOCKETFD socketFd, int robotNum, int &status)
  Gets the servo state for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
    status: Reference to an integer to store the servo status.
  Returns: Result of the operation.

set_servo_poweron(SOCKETFD socketFd)
  Powers on the servos for all robots.
  Parameters:
    socketFd: The socket file descriptor.
  Returns: Result of the operation.

set_servo_poweron_robot(SOCKETFD socketFd, int robotNum)
  Powers on the servos for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
  Returns: Result of the operation.

set_servo_poweroff(SOCKETFD socketFd)
  Powers off the servos for all robots.
  Parameters:
    socketFd: The socket file descriptor.
  Returns: Result of the operation.

set_servo_poweroff_robot(SOCKETFD socketFd, int robotNum)
  Powers off the servos for a specific robot.
  Parameters:
    socketFd: The socket file descriptor.
    robotNum: The number of the robot.
  Returns: Result of the operation.

get_current_position(SOCKETFD socketFd, int coord, std::vector< double > &pos)
  Gets the current position of the robot.
  Parameters:
    socketFd: The socket file descriptor.
    coord: The coordinate system to use.
    pos: Reference to a vector to store the position data.
  Returns: Result of the operation.
```

----------------------------------------

TITLE: Set Teach Type (C)
DESCRIPTION: C function to set the teach type. Requires a socket file descriptor and the desired teach type. Assumes the existence of EXPORT_API and Result definitions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: C
CODE:
```
EXPORT_API Result set_teach_type(SOCKETFD socketFd, int type);
```

----------------------------------------

TITLE: Get Controller ID (C#)
DESCRIPTION: Retrieves the controller ID associated with a given socket file descriptor. This function is part of the HMI library's interface for controller communication.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_controller_id_csharp(SOCKETFD socketFd, std::vector<char>& id);
```

----------------------------------------

TITLE: Source Code Link
DESCRIPTION: Provides a direct link to the source code of the nrc_modbus.h file for detailed inspection.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h

LANGUAGE: html
CODE:
```
<a href="https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h_source.html">Go to the source code of this file.</a>
```

----------------------------------------

TITLE: Queue Motion Push Back IMove
DESCRIPTION: Pushes back an 'immediate move' motion command to the queue. This is typically used for point-to-point movements.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_q

LANGUAGE: c++
CODE:
```
queue_motion_push_back_imove()
```

----------------------------------------

TITLE: LaserCuttingIOParam Variables
DESCRIPTION: Input/Output parameters for laser cutting, specifically related to the water cooler status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_w

LANGUAGE: APIDOC
CODE:
```
LaserCuttingIOParam:
  water_cooler_fault: Status indicator for a water cooler fault.
```

----------------------------------------

TITLE: Get Origin Coordinate to Target Coordinate (General)
DESCRIPTION: Transforms a position from an origin coordinate system to a target coordinate system. Requires a socket file descriptor, origin/target coordinate identifiers, origin position, and a target position vector.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_origin_coord_to_target_coord(SOCKETFD socketFd, int originCoord, std::vector<double> originPos, int targetCoord, std::vector<double>& targetPos)
  - Transforms a position from an origin coordinate system to a target coordinate system.
  - Parameters:
    - socketFd: The file descriptor for the socket connection.
    - originCoord: The identifier for the origin coordinate system.
    - originPos: A vector representing the position in the origin coordinate system.
    - targetCoord: The identifier for the target coordinate system.
    - targetPos: A reference to a vector where the transformed position will be stored.
```

----------------------------------------

TITLE: RemoteControl Structure
DESCRIPTION: Defines the structure for remote control, including ports for various operations like clearing stash, fault reset, pausing, starting, and stopping. It also includes corresponding value fields and a vector of RemoteProgram settings.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h_source

LANGUAGE: cpp
CODE:
```
struct RemoteControl {
    int clearStashPort;
    int faultResetPort;
    int pausePort;
    int startPort;
    int stopPort;
    int clearStashValue;
    int faultResetValue;
    int pauseValue;
    int startValue;
    int stopValue;
    std::vector<RemoteProgram> program;
};
```

----------------------------------------

TITLE: Get Motion Queue Size
DESCRIPTION: Retrieves the current size of the motion queue for a specific robot. The size is returned by reference. Requires a socket file descriptor and the robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_size(SOCKETFD socketFd, int& size);
EXPORT_API Result queue_motion_size_robot(SOCKETFD socketFd, int robotNum, int& size);
```

----------------------------------------

TITLE: Add TOFFSETON Command to Motion Queue
DESCRIPTION: Appends a TOFFSETON (trajectory offset start) instruction to the end of the local queue for motion mode.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: APIDOC
CODE:
```
queue_motion_push_back_TOFFSETON(SOCKETFD socketFd, OffsetCommandParam params)
  - Appends a TOFFSETON (trajectory offset start) instruction to the motion queue.
  - Parameters:
    - socketFd: The socket file descriptor.
    - params: The parameters for the offset command.
  - Returns: Result (likely indicating success or failure).
```

----------------------------------------

TITLE: VisionCalibrationData Members and Constructor
DESCRIPTION: Outlines the members of the VisionCalibrationData structure, namely 'visionNum' for the vision identifier and 'calibration' for the calibration data. It also details the constructor's initialization process.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision__parameter_8h_source

LANGUAGE: APIDOC
CODE:
```
VisionCalibrationData:
  Definition: nrc_craft_vision_parameter.h:117

  Members:
    visionNum: int - Identifier for the vision system. Defined in nrc_craft_vision_parameter.h:118.
    calibration: Calibration - Contains the calibration data. Defined in nrc_craft_vision_parameter.h:119.

  Methods:
    VisionCalibrationData(int num_points = 6): Constructor for VisionCalibrationData. Initializes visionNum to 0 and the calibration data with the specified number of points. Defined in nrc_craft_vision_parameter.h:120.

```

----------------------------------------

TITLE: Add ARCON Command to Queue
DESCRIPTION: Adds an ARCON (welding start) command to the local motion queue. This function requires a socket file descriptor and a command ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: cpp
CODE:
```
Result queue_motion_push_back_arc_on(SOCKETFD _socketFd, int _id)
```

----------------------------------------

TITLE: Robot Configuration and Parameter Functions
DESCRIPTION: Functions to retrieve robot configuration details, including DH parameters and joint parameters. Both general and robot-specific interfaces are available, along with C interface versions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_robot_configuration()
  Retrieves the current configuration of the robot.

get_robot_configuration_robot()
  Retrieves the current configuration of the robot via the robot interface.

get_robot_dh_param()
  Retrieves the Denavit-Hartenberg (DH) parameters for the robot.

get_robot_dh_param_c()
  Retrieves the DH parameters for the robot using the C interface.

get_robot_dh_param_robot()
  Retrieves the DH parameters for the robot via the robot interface.

get_robot_joint_param()
  Retrieves the joint parameters for the robot.

get_robot_joint_param_c()
  Retrieves the joint parameters for the robot using the C interface.

get_robot_joint_param_robot()
  Retrieves the joint parameters for the robot via the robot interface.
```

----------------------------------------

TITLE: Track Record Management
DESCRIPTION: Functions to control the lifecycle of tracking records. This includes starting, stopping, and retrieving the status of tracking operations. These functions are essential for data acquisition and monitoring.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: c++
CODE:
```
/*
 * @brief Starts a general track record.
 * @param socketFd The socket file descriptor.
 * @param maxSamplingNum The maximum number of samples to record.
 * @param samplingInterval The interval between samples in seconds.
 * @return Result code indicating success or failure.
 */
EXPORT_API Result track_record_start(SOCKETFD socketFd, double maxSamplingNum, double samplingInterval);

/*
 * @brief Starts a track record for a specific robot.
 * @param socketFd The socket file descriptor.
 * @param robotNum The identifier for the robot.
 * @param maxSamplingNum The maximum number of samples to record.
 * @param samplingInterval The interval between samples in seconds.
 * @return Result code indicating success or failure.
 */
EXPORT_API Result track_record_start_robot(SOCKETFD socketFd, int robotNum, double maxSamplingNum, double samplingInterval);

/*
 * @brief Stops a general track record.
 * @param socketFd The socket file descriptor.
 * @return Result code indicating success or failure.
 */
EXPORT_API Result track_record_stop(SOCKETFD socketFd);

/*
 * @brief Stops a track record for a specific robot.
 * @param socketFd The socket file descriptor.
 * @param robotNum The identifier for the robot.
 * @return Result code indicating success or failure.
 */
EXPORT_API Result track_record_stop_robot(SOCKETFD socketFd, int robotNum);

/*
 * @brief Retrieves the status of a general track record.
 * @param socketFd The socket file descriptor.
 * @param recordStart Output parameter to indicate if the record has started.
 * @return Result code indicating success or failure.
 */
EXPORT_API Result get_track_record_status(SOCKETFD socketFd, bool& recordStart);

/*
 * @brief Retrieves the status of a track record for a specific robot.
 * @param socketFd The socket file descriptor.
 * @param robotNum The identifier for the robot.
 * @param recordStart Output parameter to indicate if the record has started.
 * @return Result code indicating success or failure.
 */
EXPORT_API Result get_track_record_status_robot(SOCKETFD socketFd, int robotNum, bool& recordStart);

/*
 * @brief Saves the current track record data.
 * @param socketFd The socket file descriptor.
 * @param trajName The name to save the trajectory under.
 * @return Result code indicating success or failure.
 */
EXPORT_API Result track_record_save(SOCKETFD socketFd, std::string trajName);
```

----------------------------------------

TITLE: Robot Joint Movement
DESCRIPTION: Executes a joint movement for the robot. Requires a valid socket file descriptor, coordinate system, velocity, acceleration, deceleration, and target position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
robot_movej_c(SOCKETFD socketFd, int coord, double vel, double acc, double dec, double *targetPos)
  - Performs a joint movement.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - coord: The coordinate system for the movement.
    - vel: The velocity for the movement.
    - acc: The acceleration for the movement.
    - dec: The deceleration for the movement.
    - targetPos: Pointer to the target position array.
  - Returns: An integer status code.
```

----------------------------------------

TITLE: Vision Position Parameter Management
DESCRIPTION: Functions for setting and getting vision position parameters. These functions interact with a specified vision system or a robot's vision system, requiring a socket file descriptor, vision ID, and a VisionPositionParam structure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h

LANGUAGE: APIDOC
CODE:
```
vision_set_position_parameter(SOCKETFD socketFd, int visionNum, VisionPositionParam vsPamrm)
  - Sets the position parameters for a vision system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - visionNum: The identifier for the vision system.
    - vsPamrm: A VisionPositionParam structure containing the position data.
  - Returns: Result of the operation.

vision_set_position_parameter_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionPositionParam vsPamrm)
  - Sets the position parameters for a robot's vision system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - visionNum: The identifier for the vision system.
    - vsPamrm: A VisionPositionParam structure containing the position data.
  - Returns: Result of the operation.

vision_get_position_parameter(SOCKETFD socketFd, int visionId, VisionPositionParam &vsPamrm)
  - Retrieves the position parameters for a vision system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - visionId: The identifier for the vision system.
    - vsPamrm: A reference to a VisionPositionParam structure to store the retrieved position data.
  - Returns: Result of the operation.

vision_get_position_parameter_robot(SOCKETFD socketFd, int robotNum, int visionId, VisionPositionParam &vsPamrm)
  - Retrieves the position parameters for a robot's vision system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - visionId: The identifier for the vision system.
    - vsPamrm: A reference to a VisionPositionParam structure to store the retrieved position data.
  - Returns: Result of the operation.
```

LANGUAGE: C
CODE:
```
EXPORT_API Result vision_set_position_parameter(SOCKETFD socketFd, int visionNum, VisionPositionParam vsPamrm);
EXPORT_API Result vision_set_position_parameter_robot(SOCKETFD socketFd, int robotNum, int visionNum, VisionPositionParam vsPamrm);
EXPORT_API Result vision_get_position_parameter(SOCKETFD socketFd, int visionId, VisionPositionParam &vsPamrm);
EXPORT_API Result vision_get_position_parameter_robot(SOCKETFD socketFd, int robotNum, int visionId, VisionPositionParam &vsPamrm);
```

----------------------------------------

TITLE: Insert Vision Craft Get Position Instruction
DESCRIPTION: Inserts a VISION_POS instruction to retrieve a position from a vision craft operation. This function is used to capture positional data from a visual process.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_get_pos(SOCKETFD socketFd, int line, int id, const std::string posName)
  - Inserts a VISION_POS instruction to get a position from a vision craft.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - line: The line number in the job file.
    - id: The identifier for the vision craft.
    - posName: The name of the position to retrieve.
```

----------------------------------------

TITLE: Job Control Functions
DESCRIPTION: Provides API documentation for core job control operations. These functions allow for starting, stopping, and managing job execution, including breakpoint execution and retrieving current job information.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: APIDOC
CODE:
```
job_stop_robot(SOCKETFD socketFd, int robotNum)
  - Stops the execution of a job for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to stop.
  - Returns: Result indicating success or failure.

job_run_times(SOCKETFD socketFd, int index)
  - Executes a job based on its index.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - index: The index of the job to run.
  - Returns: Result indicating success or failure.

job_run_times_robot(SOCKETFD socketFd, int robotNum, int index)
  - Executes a job for a specific robot based on its index.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to execute the job on.
    - index: The index of the job to run.
  - Returns: Result indicating success or failure.

job_break_point_run(SOCKETFD socketFd, const std::string& jobName)
  - Executes a job from a breakpoint.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - jobName: The name of the job to execute.
  - Returns: Result indicating success or failure.

job_break_point_run_robot(SOCKETFD socketFd, int robotNum, const std::string& jobName)
  - Executes a job for a specific robot from a breakpoint.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to execute the job on.
    - jobName: The name of the job to execute.
  - Returns: Result indicating success or failure.

job_get_current_file(SOCKETFD socketFd, std::string& jobName)
  - Retrieves the name of the currently executing job.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - jobName: A reference to a string where the job name will be stored.
  - Returns: Result indicating success or failure.

job_get_current_file_robot(SOCKETFD socketFd, int robotNum, std::string& jobName)
  - Retrieves the name of the currently executing job for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to query.
    - jobName: A reference to a string where the job name will be stored.
  - Returns: Result indicating success or failure.

job_get_current_file_csharp(SOCKETFD socketFd, std::vector<char>& jobName)
  - Retrieves the name of the currently executing job, formatted for C#.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - jobName: A reference to a vector of characters where the job name will be stored.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Conveyor Check Position Job Insertion
DESCRIPTION: Inserts a conveyor check position job into the operation file. This function is used to insert a command for conveyor workpiece detection start.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_check_pos(_socketFd, _line, _id)

Description: Inserts a CONVEYOR_CHECKPOS (conveyor workpiece detection start) instruction into the job file.

Parameters:
  _socketFd: SOCKETFD for communication.
  _line: The line number.
  _id: 工艺号 (Process ID).
```

LANGUAGE: C
CODE:
```
EXPORT_API Result job_insert_conveyor_check_pos(SOCKETFD _socketFd, int _line, int _id);
```

----------------------------------------

TITLE: Determine Position Type
DESCRIPTION: A static utility function that determines the PosType based on the provided key string. It checks if the key starts with 'P' to differentiate between PType and data types.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__define_8h_source

LANGUAGE: cpp
CODE:
```
static PosType determineType(const std::string& key) {
    if (key.substr(0, 1) == "P") {
        return PosType::PType;
    }
    return PosType::data;
}
```

----------------------------------------

TITLE: Get Controller ID (C-style)
DESCRIPTION: Retrieves the serial number ID of the controller using a C-style character pointer. Requires a socket file descriptor and a character pointer to store the ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_controller_id(SOCKETFD _socketFd_, char * _id_)
  Description: Retrieves the controller serial number ID.
  Parameters:
    _socketFd_: File descriptor for the socket connection.
    _id_: Pointer to a character array to store the controller serial number ID.
```

----------------------------------------

TITLE: Add TIGWELDON command to robot queue
DESCRIPTION: Appends a TIGWELDON (TIG welding start) command to the specified robot's local motion queue. Requires a socket file descriptor, robot number, type, and two double-precision values (l1, l2).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_tigweld_on_robot(SOCKETFD socketFd, int robotNum, int type, double l1, double l2)
```

----------------------------------------

TITLE: Class Members - Variables
DESCRIPTION: Lists class members that are variables, with links to their respective classes. Includes 'B' in ToolParam and 'baudrate' in ModbusRTUParameter.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_b

LANGUAGE: APIDOC
CODE:
```
ToolParam:
  B: Variable within the ToolParam struct.

ModbusRTUParameter:
  baudrate: Variable representing the baud rate in the ModbusRTUParameter struct.
```

----------------------------------------

TITLE: Get Current Motor Speed
DESCRIPTION: Retrieves the current motor speed for the robot and external axes. It takes a socket file descriptor and output references for motor speed values in RPM.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_speed(SOCKETFD _socketFd_, std::vector< int > & _motorSpeed_, std::vector< int > & _motorSpeedSync_)
  Parameters:
    _socketFd_: The socket file descriptor for communication.
    _motorSpeed_: Output parameter for robot motor speeds (length 7) in RPM.
    _motorSpeedSync_: Output parameter for external axis motor speeds (length 5) in RPM.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_curretn_motor_speed(SOCKETFD _socketFd_, std::vector< int > & _motorSpeed_, std::vector< int > & _motorSpeedSync_);
```

----------------------------------------

TITLE: VisionParam Member Data Documentation
DESCRIPTION: Detailed documentation for each member of the VisionParam struct, including type, name, and source file reference.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_vision_param

LANGUAGE: APIDOC
CODE:
```
VisionParam::cameraList
  Type: CameraList
  Description: Represents the list of cameras configured for vision processing.
  Source: nrc_craft_vision_parameter.h

VisionParam::protocol
  Type: Protocol
  Description: Defines the communication protocol used for vision data.
  Source: nrc_craft_vision_parameter.h

VisionParam::socket
  Type: Socket
  Description: Configuration details for the network socket used for communication.
  Source: nrc_craft_vision_parameter.h

VisionParam::trigger
  Type: Trigger
  Description: Settings related to the trigger mechanism for vision capture.
  Source: nrc_craft_vision_parameter.h

VisionParam::userCoordNum
  Type: int
  Description: Stores the number of user-defined coordinates.
  Default Value: 0
  Source: nrc_craft_vision_parameter.h
```

----------------------------------------

TITLE: ModbusMasterParameter Structure
DESCRIPTION: Defines the parameters for a Modbus master, specifying the communication type (TCP or RTU) and associated parameters. It includes settings for address start and the specific TCP or RTU configurations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__modbus__parameter_8h_source

LANGUAGE: cpp
CODE:
```
struct ModbusMasterParameter {
    std::string type;
    bool startAddress;
    ModbusTCPParameter TCP;
    ModbusRTUParameter RTU;
};
```

----------------------------------------

TITLE: Get Motion Queue Length
DESCRIPTION: Retrieves the number of items in the motion queue for a specific robot. The length is returned by reference. Requires a socket file descriptor and the robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_get_queuelen(SOCKETFD socketFd, int& len);
EXPORT_API Result queue_motion_get_queuelen_robot(SOCKETFD socketFd, int robotNum, int& len);
```

----------------------------------------

TITLE: Insert Robot Vision Craft Get Position Instruction
DESCRIPTION: Inserts a VISION_POS instruction to retrieve a position from a vision craft operation for a specific robot. This function is the robot-specific version of job_insert_vision_craft_get_pos.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_vision_craft_get_pos_robot(SOCKETFD socketFd, int robotNum, int line, int id, const std::string posName)
  - Inserts a VISION_POS instruction to get a position from a vision craft for a specific robot.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - line: The line number in the job file.
    - id: The identifier for the vision craft.
    - posName: The name of the position to retrieve.
```

----------------------------------------

TITLE: Get Remote Program
DESCRIPTION: Retrieves IO remote program settings. This function takes the robot number and returns the number of remote IOs and program settings. Version differences in the 'num' parameter are noted.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_remote_program(SOCKETFD _socketFd_, int _robotNum_, int & _num_, std::vector< RemoteProgramSetting > & _program_)
  - Retrieves IO remote program settings data.
  - Parameters:
    - _socketFd_: Socket file descriptor for communication.
    - _robotNum_: Robot number (1-4).
    - _num_: Number of remote IOs. Version 22.07 defaults to 10 and can only be 10. Versions above 24.03 can set this quantity.
    - _program_: See RemoteProgramSetting for details.
  - Returns: Result status.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_remote_program(SOCKETFD _socketFd_, int _robotNum_, int & _num_, std::vector< RemoteProgramSetting > & _program_);
```

----------------------------------------

TITLE: WaveParam Members
DESCRIPTION: Details members related to wave parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_m

LANGUAGE: APIDOC
CODE:
```
WaveParam:
  moveWhenEdgeStay: Movement behavior when staying at the edge.
```

----------------------------------------

TITLE: Laser Cutting Analog Parameter Management
DESCRIPTION: APIs for managing analog parameters for laser cutting. Includes functions to set and get analog parameters for specific robots and general settings. These functions are used for controlling analog signals in the laser cutting system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h_source

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam param)
  - Sets the analog parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A LaserCuttingAnalogParam structure containing the analog parameters.
  - Returns: Result status of the operation.

laser_cutting_get_analog_parameter(SOCKETFD socketFd, LaserCuttingAnalogParam &param)
  - Retrieves the analog parameters for laser cutting.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A reference to a LaserCuttingAnalogParam structure to store the retrieved analog parameters.
  - Returns: Result status of the operation.

laser_cutting_set_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam param)
  - Sets the analog parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to configure.
    - param: A LaserCuttingAnalogParam structure containing the analog parameters.
  - Returns: Result status of the operation.

laser_cutting_get_analog_parameter_robot(SOCKETFD socketFd, int robotNum, LaserCuttingAnalogParam &param)
  - Retrieves the analog parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot to retrieve parameters from.
    - param: A reference to a LaserCuttingAnalogParam structure to store the retrieved analog parameters.
  - Returns: Result status of the operation.

Related Types:
  - SOCKETFD: Integer representing a socket file descriptor.
  - LaserCuttingAnalogParam: Structure holding analog parameters for laser cutting.
  - Result: Enum or type indicating the success or failure of an operation.
```

----------------------------------------

TITLE: Laser Cutting Equipment Parameters
DESCRIPTION: Details on parameters for laser cutting equipment, including global, I/O, and general cutting parameters. This section outlines the structure and potential values for configuring laser cutting operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/doxygen_crawl

LANGUAGE: APIDOC
CODE:
```
struct_laser_cutting_equipment:
  - Global Parameters: struct_laser_cutting_global_param
  - I/O Parameters: struct_laser_cutting_i_o_param
  - Cutting Parameters: struct_laser_cutting_param

struct_laser_cutting_global_param:
  - a3fd414c1f1101ff20d35c927fe042dab: Description for parameter a3fd414c1f1101ff20d35c927fe042dab

struct_laser_cutting_i_o_param:
  - a22b53c4201a7c453f156c79a7361e5c0: Description for parameter a22b53c4201a7c453f156c79a7361e5c0
  - a48b76acf57c23017230fd4f85ab7e09d: Description for parameter a48b76acf57c23017230fd4f85ab7e09d
  - a6d5de434bce1bcb3ae71c1f8536be040: Description for parameter a6d5de434bce1bcb3ae71c1f8536be040
  - ac6d5ed5ba3ba963d9496d7ab1ce9fe29: Description for parameter ac6d5ed5ba3ba963d9496d7ab1ce9fe29
  - ade08c0b3ad8b6e7f4b0d16c26dc681df: Description for parameter ade08c0b3ad8b6e7f4b0d16c26dc681df

struct_laser_cutting_param:
  - a09d2462808fa58402d06c723d4ca1671: Description for parameter a09d2462808fa58402d06c723d4ca1671
  - a0ba6f02ab92e90e4b11dacd541886db8: Description for parameter a0ba6f02ab92e90e4b11dacd541886db8
  - a44d47415f410bb0df9ea32da343f3fad: Description for parameter a44d47415f410bb0df9ea32da343f3fad
  - a61eabc356be73d8b0be5a09a12612016: Description for parameter a61eabc356be73d8b0be5a09a12612016
  - ac30731d7915c4e92b1e4bc4c42cd8ac6: Description for parameter ac30731d7915c4e92b1e4bc4c42cd8ac6
  - ae116b422915b417e515a6314d594787b: Description for parameter ae116b422915b417e515a6314d594787b
  - af58b968e4880c68adf6ee33275aacbc2: Description for parameter af58b968e4880c68adf6ee33275aacbc2
```

----------------------------------------

TITLE: Set Conveyor Belt Tracking Range
DESCRIPTION: Defines the tracking range for conveyor belt tracking. This function sets various parameters related to the tracking range and starting point.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h_source

LANGUAGE: APIDOC
CODE:
```
conveyor_belt_tracking_set_tracking_range(SOCKETFD socketFd, int conveyorID, double receLatestPos, double trackRangeXMax, double trackRangeYMax, double trackRangeYMin, double trackRangeZMax, double trackRangeZMin, double trackStartXPoint)
  Parameters:
    socketFd: File descriptor for the socket connection.
    conveyorID: Identifier for the conveyor belt.
    receLatestPos: The latest received position.
    trackRangeXMax: Maximum X-axis tracking range.
    trackRangeYMax: Maximum Y-axis tracking range.
    trackRangeYMin: Minimum Y-axis tracking range.
    trackRangeZMax: Maximum Z-axis tracking range.
    trackRangeZMin: Minimum Z-axis tracking range.
    trackStartXPoint: The starting X-point for tracking.
  Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: ModbusMasterParameter Members
DESCRIPTION: This section details the members of the ModbusMasterParameter structure. It includes parameters related to Modbus communication, such as RTU and TCP configurations, and the starting address for data access.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_modbus_master_parameter-members

LANGUAGE: APIDOC
CODE:
```
ModbusMasterParameter:
  RTU
    Description: Represents RTU (Remote Terminal Unit) communication parameters.
    Type: (Details not provided)

  ModbusMasterParameter
    Description: Constructor for the ModbusMasterParameter class.
    Parameters: (Details not provided)

  startAddress
    Description: The starting address for Modbus data access.
    Type: (Details not provided)

  TCP
    Description: Represents TCP/IP communication parameters for Modbus.
    Type: (Details not provided)

  type
    Description: The type of Modbus communication (e.g., RTU or TCP).
    Type: (Details not provided)
```

----------------------------------------

TITLE: Robot Joint Parameter Retrieval (Specific Robot)
DESCRIPTION: Retrieves the joint parameters for a specific robot, identified by its number. This allows for detailed configuration access for individual robots in a multi-robot setup.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_robot_joint_param_robot(SOCKETFD socketFd, int robotNum, int id, RobotJointParam &param)
  - Retrieves joint parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - id: The identifier for the joint or parameter set.
    - param: Reference to a RobotJointParam structure to store the retrieved parameters.
  - Returns: Result status of the operation.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_robot_joint_param_robot(SOCKETFD socketFd, int robotNum, int id, RobotJointParam &param);

```

----------------------------------------

TITLE: HMI Job Download Functions
DESCRIPTION: API documentation for functions related to downloading job files. This includes downloading all job files to a specified directory or downloading log files based on a quantity.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_download_by_directory(SOCKETFD socketFd, const std::string &directoryPath, bool isCover)
  - Downloads all job files to the specified directory.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - directoryPath: The target directory for downloaded files.
    - isCover: Boolean flag to indicate if existing files should be overwritten.
  - Returns: Result indicating success or failure.

log_download_by_quantity(SOCKETFD socketFd, int counts, const std::string &directoryPath)
  - Downloads a specified number of log files to the given directory.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - counts: The number of log files to download.
    - directoryPath: The target directory for downloaded log files.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Get Static Search Position (Robot Specific)
DESCRIPTION: Retrieves the static search position coordinates for a specific robot. This function enables fetching positional data tailored to a particular robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_static_search_position_robot(SOCKETFD socketFd, int robotNum, int fileid, int tableid, int delaytime, std::vector< double > &pos)
  - Retrieves the static search position coordinates for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the target robot.
    - fileid: The ID of the file containing the position data.
    - tableid: The ID of the table within the file.
    - delaytime: A delay time parameter for the search.
    - pos: A reference to a vector of doubles to store the position coordinates.
  - Returns: void (Position coordinates are populated in the 'pos' parameter)
```

----------------------------------------

TITLE: Robot Speed Control APIs
DESCRIPTION: APIs to set and get the speed of robots. These functions require a socket file descriptor and accept or return an integer representing the speed. Speed can be set for teaching, running, or remote modes.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
set_speed(SOCKETFD socketFd, int speed)
  Sets the speed for the current mode (teaching, running, remote).
  Parameters:
    socketFd: The socket file descriptor for communication.
    speed: The desired speed value.

set_speed_robot(SOCKETFD socketFd, int robotNum, int speed)
  Sets the speed for a specified robot in the current mode.
  Parameters:
    socketFd: The socket file descriptor for communication.
    robotNum: The number of the robot.
    speed: The desired speed value.

get_speed(SOCKETFD socketFd, int &speed)
  Gets the current speed for the current mode (teaching, running, remote).
  Parameters:
    socketFd: The socket file descriptor for communication.
    speed: A reference to an integer to store the current speed.

```

----------------------------------------

TITLE: LaserCuttingCraftParam Variables
DESCRIPTION: This section lists variables associated with the LaserCuttingCraftParam class. It includes details about the 'num' variable.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_n

LANGUAGE: APIDOC
CODE:
```
LaserCuttingCraftParam:
  num : [LaserCuttingCraftParam](https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_craft_param.html#a86ca6698d3d07059c26e0bd33ff862db)
    - Description: A numerical identifier or count for laser cutting craft parameters.
```

----------------------------------------

TITLE: Remote Control Parameters
DESCRIPTION: Parameters related to the remote control functionality.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_p

LANGUAGE: APIDOC
CODE:
```
RemoteControl:
  program: Specifies the program to be executed via remote control.
```

----------------------------------------

TITLE: Get Current Motor Torque for Robot
DESCRIPTION: Retrieves the current motor torque specifically for a given robot. This function requires the robot number and a valid SOCKETFD. The torque is reported in percentage.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_torque_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< int > & _motorTorque_, std::vector< int > & _motorTorqueSync_)
  - Retrieves current motor torque for a specific robot.
  - Parameters:
    - _socketFd_: File descriptor for socket communication.
    - _robotNum_: The number of the robot.
    - _motorTorque_: Output vector for robot motor torque in [%].
    - _motorTorqueSync_: Output vector for external axis motor torque in [%].
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_curretn_motor_torque_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< int > & _motorTorque_, std::vector< int > & _motorTorqueSync_);
```

----------------------------------------

TITLE: Parameter Header Files
DESCRIPTION: Header files containing parameter definitions for various craft functionalities within the net_lib library. These files define structures and constants used for configuring different operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/files

LANGUAGE: cpp
CODE:
```
#include "nrc_craft_conveyor_belt_track_parameter.h"
#include "nrc_craft_laser_cutting_parameter.h"
#include "nrc_craft_vision_parameter.h"
#include "nrc_craft_weld_parameter.h"
#include "nrc_define.h"
#include "nrc_interface_parameter.h"
```

----------------------------------------

TITLE: Speed and Position Settings
DESCRIPTION: Functions related to setting and retrieving speed and position information for robot axes and the robot itself.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_axis_zero_position(SOCKETFD socketFd, int axis)
  设置零点位置

set_zero_pos_deviation_robot(SOCKETFD socketFd, int robotNum, int axis, double shift)
  Sets the zero position deviation for a robot axis.

get_curretn_line_speed_and_joint_speed_robot(SOCKETFD socketFd, int robotNum, double &lineSpeed, std::vector< double > &jointSpeed, std::vector< double > &jointSpeedSync)
  Retrieves the current linear and joint speeds for a robot.

get_speed(SOCKETFD socketFd, int &speed)
  Retrieves the current speed setting.
```

----------------------------------------

TITLE: Get Remote Status Tips
DESCRIPTION: Retrieves data for remote status tips, including robot number, outage port and value, and remote program details. This function is part of the IO control module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
get_remote_status_tips(_socketFd: SOCKETFD, _robotNum: int, _num: int &, _outagePort: int &, _outageValue: int &, _program: std::vector<RemoteProgram> &)
  获取远程状态提示功能数据
  Parameters:
    robot: 机器人编号(1-4)
    outagePort: 断电保持数据恢复端口
    outageValue: 断电保持数据恢复端口有效值(0/1/2)
    num: 远程IO数量(22.07版本默认10个，最多也只能是10个,24.03版本以上可以设置该数量)
    program: 远程控制程序参数设置
```

----------------------------------------

TITLE: Get Current Job Line (Robot)
DESCRIPTION: Retrieves the current line number for a specific robot within the job file. Requires a socket file descriptor and robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_get_current_line_robot(SOCKETFD socketFd, int robotNum, int& line);
```

----------------------------------------

TITLE: Get Current Motor Torque
DESCRIPTION: Fetches the current motor torque values for both regular and synchronized states using the socket file descriptor. The torque values are returned in separate vectors of integers.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_curretn_motor_torque(SOCKETFD socketFd, std::vector<int>& motorTorque, std::vector<int>& motorTorqueSync);
```

----------------------------------------

TITLE: Get Robot Speed
DESCRIPTION: Retrieves the current speed of a specified robot. Requires a socket file descriptor and the robot number. The speed is returned via a reference parameter. Returns a Result status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
Result get_speed_robot(SOCKETFD socketFd, int robotNum, int& speed);
```

----------------------------------------

TITLE: Get Robot Current Position
DESCRIPTION: Retrieves the current position of the robot in a specified coordinate system. Requires a valid socket file descriptor, the coordinate system, and a pointer to a double array for the position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_current_position_c(SOCKETFD _socketFd_, int _coord_, double * _pos_)
  - Gets the robot's current position.
  - Parameters:
    - _socketFd_: The socket file descriptor for communication.
    - _coord_: Input parameter specifying the coordinate system for which to query the position.
    - _pos_: Output parameter, a container for the returned position data (length 7).
  - Returns: An integer status code.
```

----------------------------------------

TITLE: Get Robot External Axis Position
DESCRIPTION: Retrieves the current position of the robot's external axes. Requires a valid socket file descriptor and a pointer to a double array for the position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_current_extra_position_c(SOCKETFD _socketFd_, double * _pos_)
  - Gets the current position of the robot's external axes.
  - Parameters:
    - _socketFd_: The socket file descriptor for communication.
    - _pos_: Pointer to a double array of length 5 to store the position data.
  - Returns: An integer status code.
```

----------------------------------------

TITLE: Conveyor Motion Control
DESCRIPTION: Functions to control the motion of a conveyor belt. These functions allow for starting, stopping, and setting conveyor positions. Some functions include robot integration for synchronized movements.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: c++
CODE:
```
Result queue_motion_push_back_conveyor_on(SOCKETFD socketFd, int id, int postype, std::vector<double> pos, int vel, int acc);
Result queue_motion_push_back_conveyor_on_robot(SOCKETFD socketFd, int robotNum, int id, int postype, std::vector<double> pos, int vel, int acc);
Result queue_motion_push_back_conveyor_off(SOCKETFD socketFd, int id);
Result queue_motion_push_back_conveyor_off_robot(SOCKETFD socketFd, int robotNum, int id);
```

----------------------------------------

TITLE: HMI Library Track Record Functions
DESCRIPTION: Manages the recording and playback of robot track data. This includes starting, stopping, saving, deleting, and playing back recorded tracks, with specific functions for robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_t

LANGUAGE: APIDOC
CODE:
```
track_record_delete(): Deletes a track record. Defined in nrc_track.h.
track_record_delete_robot(): Deletes a robot-specific track record. Defined in nrc_track.h.
track_record_playback(): Plays back a track record. Defined in nrc_track.h.
track_record_playback_robot(): Plays back a robot-specific track record. Defined in nrc_track.h.
track_record_save(): Saves a track record. Defined in nrc_track.h.
track_record_save_robot(): Saves a robot-specific track record. Defined in nrc_track.h.
track_record_start(): Starts recording a track. Defined in nrc_track.h.
track_record_start_robot(): Starts recording a robot-specific track. Defined in nrc_track.h.
track_record_stop(): Stops recording a track. Defined in nrc_track.h.
track_record_stop_robot(): Stops recording a robot-specific track. Defined in nrc_track.h.
```

----------------------------------------

TITLE: Add TOFFSETON Command for Robot
DESCRIPTION: Adds a TOFFSETON (trajectory offset start) command to the motion queue for a specific robot. This function requires a socket file descriptor, the robot number, and command parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
queue_motion_push_back_TOFFSETON_robot(SOCKETFD socketFd, int robotNum, OffsetCommandParam params)
```

----------------------------------------

TITLE: Queue Motion Push Back IMove Robot
DESCRIPTION: Pushes back an 'immediate move' motion command to the queue, specifically for robot operations. This function facilitates direct point-to-point movements for robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_q

LANGUAGE: c++
CODE:
```
queue_motion_push_back_imove_rbobt()
```

----------------------------------------

TITLE: Safe IO Functions
DESCRIPTION: Handles the configuration of safety IO settings for robots. These functions allow setting and retrieving the SafeIO parameters, which are crucial for ensuring safe operation.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
set_safe_IO_function(SOCKETFD socketFd, int robotNum, SafeIO safeIO)
  - Sets the IO safety settings for a given robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - safeIO: A SafeIO structure containing the safety configuration parameters.
  - Returns: Result (likely indicating success or failure).

get_safe_IO_function(SOCKETFD socketFd, int robotNum, SafeIO &safeIO)
  - Retrieves the IO safety settings for a given robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - safeIO: Reference to a SafeIO structure to store the retrieved safety configuration.
  - Returns: Result (likely indicating success or failure).
```

----------------------------------------

TITLE: Get Static Search Position for Robot
DESCRIPTION: Retrieves the coordinates for static search positioning for a specific robot. This function requires robot number, file ID, table ID, and a delay time.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_static_search_position_robot(SOCKETFD _socketFd_, int _robotNum_, int _fileid_, int _tableid_, int _delaytime_, std::vector< double > & _pos_)
  - Retrieves coordinates for static search positioning for a specific robot.
  - Parameters:
    - _socketFd_: File descriptor for the socket connection.
    - _robotNum_: The robot number.
    - _fileid_: The file number for search.
    - _tableid_: The table number for search parameters.
    - _delaytime_: The delay time for the parameter table.
    - _pos_: A reference to a std::vector<double> to store the position coordinates.
```

LANGUAGE: C++
CODE:
```
Result get_static_search_position_robot(SOCKETFD _socketFd_, int _robotNum_, int _fileid_, int _tableid_, int _delaytime_, std::vector< double > & _pos_)
```

----------------------------------------

TITLE: Weld Control and Setting Functions
DESCRIPTION: Provides functions to set various welding control parameters and states. This includes setting general configuration, enabling/disabling welding, controlling feed wire, performing hand spot welding, rewinding wire, and supplying gas. Robot-specific versions are also available for each function.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_w

LANGUAGE: APIDOC
CODE:
```
weld_set_config(config)
  - Sets the general welding configuration.
  - Parameters:
    - config: The welding configuration to set.
  - Returns: Status of the operation.

weld_set_config_robot(config)
  - Sets the robot-specific welding configuration.
  - Parameters:
    - config: The robot-specific welding configuration to set.
  - Returns: Status of the operation.

weld_set_enable(enable)
  - Enables or disables the welding process.
  - Parameters:
    - enable: Boolean indicating whether to enable (true) or disable (false).
  - Returns: Status of the operation.

weld_set_enable_robot(enable)
  - Enables or disables the robot-specific welding process.
  - Parameters:
    - enable: Boolean indicating whether to enable (true) or disable (false).
  - Returns: Status of the operation.

weld_set_feed_wire(feed_wire_params)
  - Sets the feed wire parameters.
  - Parameters:
    - feed_wire_params: Parameters for the feed wire.
  - Returns: Status of the operation.

weld_set_feed_wire_robot(feed_wire_params)
  - Sets the robot-specific feed wire parameters.
  - Parameters:
    - feed_wire_params: Robot-specific parameters for the feed wire.
  - Returns: Status of the operation.

weld_set_hand_spot(spot_params)
  - Sets parameters for hand spot welding.
  - Parameters:
    - spot_params: Parameters for hand spot welding.
  - Returns: Status of the operation.

weld_set_hand_spot_robot(spot_params)
  - Sets robot-specific parameters for hand spot welding.
  - Parameters:
    - spot_params: Robot-specific parameters for hand spot welding.
  - Returns: Status of the operation.

weld_set_rewind_wire(rewind_params)
  - Sets parameters for rewinding the wire.
  - Parameters:
    - rewind_params: Parameters for wire rewinding.
  - Returns: Status of the operation.

weld_set_rewind_wire_robot(rewind_params)
  - Sets robot-specific parameters for rewinding the wire.
  - Parameters:
    - rewind_params: Robot-specific parameters for wire rewinding.
  - Returns: Status of the operation.

weld_set_supply_gas(gas_params)
  - Sets parameters for supplying gas.
  - Parameters:
    - gas_params: Parameters for gas supply.
  - Returns: Status of the operation.

weld_set_supply_gas_robot(gas_params)
  - Sets robot-specific parameters for supplying gas.
  - Parameters:
    - gas_params: Robot-specific parameters for gas supply.
  - Returns: Status of the operation.
```

----------------------------------------

TITLE: Robot Type Management
DESCRIPTION: Functions to get and set the robot type. These functions are used to identify and configure specific robot models within the system. They require a socket file descriptor and a robot number.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_robot_type_robot(SOCKETFD socketFd, int robotNum, int &type)
  - Retrieves the type of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number identifying the robot.
    - type: An output parameter to store the retrieved robot type.
```

LANGUAGE: APIDOC
CODE:
```
set_teach_type(SOCKETFD socketFd, int type)
  - Sets the teach mode type for the robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - type: The type of teach mode to set.
```

LANGUAGE: APIDOC
CODE:
```
set_teach_type_robot(SOCKETFD socketFd, int robotNum, int type)
  - Sets the teach mode type for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number identifying the robot.
    - type: The type of teach mode to set.
```

LANGUAGE: APIDOC
CODE:
```
get_teach_type(SOCKETFD socketFd, int &type)
  - Retrieves the current teach mode type.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - type: An output parameter to store the retrieved teach mode type.
  - Description: 获取示教模式下是何种类型
```

LANGUAGE: APIDOC
CODE:
```
get_teach_type_robot(SOCKETFD socketFd, int robotNum, int &type)
  - Retrieves the teach mode type for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number identifying the robot.
    - type: An output parameter to store the retrieved teach mode type.
```

----------------------------------------

TITLE: Tool Calibration Functions
DESCRIPTION: Provides functions for calibrating tools with a specified number of points. Includes functions for clearing calibration points and calculating calibration data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
tool_hand_2_or_20_point_calibrate_clear(SOCKETFD socketFd, int pointNum)
  - Clears calibration points for a tool.
  - Parameters:
    - socketFd: The socket file descriptor.
    - pointNum: The number of points to clear.
  - Returns: Result status.

tool_hand_2_or_20_point_calibrate_clear_robot(SOCKETFD socketFd, int robotNum, int pointNum)
  - Clears calibration points for a tool on a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - pointNum: The number of points to clear.
  - Returns: Result status.

tool_hand_7_point_calibrate(SOCKETFD socketFd, int point, int toolNum)
  - Calibrates a tool with 7 points.
  - Parameters:
    - socketFd: The socket file descriptor.
    - point: The calibration point number.
    - toolNum: The tool number.
  - Returns: Result status.

tool_hand_7_point_calibrate_robot(SOCKETFD socketFd, int robotNum, int point, int toolNum)
  - Calibrates a tool with 7 points on a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - point: The calibration point number.
    - toolNum: The tool number.
  - Returns: Result status.

tool_hand_7_point_calibrate_caculate(SOCKETFD socketFd, int toolNum, int calibrationPointNum = 7)
  - Calculates calibration data for a tool with 7 points.
  - Parameters:
    - socketFd: The socket file descriptor.
    - toolNum: The tool number.
    - calibrationPointNum: The number of calibration points (defaults to 7).
  - Returns: Result status.

tool_hand_7_point_calibrate_caculate_robot(SOCKETFD socketFd, int robotNum, int toolNum, int calibrationPointNum = 7)
  - Calculates calibration data for a tool with 7 points on a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - toolNum: The tool number.
    - calibrationPointNum: The number of calibration points (defaults to 7).
  - Returns: Result status.

tool_hand_7_point_calibrate_clear(SOCKETFD socketFd, int pointNum, int toolNum)
  - Clears calibration points for a tool.
  - Parameters:
    - socketFd: The socket file descriptor.
    - pointNum: The number of points to clear.
    - toolNum: The tool number.
  - Returns: Result status.

tool_hand_7_point_calibrate_clear_robot(SOCKETFD socketFd, int robotNum, int pointNum, int toolNum)
  - Clears calibration points for a tool on a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - pointNum: The number of points to clear.
    - toolNum: The tool number.
  - Returns: Result status.
```

----------------------------------------

TITLE: Download Job by Directory
DESCRIPTION: Downloads job data from a specified directory. This function requires a socket file descriptor, the path to the directory, and a flag to indicate if existing data should be overwritten.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_download_by_directory(SOCKETFD socketFd, const std::string& directoryPath, bool isCover);
```

----------------------------------------

TITLE: Get Current Coordinate
DESCRIPTION: Retrieves the current coordinate of a specified robot. Requires a socket file descriptor and the robot number. The coordinate is returned via a reference parameter. Returns a Result status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
Result get_current_coord_robot(SOCKETFD socketFd, int robotNum, int& coord);
```

----------------------------------------

TITLE: Get Static Search Position
DESCRIPTION: Obtains the static search position based on file ID, table ID, and a delay time, using the provided socket file descriptor. The position is returned as a vector of doubles.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_static_search_position(SOCKETFD socketFd, int fileid, int tableid, int delaytime, std::vector<double>& pos);
```

----------------------------------------

TITLE: Analog Input/Output Functions
DESCRIPTION: Functions for accessing analog input and output values. References to specific header files are provided.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_analog_input()
  - Retrieves the value of an analog input.
  - Defined in: nrc_io.h

get_analog_output()
  - Retrieves the value of an analog output.
  - Defined in: nrc_io.h
```

----------------------------------------

TITLE: End Motion Until Robot
DESCRIPTION: Signals the end of a robot-specific motion sequence that was previously started with an 'until' condition. This function is part of the HMI library's queue operation module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_end_until_robot(SOCKETFD socketFd, int robotNum);
```

----------------------------------------

TITLE: End Motion While Robot
DESCRIPTION: Signals the end of a robot-specific motion sequence that was previously started with a 'while' condition. This function is part of the HMI library's queue operation module.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result queue_motion_push_back_end_while_robot(SOCKETFD socketFd, int robotNum);
```

----------------------------------------

TITLE: Set Hard Enable Port
DESCRIPTION: Configures the hard enable ports for a system. It requires a socket file descriptor and values for two ports.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result set_hard_enable_port(SOCKETFD socketFd, int enable, int port1, int port2);
```

----------------------------------------

TITLE: Track Recording and Playback API
DESCRIPTION: Provides functions for managing robot track recordings, including starting, stopping, saving, deleting, and playback operations. Some functions are specific to individual robots, while others apply to general track management.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h_source

LANGUAGE: APIDOC
CODE:
```
track_record_save_robot(SOCKETFD socketFd, int robotNum, std::string trajName)
  - Saves the track record for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The number of the robot.
    - trajName: The name of the trajectory to save.
  - Returns: Result status.

track_record_playback(SOCKETFD socketFd, int vel)
  - Plays back a track record with a specified velocity.
  - Parameters:
    - socketFd: The socket file descriptor.
    - vel: The playback velocity.
  - Returns: Result status.

track_record_playback_robot(SOCKETFD socketFd, int robotNum, int vel)
  - Plays back a track record for a specific robot with a specified velocity.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The number of the robot.
    - vel: The playback velocity.
  - Returns: Result status.

track_record_delete(SOCKETFD socketFd)
  - Deletes the track record.
  - Parameters:
    - socketFd: The socket file descriptor.
  - Returns: Result status.

track_record_delete_robot(SOCKETFD socketFd, int robotNum)
  - Deletes the track record for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The number of the robot.
  - Returns: Result status.

track_record_stop(SOCKETFD socketFd)
  - Stops the track recording.
  - Parameters:
    - socketFd: The socket file descriptor.
  - Returns: Result status.

track_record_save(SOCKETFD socketFd, std::string trajName)
  - Saves the track record with a specified trajectory name.
  - Parameters:
    - socketFd: The socket file descriptor.
    - trajName: The name of the trajectory.
  - Returns: Result status.

track_record_start(SOCKETFD socketFd, double maxSamplingNum, double samplingInterval)
  - Starts the track recording with specified sampling parameters.
  - Parameters:
    - socketFd: The socket file descriptor.
    - maxSamplingNum: The maximum number of samples.
    - samplingInterval: The interval between samples.
  - Returns: Result status.

get_track_record_status_robot(SOCKETFD socketFd, int robotNum, bool &recordStart)
  - Retrieves the recording status for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The number of the robot.
    - recordStart: Output parameter to indicate if recording has started.
  - Returns: Result status.
```

----------------------------------------

TITLE: Job Step Functions
DESCRIPTION: Provides functions to execute a single step within a job, with and without robot integration. This is useful for debugging or executing jobs incrementally.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_j

LANGUAGE: APIDOC
CODE:
```
job_step()
  - Executes a single step of the current job.
  - Related to: job_step_robot()

job_step_robot()
  - Executes a single step of the current job with robot integration.
  - Related to: job_step()

```

----------------------------------------

TITLE: Get Controller ID for Specific Robot (C#)
DESCRIPTION: Retrieves the controller ID for a specific robot, identified by its number, using the provided socket file descriptor. This is useful when managing multiple robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_controller_id_csharp_robot(SOCKETFD socketFd, int robotNum, std::vector<char>& id);
```

----------------------------------------

TITLE: nrc_craft_weld.h File Reference
DESCRIPTION: This section provides details about the nrc_craft_weld.h file, including its source code link and included header files. It is part of the cpp_interface directory within the net_lib project.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__weld_8h

LANGUAGE: c++
CODE:
```
#include "parameter/nrc_define.h"
#include "parameter/nrc_craft_weld_parameter.h"

[Go to the source code of this file.](https://doc.hmilib.inexbot.coision.cn/nrc__craft__weld_8h_source.html)
```

----------------------------------------

TITLE: LaserCuttingIOParam Members
DESCRIPTION: This section details the members of the LaserCuttingIOParam structure. It includes individual parameters such as 'io', 'laser_fault', 'pressure_fault', 'regulator_fault', and 'water_cooler_fault', along with links to their definitions and the parent structure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_i_o_param-members

LANGUAGE: APIDOC
CODE:
```
LaserCuttingIOParam:
  io
    Description: Input/Output status.
    Type: Unknown (likely boolean or integer)

  laser_fault
    Description: Indicates a fault condition related to the laser.
    Type: Unknown (likely boolean or integer)

  pressure_fault
    Description: Indicates a fault condition related to pressure.
    Type: Unknown (likely boolean or integer)

  regulator_fault
    Description: Indicates a fault condition related to the regulator.
    Type: Unknown (likely boolean or integer)

  water_cooler_fault
    Description: Indicates a fault condition related to the water cooler.
    Type: Unknown (likely boolean or integer)
```

----------------------------------------

TITLE: Remote Control Parameters
DESCRIPTION: Parameters related to the remote control functionality.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_p

LANGUAGE: APIDOC
CODE:
```
RemoteControl:
  program: Specifies the program to be executed via remote control.
```

----------------------------------------

TITLE: Get Current Line Number for Robot
DESCRIPTION: Retrieves the current line number being executed in the opened job file for a specific robot. This function is useful for tracking robot program execution flow.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: cpp
CODE:
```
Result job_get_current_line_robot(SOCKETFD _socketFd_, int _robotNum_, int& _line_)
```

----------------------------------------

TITLE: nrc_craft_vision.h Header Inclusion
DESCRIPTION: This snippet shows the necessary header files to include for using the nrc_craft_vision functionality. It depends on `nrc_define.h` and `nrc_craft_vision_parameter.h` for definitions and parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h

LANGUAGE: c++
CODE:
```
#include "parameter/nrc_define.h"
#include "parameter/nrc_craft_vision_parameter.h"
```

----------------------------------------

TITLE: Insert Conveyor Position Instruction
DESCRIPTION: Inserts a conveyor position instruction into the job file to get the conveyor tracking position. Requires a socket file descriptor, line number, job ID, and position name.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: c++
CODE:
```
int job_insert_conveyor_pos(SOCKETFD socketFd, int line, int id, const std::string posName)
```

----------------------------------------

TITLE: Weld Get Feed Wire Status
DESCRIPTION: Retrieves the status of the wire feeding mechanism, including states for feeding, rewinding, gas supply, and welding enable. It also provides the status for manual spot welding.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__weld_8h_source

LANGUAGE: APIDOC
CODE:
```
weld_get_feed_wire_status(SOCKETFD socketFd, std::vector< int > &status)
  - Gets the status of feed wire, rewind wire, supply gas, weld enable, and hand spot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - status: A reference to a vector of integers to store the retrieved statuses.
```

LANGUAGE: APIDOC
CODE:
```
weld_get_feed_wire_status_robot(SOCKETFD socketFd, int robotNum, std::vector< int > &status)
  - Gets the status of feed wire, rewind wire, supply gas, weld enable, and hand spot for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - status: A reference to a vector of integers to store the retrieved statuses.
```

----------------------------------------

TITLE: Joint Movement (robot_movej)
DESCRIPTION: Executes a joint space movement. Requires socket file descriptor and a MoveCmd structure containing target position, velocity, coordinate system, and acceleration.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
robot_movej(SOCKETFD _socketFd_, MoveCmd _moveCmd_)
  Parameters:
    _socketFd_: File descriptor for the socket connection.
    _moveCmd_: Structure containing movement parameters:
      targetPosValue: Position vector (n elements for n axes).
      vel: Velocity (0 < vel <= 100).
      coord: Coordinate system (0 <= coord <= 3).
      acc: Acceleration (>= 0).
  Description: Performs a joint space movement.
```

----------------------------------------

TITLE: Digital Input/Output Error Message Management
DESCRIPTION: Functions to get and set error messages for digital inputs and outputs. These functions interact with a socket file descriptor and a vector of AlarmdIO objects to manage error states.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: c++
CODE:
```
Result get_error_msg_of_digital_input(SOCKETFD socketFd, std::vector<AlarmdIO>& msg);
Result set_error_msg_of_digital_output(SOCKETFD socketFd, std::vector<AlarmdIO> msg);
Result get_error_msg_of_digital_output(SOCKETFD socketFd, std::vector<AlarmdIO>& msg);
```

----------------------------------------

TITLE: RemoteProgramSetting Structure
DESCRIPTION: Defines the structure for remote program settings, specifying the job to be run and the number of times it should be executed.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h_source

LANGUAGE: cpp
CODE:
```
struct RemoteProgramSetting {
    std::string job;
    int times;
};
```

----------------------------------------

TITLE: Get Current Motor Payload
DESCRIPTION: Retrieves the current motor payload for the robot and external axes. It takes a socket file descriptor and output references for motor payload values. The payload is represented as a percentage.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_payload(SOCKETFD _socketFd_, std::vector< double > & _motorPayload_, std::vector< double > & _motorPayloadSync_)
  Parameters:
    _socketFd_: The socket file descriptor for communication.
    _motorPayload_: Output parameter for robot motor payload values (length 7) in percentage.
    _motorPayloadSync_: Output parameter for external axis motor payload values (length 5) in percentage.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_curretn_motor_payload(SOCKETFD _socketFd_, std::vector< double > & _motorPayload_, std::vector< double > & _motorPayloadSync_);
```

----------------------------------------

TITLE: Calibration Class Functions
DESCRIPTION: Provides functions related to calibration, including adding calibration points and retrieving points.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_func

LANGUAGE: APIDOC
CODE:
```
Calibration:
  addCalibrationPoint()
    - Adds a calibration point.
    - Belongs to the Calibration class.

  Calibration()
    - Constructor for the Calibration class.

  getPoint()
    - Retrieves a calibration point.
    - Belongs to the Calibration class.
```

----------------------------------------

TITLE: Connect to Robot Controller
DESCRIPTION: Establishes a connection to the robot controller via IP address and port. This is a synchronous operation and will block until the connection result is returned.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h

LANGUAGE: APIDOC
CODE:
```
connect_robot_c(const char * _ip_, const char * _port_)
  - Connects to the controller.
  - Parameters:
    - _ip_: The IP address of the controller (e.g., "************").
    - _port_: The port number of the controller (e.g., "6001").
  - Returns: -1 on failure. The function is synchronous and blocks until connection is established.
```

----------------------------------------

TITLE: JavaScript Search Functionality
DESCRIPTION: Demonstrates JavaScript functions used for search and UI control within the documentation, such as closing search results.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_x

LANGUAGE: javascript
CODE:
```
javascript:searchBox.CloseResultsWindow()
```

----------------------------------------

TITLE: Track Recording Control
DESCRIPTION: Functions to control the track recording process. This includes starting, stopping, and saving recorded tracks. Some functions are specific to individual robots.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h

LANGUAGE: APIDOC
CODE:
```
track_record_start(SOCKETFD socketFd, double maxSamplingNum, double samplingInterval)
  - Starts track recording.
  - Parameters:
    - socketFd: The socket file descriptor.
    - maxSamplingNum: Maximum number of samples.
    - samplingInterval: Interval between samples.

track_record_start_robot(SOCKETFD socketFd, int robotNum, double maxSamplingNum, double samplingInterval)
  - Starts track recording for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - maxSamplingNum: Maximum number of samples.
    - samplingInterval: Interval between samples.

track_record_stop(SOCKETFD socketFd)
  - Stops track recording.
  - Parameters:
    - socketFd: The socket file descriptor.

track_record_stop_robot(SOCKETFD socketFd, int robotNum)
  - Stops track recording for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.

track_record_save(SOCKETFD socketFd, std::string trajName)
  - Saves the recorded track.
  - Parameters:
    - socketFd: The socket file descriptor.
    - trajName: The name of the trajectory to save.

track_record_save_robot(SOCKETFD socketFd, int robotNum, std::string trajName)
  - Saves the recorded track for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - trajName: The name of the trajectory to save.
```

----------------------------------------

TITLE: Calibration Struct Documentation
DESCRIPTION: This section details the Calibration struct, its constructor, member functions, and public attributes. It includes information on adding calibration points, retrieving points by index, and the calibrated status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_calibration

LANGUAGE: APIDOC
CODE:
```
Calibration Struct Reference
`#include <nrc_craft_vision_parameter.h>`

## Public Member Functions

* void addCalibrationPoint(const CalibrationPoint &pos)
  * Adds a calibration point to the struct.
  * Parameters:
    * pos: A const reference to a CalibrationPoint object.

* CalibrationPoint getPoint(int index) const
  * Retrieves a calibration point by its index.
  * Parameters:
    * index: The index of the calibration point to retrieve.
  * Returns: A CalibrationPoint object.

## Public Attributes

* bool calibrated
  * A boolean flag indicating if the calibration is complete.

* std::vector<CalibrationPoint> point
  * A vector storing CalibrationPoint objects.

* int point_num
  * The number of calibration points.

## Constructor & Destructor Documentation

* Calibration()
  * Constructor for the Calibration struct.
  * Parameters:
    * _num_points_: The initial number of points (defaults to 6).

## Member Function Documentation

* addCalibrationPoint()
  * void Calibration::addCalibrationPoint(const CalibrationPoint & _pos_)
    * Adds a calibration point.

* getPoint()
  * CalibrationPoint Calibration::getPoint(int _index_)
    * Retrieves a calibration point by index.

## Member Data Documentation

* calibrated
  * bool Calibration::calibrated
    * Indicates if calibration is done.

* point
  * std::vector<CalibrationPoint> Calibration::point
    * Stores calibration points.

* point_num
  * int Calibration::point_num
    * Number of calibration points.

* * *
The documentation for this struct was generated from the following file:
  * parameter/[nrc_craft_vision_parameter.h]
```

----------------------------------------

TITLE: Get Controller ID (C# style)
DESCRIPTION: Retrieves the serial number ID of the controller using a C# style vector of characters. Requires a socket file descriptor and a reference to a vector of characters to store the ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_controller_id_csharp(SOCKETFD _socketFd_, std::vector< char > & _id_)
  Description: Retrieves the controller serial number ID (C# style).
  Parameters:
    _socketFd_: File descriptor for the socket connection.
    _id_: Reference to a vector of characters to store the controller serial number ID.
```

----------------------------------------

TITLE: RemoteProgram Structure Fields
DESCRIPTION: Defines the members of the RemoteProgram structure, used to store individual program settings. Includes port and value information for each program step.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h_source

LANGUAGE: APIDOC
CODE:
```
RemoteProgram:
  port: int
    Description: Port number for the program step.
    Definition: nrc_io_parameter.h:16
  value: int
    Description: Value associated with the program step.
    Definition: nrc_io_parameter.h:17
```

----------------------------------------

TITLE: Get Origin Coordinate to Target Coordinate (Robot)
DESCRIPTION: Transforms a position from an origin coordinate system to a target coordinate system for a specific robot. Requires a socket file descriptor, robot number, origin/target coordinate identifiers, origin position, and a target position vector.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_origin_coord_to_target_coord_robot(SOCKETFD socketFd, int robotNum, int originCoord, std::vector<double> originPos, int targetCoord, std::vector<double>& targetPos)
  - Transforms a position from an origin coordinate system to a target coordinate system for a specific robot.
  - Parameters:
    - socketFd: The file descriptor for the socket connection.
    - robotNum: The identifier for the robot.
    - originCoord: The identifier for the origin coordinate system.
    - originPos: A vector representing the position in the origin coordinate system.
    - targetCoord: The identifier for the target coordinate system.
    - targetPos: A reference to a vector where the transformed position will be stored.
```

----------------------------------------

TITLE: CollisionPara Structure Documentation
DESCRIPTION: Details the members of the CollisionPara structure, which holds parameters related to collision detection and robot configuration. Includes error enable time, axis count, collision detection thresholds for run and teach modes, and position delay time.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface__parameter_8h_source

LANGUAGE: APIDOC
CODE:
```
CollisionPara:
  error_enable_time_ms_value: double
    误差允许时间，参数范围：0≤error_enable_time_ms_value≤99
  axisum: unsigned int
    机器人轴数，默认为六轴机器人
  collisionDetection_run: std::vector< double >
    数组，碰撞检测阈值（指令），第几位为第几轴的碰撞检测阈值，参数范围：1≤vector_collisionDetection_run≤10000
  position_delay_time_ms_value: double
    //指令位置响应时间，参数范围：0<position_delay_time_ms_value≤99
  collisionDetection_teach: std::vector< double >
    数组，碰撞检测阈值（点动），第几位为第几轴的碰撞检测阈值，参数范围：1≤vector_collisionDetection_teach≤10000
```

----------------------------------------

TITLE: Modbus Read Coil Status
DESCRIPTION: Reads the status of coils from a Modbus device. Requires a socket file descriptor, device ID, starting address, quantity of coils to read, and a vector to store the data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h

LANGUAGE: c++
CODE:
```
int modbus_read_coil_status(SOCKETFD socketFd, int id, int address, int quantity, std::vector< int > &data)
// Function code 01H
```

LANGUAGE: APIDOC
CODE:
```
modbus_read_coil_status(SOCKETFD socketFd, int id, int address, int quantity, std::vector< int > &data)
  - Reads the status of coils.
  - Function code: 01H
  - Parameters:
    - socketFd: The socket file descriptor.
    - id: The device ID.
    - address: The starting address of the coils.
    - quantity: The number of coils to read.
    - data: A reference to a vector of integers to store the coil states (0 or 1).
```

----------------------------------------

TITLE: Job Configuration and Upload Functions
DESCRIPTION: Includes functions for setting local positions within a job and uploading job files or directories. These are essential for configuring job parameters and transferring job data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_j

LANGUAGE: APIDOC
CODE:
```
job_set_local_position()
  - Sets the local position for a job.
  - Related to: job_set_local_position_robot()

job_set_local_position_robot()
  - Sets the local position for a job with robot integration.
  - Related to: job_set_local_position()

job_sync_job_file()
  - Synchronizes a job file to the system.

job_upload_by_directory()
  - Uploads a job by specifying a directory.

job_upload_by_file()
  - Uploads a job by specifying a file.

```

----------------------------------------

TITLE: Get Static Search Position for Specific Robot
DESCRIPTION: Retrieves the static search position for a specific robot, identified by its number, along with file ID, table ID, and delay time. The position is returned as a vector of doubles.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result get_static_search_position_robot(SOCKETFD socketFd, int robotNum, int fileid, int tableid, int delaytime, std::vector<double>& pos);
```

----------------------------------------

TITLE: Get Global Position for Robot
DESCRIPTION: Retrieves the global position (GP) data for a specific robot. This function requires the robot number, a valid SOCKETFD, and a position name. It returns the position data in a double vector.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_global_position_robot(SOCKETFD _socketFd_, int _robotNum_, std::string _posName_, std::vector< double > & _pos_)
  - Retrieves global position (GP) data for a specific robot.
  - Parameters:
    - _socketFd_: File descriptor for socket communication.
    - _robotNum_: The number of the robot.
    - _posName_: Name of the global position (e.g., "GP0001").
    - _pos_: Output vector containing position data.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_global_position_robot(SOCKETFD _socketFd_, int _robotNum_, std::string _posName_, std::vector< double > & _pos_);
```

----------------------------------------

TITLE: Laser Cutting IO Parameter Management
DESCRIPTION: Functions to get and set Input/Output (IO) parameters for laser cutting. These functions interact with a specified socket file descriptor and can be used for general IO parameter retrieval or when a robot number is involved. The `laser_cutting_get_io_parameter` function retrieves IO parameters, while `laser_cutting_get_io_parameter_robot` is used when a specific robot number is associated. The return status indicates success (0) or failure (-1, -2).

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h

LANGUAGE: APIDOC
CODE:
```
laser_cutting_get_io_parameter(SOCKETFD _socketFd, LaserCuttingIOParam & _param)
  Retrieves IO parameters for laser cutting.
  Parameters:
    _socketFd: The socket file descriptor for communication.
    _param: A reference to a LaserCuttingIOParam structure to store the retrieved parameters.
  Returns:
    Status code: 0 for success, -1 for message retrieval failure, -2 for client not found.

laser_cutting_get_io_parameter_robot(SOCKETFD _socketFd, int _robotNum, LaserCuttingIOParam & _param)
  Retrieves IO parameters for laser cutting associated with a specific robot.
  Parameters:
    _socketFd: The socket file descriptor for communication.
    _robotNum: The number of the robot.
    _param: A reference to a LaserCuttingIOParam structure to store the retrieved parameters.
  Returns:
    Status code: 0 for success, -1 for message retrieval failure, -2 for client not found.
```

LANGUAGE: C
CODE:
```
EXPORT_API Result laser_cutting_get_io_parameter(SOCKETFD _socketFd, LaserCuttingIOParam & _param);
EXPORT_API Result laser_cutting_get_io_parameter_robot(SOCKETFD _socketFd, int _robotNum, LaserCuttingIOParam & _param);
```

----------------------------------------

TITLE: Comparison Define Values
DESCRIPTION: Defines for comparison operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
GREATER
  Represents a greater than comparison.
  Source: nrc_define.h

GREATER_EQUAL
  Represents a greater than or equal to comparison.
  Source: nrc_define.h
```

----------------------------------------

TITLE: Get Current Motor Speed
DESCRIPTION: Retrieves the current motor speed. This function requires a valid socket file descriptor and output vectors to store the speed data. It is used for general motor speed retrieval.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_speed(SOCKETFD socketFd, std::vector<int>& motorSpeed, std::vector<int>& motorSpeedSync)
  - Retrieves current motor speed.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - motorSpeed: Output vector to store motor speed values.
    - motorSpeedSync: Output vector to store synchronized motor speed values.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Robot Mode Retrieval Functions
DESCRIPTION: These functions are used to get the current operational mode of the robot. They require a socket file descriptor and output the mode into an integer reference. The `get_current_mode` function provides a description of the possible mode values.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_current_mode(_socketFd, _mode)
  Retrieves the current operational mode of the robot.
  Parameters:
    _socketFd: SOCKETFD - File descriptor for the socket connection.
    _mode: int & - Output parameter to store the current mode (0: Teach, 1: Remote, 2: Run).

get_current_mode_robot(_socketFd, _robotNum, _mode)
  Retrieves the current operational mode of a specific robot.
  Parameters:
    _socketFd: SOCKETFD - File descriptor for the socket connection.
    _robotNum: int - The number of the robot.
    _mode: int & - Output parameter to store the current mode.
```

----------------------------------------

TITLE: queue_motion_push_back_dout_c Function Documentation
DESCRIPTION: Detailed documentation for the `queue_motion_push_back_dout_c` function. This function adds a DOUT instruction to the local queue for queue motion mode. It requires a socket file descriptor, a port number, and a boolean value indicating the state.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__queue__operate_8h

LANGUAGE: APIDOC
CODE:
```
queue_motion_push_back_dout_c(SOCKETFD socketFd, int port, bool value)
  - Adds a DOUT instruction to the local queue for queue motion mode.
  - Parameters:
    - socketFd: SOCKET file descriptor.
    - port: The port number.
    - value: The boolean value for the DOUT instruction.
```

----------------------------------------

TITLE: Add TOFFSETON Command to Queue
DESCRIPTION: Appends a TOFFSETON (trajectory offset start) command to the local motion queue. This function is part of the queue operation set for motion control.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: APIDOC
CODE:
```
queue_motion_push_back_TOFFSETON(SOCKETFD _socketFd_, OffsetCommandParam _params_)
  - Appends a TOFFSETON command to the local motion queue.
  - Parameters:
    - _socketFd_: SOCKETFD, the socket file descriptor.
    - _params_: OffsetCommandParam, parameters for the offset command.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result queue_motion_push_back_TOFFSETON(SOCKETFD _socketFd_, OffsetCommandParam _params_);
```

----------------------------------------

TITLE: Get Current Motor Payload
DESCRIPTION: Retrieves the current motor payload. This function requires a valid socket file descriptor and output vectors to store the payload data. It is used for general motor payload retrieval.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_payload(SOCKETFD socketFd, std::vector<double>& motorPayload, std::vector<double>& motorPayloadSync)
  - Retrieves current motor payload.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - motorPayload: Output vector to store motor payload values.
    - motorPayloadSync: Output vector to store synchronized motor payload values.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Controller ID Retrieval
DESCRIPTION: Retrieves the serial number ID of the controller. This function requires a socket file descriptor and a character pointer to store the ID. It is used to get the serial number ID of the current controller.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_controller_id(SOCKETFD socketFd, char *id)
  - Retrieves the serial number ID of the controller.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - id: A character pointer to store the controller ID.
  - Returns: Result of the operation.
```

LANGUAGE: APIDOC
CODE:
```
get_controller_id_robot(SOCKETFD socketFd, int robotNum, char *id)
  - Retrieves the serial number ID of the controller for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - id: A character pointer to store the controller ID.
  - Returns: Result of the operation.
```

----------------------------------------

TITLE: LaserCuttingEquipment Members
DESCRIPTION: This section lists the members of the LaserCuttingEquipment structure. Each member is described with its name, the structure it belongs to, and a link to its detailed documentation. This includes properties related to arrival light mode, collision distance, aspiration modes, focus compensation, follow behavior, retreat distance, and wait times.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_laser_cutting_equipment-members

LANGUAGE: APIDOC
CODE:
```
LaserCuttingEquipment:
  arrivalOutLightMode
    Description: Controls the mode for the output light upon arrival.
    Belongs to: LaserCuttingEquipment

  collisionDistance
    Description: Defines the distance threshold for collision detection.
    Belongs to: LaserCuttingEquipment

  delAspiratedMode
    Description: Specifies the mode for aspirator deactivation.
    Belongs to: LaserCuttingEquipment

  delAspiratedTime
    Description: Sets the time duration for aspirator deactivation.
    Belongs to: LaserCuttingEquipment

  focusCompensation
    Description: Manages the focus compensation settings.
    Belongs to: LaserCuttingEquipment

  focusCompensationConstant
    Description: Represents the constant value for focus compensation.
    Belongs to: LaserCuttingEquipment

  focusCompensationPower
    Description: Represents the power value for focus compensation.
    Belongs to: LaserCuttingEquipment

  focusCompensationTime
    Description: Represents the time value for focus compensation.
    Belongs to: LaserCuttingEquipment

  focusFormula
    Description: Specifies the formula used for focus calculation.
    Belongs to: LaserCuttingEquipment

  follow
    Description: Controls the follow behavior of the equipment.
    Belongs to: LaserCuttingEquipment

  preAspiratedTime
    Description: Sets the time duration for pre-aspiration.
    Belongs to: LaserCuttingEquipment

  rePerforate
    Description: Indicates whether re-perforation is enabled.
    Belongs to: LaserCuttingEquipment

  RetreatDistance
    Description: Defines the distance for retreating.
    Belongs to: LaserCuttingEquipment

  waitFollowTime
    Description: Sets the waiting time for follow behavior.
    Belongs to: LaserCuttingEquipment

  waitLiftUpTime
    Description: Sets the waiting time for lifting up.
    Belongs to: LaserCuttingEquipment
```

----------------------------------------

TITLE: HMILib Job Operation API
DESCRIPTION: API documentation for HMILib job operation functions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: apidoc
CODE:
```
APIDOC:
  job_get_current_file_csharp_robot(SOCKETFD socketFd, int robotNum, std::vector<char>& jobName)
    Description: Retrieves the name of the current job file for a specific robot.
    Parameters:
      - socketFd: SOCKETFD - The file descriptor for the socket connection.
      - robotNum: int - The number of the robot.
      - jobName: std::vector<char>& - Output parameter to store the job file name.
    Returns: Result - Indicates success or failure of the operation.

  job_get_current_line(SOCKETFD socketFd, int& line)
    Description: Retrieves the current line number within the job file.
    Parameters:
      - socketFd: SOCKETFD - The file descriptor for the socket connection.
      - line: int& - Output parameter to store the current line number.
    Returns: Result - Indicates success or failure of the operation.

  job_get_current_line_robot(SOCKETFD socketFd, int robotNum, int& line)
    Description: Retrieves the current line number for a specific robot within the job file.
    Parameters:
      - socketFd: SOCKETFD - The file descriptor for the socket connection.
      - robotNum: int - The number of the robot.
      - line: int& - Output parameter to store the current line number.
    Returns: Result - Indicates success or failure of the operation.

  job_insert_local_position(SOCKETFD socketFd, PositionData posData)
    Description: Inserts a local position into the job file.
    Parameters:
      - socketFd: SOCKETFD - The file descriptor for the socket connection.
      - posData: PositionData - Structure containing the position data to insert.
    Returns: Result - Indicates success or failure of the operation.

  job_insert_local_position_robot(SOCKETFD socketFd, int robotNum, PositionData posData)
    Description: Inserts a local position for a specific robot into the job file.
    Parameters:
      - socketFd: SOCKETFD - The file descriptor for the socket connection.
      - robotNum: int - The number of the robot.
      - posData: PositionData - Structure containing the position data to insert.
    Returns: Result - Indicates success or failure of the operation.

  job_set_local_position(SOCKETFD socketFd, const std::string& posName, std::vector<double> posInfo)
    Description: Sets a local position in the job file with a given name.
    Parameters:
      - socketFd: SOCKETFD - The file descriptor for the socket connection.
      - posName: const std::string& - The name of the position.
      - posInfo: std::vector<double> - Vector containing the position coordinates and other information.
    Returns: Result - Indicates success or failure of the operation.

  job_set_local_position_robot(SOCKETFD socketFd, int robotNum, const std::string& posName, std::vector<double> posInfo)
    Description: Sets a local position for a specific robot in the job file with a given name.
    Parameters:
      - socketFd: SOCKETFD - The file descriptor for the socket connection.
      - robotNum: int - The number of the robot.
      - posName: const std::string& - The name of the position.
      - posInfo: std::vector<double> - Vector containing the position coordinates and other information.
    Returns: Result - Indicates success or failure of the operation.
```

----------------------------------------

TITLE: Add ARCON Command to Robot Queue
DESCRIPTION: Adds an ARCON (welding start) command to the local motion queue for a specific robot. This function requires a socket file descriptor, the robot number, and a command ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: cpp
CODE:
```
Result queue_motion_push_back_arc_on_robot(SOCKETFD _socketFd, int _robotNum, int _id)
```

----------------------------------------

TITLE: Robot Status and Configuration
DESCRIPTION: Functions to retrieve robot status, configuration, and set parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_current_position(SOCKETFD socketFd, int coord, std::vector< double > &pos)
  - Retrieves the current position of the robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - coord: The coordinate system for the position.
    - pos: Output vector to store the position data.

get_current_extra_position_robot(SOCKETFD socketFd, int robotNum, std::vector< double > &pos)
  - Retrieves the current extra position of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - pos: Output vector to store the position data.

get_speed_robot(SOCKETFD socketFd, int robotNum, int &speed)
  - Retrieves the current speed of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - speed: Output integer to store the speed.

get_robot_configuration_robot(SOCKETFD socketFd, int robotNum, int &configuration)
  - Retrieves the configuration of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - configuration: Output integer to store the configuration.

get_curretn_motor_payload_robot(SOCKETFD socketFd, int robotNum, std::vector< double > &motorPayload, std::vector< double > &motorPayloadSync)
  - Retrieves the current motor payload and synchronized payload for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - motorPayload: Output vector to store motor payload.
    - motorPayloadSync: Output vector to store synchronized motor payload.

set_position_dragParams_robot(SOCKETFD socketFd, int robotNum, double dragInPosMaxVel, double dragInPosMaxAngleVel)
  - Sets drag parameters for a specific robot's position.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - dragInPosMaxVel: Maximum velocity limit in Cartesian space.
    - dragInPosMaxAngleVel: Maximum angular velocity limit in joint space.

set_position_dragParams(SOCKETFD socketFd, double dragInPosMaxVel, double dragInPosMaxAngleVel)
  - Sets drag parameters for position.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - dragInPosMaxVel: Maximum velocity limit in Cartesian space.
    - dragInPosMaxAngleVel: Maximum angular velocity limit in joint space.

set_robots_parallel(SOCKETFD socketFd, bool open)
  - Enables or disables multi-robot parallel mode.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - open: Boolean to enable (true) or disable (false) parallel mode.
```

----------------------------------------

TITLE: Modbus Write Multiple Coil Status
DESCRIPTION: Writes the status of multiple coils in a Modbus device. Requires a socket file descriptor, device ID, starting address, and a vector of integers representing the coil states.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h

LANGUAGE: c++
CODE:
```
int modbus_write_multiple_coil_status(SOCKETFD socketFd, int id, int address, const std::vector< int > &data)
// Function code 0FH
```

LANGUAGE: APIDOC
CODE:
```
modbus_write_multiple_coil_status(SOCKETFD socketFd, int id, int address, const std::vector< int > &data)
  - Writes the status of multiple coils.
  - Function code: 0FH
  - Parameters:
    - socketFd: The socket file descriptor.
    - id: The device ID.
    - address: The starting address of the coils.
    - data: A vector of integers representing the coil states (0 or 1).
```

----------------------------------------

TITLE: Robot Cartesian Space Movement (External Axis)
DESCRIPTION: Executes a Cartesian space movement for the robot, including external axes. Requires a socket file descriptor and a MoveCmd structure. The MoveCmd structure's 'pos' parameter is an array where the first 7 elements are robot joint positions and subsequent elements (starting from index 7) are for external axes.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
robot_extra_movel(_socketFd: SOCKETFD, _moveCmd_: MoveCmd)
  - Executes a Cartesian space movement, potentially including external axes.
  - Parameters:
    - _socketFd: File descriptor for the socket connection.
    - _moveCmd_: Structure containing movement commands.
      - pos: Array of 14 elements. First 7 for robot joints, elements from index 7 onwards for external axes. Unused elements should be 0.
      - vel: Speed, range: 1 < vel <= 9999.
      - coord: Coordinate system, range: 0 <= coord <= 3.
      - acc: Acceleration, range: 0.
```

----------------------------------------

TITLE: AlarmdIO Members
DESCRIPTION: Details members related to alarm I/O.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_m

LANGUAGE: APIDOC
CODE:
```
AlarmdIO:
  msg: The message content.
  msgType: The type of the message.
```

----------------------------------------

TITLE: Data Structures
DESCRIPTION: References to important data structures used in the API, including IOCommandParams, MoveCmd, and PositionData.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: APIDOC
CODE:
```
IOCommandParams
  - Definition: nrc_define.h:95

MoveCmd
  - Definition: nrc_define.h:36

PositionData
  - Definition: nrc_define.h:78
```

----------------------------------------

TITLE: Digital Input Error Message Management
DESCRIPTION: Functions to set and get error messages for digital input ports. These functions require a socket file descriptor and a vector of AlarmdIO objects. They are used for managing alarm states of digital inputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: cpp
CODE:
```
int set_error_msg_of_digital_input(SOCKETFD socketFd, std::vector<AlarmdIO> msg);
int get_error_msg_of_digital_input(SOCKETFD socketFd, std::vector<AlarmdIO> &msg);
```

----------------------------------------

TITLE: Vision Calibration Functions
DESCRIPTION: Provides functions for calibrating the vision system. Includes general calibration and robot-specific calibration.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_v

LANGUAGE: APIDOC
CODE:
```
vision_calibrate()
  Description: Calibrates the vision system.
  Source: nrc_craft_vision.h

vision_calibrate_robot()
  Description: Calibrates the vision system for robot operations.
  Source: nrc_craft_vision.h
```

----------------------------------------

TITLE: Conveyor Check Position Robot Job Insertion
DESCRIPTION: Inserts a conveyor check position robot job into the operation file. This function is used to insert a command for conveyor workpiece detection start for a specific robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_conveyor_check_pos_robot(_socketFd, _robotNum, _line, _id)

Parameters:
  _socketFd: SOCKETFD for communication.
  _robotNum: The robot number.
  _line: The line number.
  _id: The job ID.
```

LANGUAGE: C
CODE:
```
EXPORT_API Result job_insert_conveyor_check_pos_robot(SOCKETFD _socketFd, int _robotNum, int _line, int _id);
```

----------------------------------------

TITLE: Queue Motion Status Management
DESCRIPTION: Functions to set and get the status of the motion queue for controllers and specific robots. This includes enabling/disabling the queue mode and querying its current state.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: APIDOC
CODE:
```
queue_motion_set_status(SOCKETFD socketFd, bool status)
  - Opens or closes the controller's queue motion mode.
  - Parameters:
    - socketFd: The socket file descriptor.
    - status: A boolean indicating whether to enable (true) or disable (false) the queue motion mode.

queue_motion_set_status_robot(SOCKETFD socketFd, int robotNum, bool status)
  - Sets the queue motion status for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The identifier for the robot.
    - status: A boolean indicating whether to enable (true) or disable (false) the queue motion mode.

queue_motion_get_status(SOCKETFD socketFd, bool &status)
  - Queries whether the controller's queue motion mode is currently enabled.
  - Parameters:
    - socketFd: The socket file descriptor.
    - status: A reference to a boolean that will be updated with the current status.

queue_motion_get_status_robot(SOCKETFD socketFd, int robotNum, bool &status)
  - Retrieves the queue motion status for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The identifier for the robot.
    - status: A reference to a boolean that will be updated with the robot's current status.
```

----------------------------------------

TITLE: Motion Queue Operations
DESCRIPTION: Provides an overview of the motion queue operations available for robot and controller interactions. These functions manage the lifecycle of motion commands within the queue system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_q

LANGUAGE: APIDOC
CODE:
```
queue_motion_restart_robot()
  - Restarts the motion queue for the robot.
  - Source: nrc_queue_operate.h

queue_motion_send_to_controller()
  - Sends motion commands to the controller.
  - Source: nrc_queue_operate.h

queue_motion_send_to_controller_c()
  - Sends motion commands to the controller using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_send_to_controller_robot()
  - Sends motion commands to the robot controller.
  - Source: nrc_queue_operate.h

queue_motion_set_status()
  - Sets the status of the motion queue.
  - Source: nrc_queue_operate.h

queue_motion_set_status_c()
  - Sets the status of the motion queue using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_set_status_robot()
  - Sets the status of the motion queue for the robot.
  - Source: nrc_queue_operate.h

queue_motion_size()
  - Retrieves the current size of the motion queue.
  - Source: nrc_queue_operate.h

queue_motion_size_robot()
  - Retrieves the current size of the motion queue for the robot.
  - Source: nrc_queue_operate.h

queue_motion_stop()
  - Stops all motion commands in the queue.
  - Source: nrc_queue_operate.h

queue_motion_stop_not_power_off()
  - Stops motion commands without powering off.
  - Source: nrc_queue_operate.h

queue_motion_stop_not_power_off_c()
  - Stops motion commands without powering off, using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_stop_not_power_off_robot()
  - Stops motion commands for the robot without powering off.
  - Source: nrc_queue_operate.h

queue_motion_stop_robot()
  - Stops all motion commands for the robot.
  - Source: nrc_queue_operate.h

queue_motion_suspend()
  - Suspends motion commands in the queue.
  - Source: nrc_queue_operate.h

queue_motion_suspend_c()
  - Suspends motion commands using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_suspend_robot()
  - Suspends motion commands for the robot.
  - Source: nrc_queue_operate.h
```

----------------------------------------

TITLE: Get Controller ID for Robot (C-style)
DESCRIPTION: Retrieves the serial number ID of the controller for a specific robot using a C-style character pointer. Requires a socket file descriptor, robot number, and a character pointer for the ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_controller_id_robot(SOCKETFD _socketFd_, int _robotNum_, char * _id_)
  Parameters:
    _socketFd_: File descriptor for the socket connection.
    _robotNum_: The robot number.
    _id_: Pointer to a character array to store the controller serial number ID.
```

----------------------------------------

TITLE: Digital Input/Output Functions
DESCRIPTION: Functions for accessing digital input and output states. Includes C interface variants and error message retrieval.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_digital_input()
  - Retrieves the state of a digital input.
  - Defined in: nrc_io.h

get_digital_input_c()
  - Retrieves the state of a digital input using a C interface.
  - Defined in: nrc_c_io.h

get_digital_output()
  - Retrieves the state of a digital output.
  - Defined in: nrc_io.h

get_digital_output_c()
  - Retrieves the state of a digital output using a C interface.
  - Defined in: nrc_c_io.h

get_error_msg_of_digital_input()
  - Retrieves the error message associated with a digital input.
  - Defined in: nrc_io.h

get_error_msg_of_digital_output()
  - Retrieves the error message associated with a digital output.
  - Defined in: nrc_io.h

get_force_digital_input()
  - Retrieves the forced state of a digital input.
  - Defined in: nrc_io.h
```

----------------------------------------

TITLE: User Coordinate System Information
DESCRIPTION: Retrieves the number and parameters of the user coordinate systems.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_g

LANGUAGE: APIDOC
CODE:
```
get_user_coord_number()
  - Retrieves the number of user coordinate systems.
  - Source: nrc_interface.h

get_user_coord_number_robot()
  - Retrieves the number of user coordinate systems from the robot.
  - Source: nrc_interface.h

get_user_coord_para()
  - Retrieves the parameters for user coordinate systems.
  - Source: nrc_interface.h

get_user_coord_para_robot()
  - Retrieves the parameters for user coordinate systems from the robot.
  - Source: nrc_interface.h
```

----------------------------------------

TITLE: Modbus Read Holding Registers
DESCRIPTION: Reads data from holding registers in a Modbus device. Requires a socket file descriptor, device ID, starting address, quantity of registers to read, and a vector to store the data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h

LANGUAGE: c++
CODE:
```
int modbus_read_holding_registers(SOCKETFD socketFd, int id, int address, int quantity, std::vector< int > &data)
// Function code 03H
```

LANGUAGE: APIDOC
CODE:
```
modbus_read_holding_registers(SOCKETFD socketFd, int id, int address, int quantity, std::vector< int > &data)
  - Reads data from holding registers.
  - Function code: 03H
  - Parameters:
    - socketFd: The socket file descriptor.
    - id: The device ID.
    - address: The starting address of the holding registers.
    - quantity: The number of registers to read.
    - data: A reference to a vector of integers to store the read data.
```

----------------------------------------

TITLE: Modbus Write Holding Registers
DESCRIPTION: Writes data to holding registers in a Modbus device. Supports writing multiple registers at once. Requires a socket file descriptor, device ID, starting address, and the data to be written.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__modbus_8h

LANGUAGE: c++
CODE:
```
int modbus_write_holding_registers(SOCKETFD socketFd, int id, int address, const std::vector< int > &data)
// Function code 10H
```

LANGUAGE: APIDOC
CODE:
```
modbus_write_holding_registers(SOCKETFD socketFd, int id, int address, const std::vector< int > &data)
  - Writes data to holding registers.
  - Function code: 10H
  - Parameters:
    - socketFd: The socket file descriptor.
    - id: The device ID.
    - address: The starting address of the holding registers.
    - data: A vector of integers representing the data to write.
```

----------------------------------------

TITLE: Insert Samov Command for Robot
DESCRIPTION: Inserts a Samov (point-to-point movement) command into a job file for a specific robot. Requires socket file descriptor, robot number, line number, movement command parameters, and position data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_insert_samov_command_robot(SOCKETFD _socketFd_, int _robotNum_, int _line_, MoveCmd _moveCmd_, PositionData _posData_)
  - Inserts a Samov command into the job file for a specific robot.
  - Parameters:
    - _socketFd_: The socket file descriptor.
    - _robotNum_: The robot number.
    - _line_: The line number to insert the command at.
    - _moveCmd_: The movement command parameters. PositionData is used for point data.
    - _posData_: The position data for the movement.
```

LANGUAGE: C
CODE:
```
EXPORT_API Result job_insert_samov_command_robot(SOCKETFD _socketFd_, int _robotNum_, int _line_, MoveCmd _moveCmd_, PositionData _posData_);
```

----------------------------------------

TITLE: Get Conveyor Belt Track Parameters for Robot
DESCRIPTION: Retrieves the track range parameters for a specific robot on a conveyor belt. Requires a socket file descriptor, robot number, conveyor ID, and a structure to store the parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result conveyor_belt_get_track_range_paramters_robot(SOCKETFD socketFd, int robotNum, int conveyorID, ConveyorTrackRangeParams &param)
```

----------------------------------------

TITLE: Get Conveyor Belt Wait Point Parameters for Robot
DESCRIPTION: Retrieves the wait point parameters for a specific robot on a conveyor belt. Requires a socket file descriptor, robot number, conveyor ID, and a structure to store the parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__conveyor__belt__track_8h

LANGUAGE: c
CODE:
```
EXPORT_API Result conveyor_belt_get_wait_point_paramters_robot(SOCKETFD socketFd, int robotNum, int conveyorID, ConveyorWaitPointParams &param)
```

----------------------------------------

TITLE: Modbus Master Parameters
DESCRIPTION: Configuration parameters for a Modbus master, including RTU settings.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_r

LANGUAGE: APIDOC
CODE:
```
ModbusMasterParameter:
  RTU : Modbus RTU (Remote Terminal Unit) specific parameters.
```

----------------------------------------

TITLE: Robot DH Parameter Retrieval
DESCRIPTION: Retrieves the Denavit-Hartenberg (DH) parameters for a robot. This function requires a socket file descriptor and a reference to a RobotDHParam structure to store the parameters. It is used to get the DH parameters of the current robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_robot_dh_param(SOCKETFD socketFd, RobotDHParam &param)
  - Retrieves the Denavit-Hartenberg (DH) parameters for the current robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A reference to a RobotDHParam structure to store the DH parameters.
  - Returns: Result of the operation.
```

LANGUAGE: APIDOC
CODE:
```
get_robot_dh_param_robot(SOCKETFD socketFd, int robotNum, RobotDHParam &param)
  - Retrieves the Denavit-Hartenberg (DH) parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - param: A reference to a RobotDHParam structure to store the DH parameters.
  - Returns: Result of the operation.
```

----------------------------------------

TITLE: SafeIO Structure
DESCRIPTION: Defines the structure for safe input/output, including ports for two emergency stop signals.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io__parameter_8h_source

LANGUAGE: cpp
CODE:
```
struct SafeIO {
    int quickStopPort1;
    int quickStopPort2;
};
```

----------------------------------------

TITLE: Insert End Until
DESCRIPTION: Marks the end of an 'until' loop in a job. This function signifies the termination point of a previously defined 'until' block. It requires a socket file descriptor and the line number where the 'until' loop started.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
Result job_insert_end_until(SOCKETFD socketFd, int line);
```

----------------------------------------

TITLE: Digital Output Error Message Management
DESCRIPTION: Functions to set and get error messages for digital output ports. These functions require a socket file descriptor and a vector of AlarmdIO objects. They are used for managing alarm states of digital outputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: cpp
CODE:
```
int set_error_msg_of_digital_output(SOCKETFD socketFd, std::vector<AlarmdIO> msg);
int get_error_msg_of_digital_output(SOCKETFD socketFd, std::vector<AlarmdIO> &msg);
```

----------------------------------------

TITLE: Open Job
DESCRIPTION: Opens an existing job for operation. This function requires a socket file descriptor and the name of the job to be opened.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_open(SOCKETFD socketFd, const std::string& jobName);
```

----------------------------------------

TITLE: Get Global Position
DESCRIPTION: Retrieves the global position (GP) data. This function requires a valid SOCKETFD, a position name, and returns the position data in a double vector. The position data includes coordinates, orientation, and robot position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_global_position(SOCKETFD _socketFd_, std::string _posName_, std::vector< double > & _pos_)
  - Retrieves global position (GP) data.
  - Parameters:
    - _socketFd_: File descriptor for socket communication.
    - _posName_: Name of the global position (e.g., "GP0001").
    - _pos_: Output vector containing position data (length 14: first 7 for coordinates/attitude, last 7 for robot position).
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_global_position(SOCKETFD _socketFd_, std::string _posName_, std::vector< double > & _pos_);
```

----------------------------------------

TITLE: I/O and Timer Commands Insertion
DESCRIPTION: Functions for inserting I/O output commands and timer commands into a job. Robot-specific versions are available for both.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func_j

LANGUAGE: C++
CODE:
```
job_insert_io_out_command()
job_insert_io_out_command_robot()
job_insert_timer_command()
job_insert_timer_command_robot()
```

----------------------------------------

TITLE: Add TOFFSETON Command for Specific Robot to Queue
DESCRIPTION: Appends a TOFFSETON (trajectory offset start) command to the local motion queue for a specified robot. This function allows targeting specific robot instances.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: APIDOC
CODE:
```
queue_motion_push_back_TOFFSETON_robot(SOCKETFD _socketFd_, int _robotNum_, OffsetCommandParam _params_)
  - Appends a TOFFSETON command for a specific robot to the local motion queue.
  - Parameters:
    - _socketFd_: SOCKETFD, the socket file descriptor.
    - _robotNum_: int, the number of the target robot.
    - _params_: OffsetCommandParam, parameters for the offset command.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result queue_motion_push_back_TOFFSETON_robot(SOCKETFD _socketFd_, int _robotNum_, OffsetCommandParam _params_);
```

----------------------------------------

TITLE: Robot DH Parameter Retrieval
DESCRIPTION: Retrieves the Denavit-Hartenberg (DH) parameters for a robot. This function is used to get the kinematic configuration of the robot. It requires a valid socket file descriptor and a reference to a RobotDHParam structure to store the retrieved parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_robot_dh_param(SOCKETFD socketFd, RobotDHParam& param)
  - Retrieves the DH parameters for the connected robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - param: A reference to a RobotDHParam structure to store the DH parameters.
  - Returns: Result indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_robot_dh_param_robot(SOCKETFD socketFd, int robotNum, RobotDHParam& param)
  - Retrieves the DH parameters for a specific robot in a multi-robot system.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier of the robot.
    - param: A reference to a RobotDHParam structure to store the DH parameters.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Queue Motion Push Back Wave On
DESCRIPTION: Adds a 'wave on' motion to the queue. Requires a socket file descriptor and an ID for the motion.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h_source

LANGUAGE: c
CODE:
```
EXPORT_API Result queue_motion_push_back_wave_on(SOCKETFD socketFd, int id);
```

----------------------------------------

TITLE: Add VISION_POS Command to Queue
DESCRIPTION: Appends a VISION_POS (get vision position) command to the local motion queue. This command retrieves and stores a position identified by vision processing.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: APIDOC
CODE:
```
queue_motion_push_back_vision_craft_get_pos(SOCKETFD _socketFd_, int _id_, const std::string _posName_)
  - Appends a VISION_POS command to the local motion queue.
  - Parameters:
    - _socketFd_: SOCKETFD, the socket file descriptor.
    - _id_: int, the craft ID.
    - _posName_: const std::string, the variable name to store the position.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result queue_motion_push_back_vision_craft_get_pos(SOCKETFD _socketFd_, int _id_, const std::string _posName_);
```

----------------------------------------

TITLE: Weld Configuration API
DESCRIPTION: Provides functions to retrieve welding configuration parameters. It supports fetching configuration for a specific index or for a given robot number and index. These functions are part of the craft welding interface.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__weld_8h_source

LANGUAGE: APIDOC
CODE:
```
weld_get_config(socketFd, index, param)
  - Retrieves welding configuration for a given index.
  - Parameters:
    - socketFd: SOCKETFD - File descriptor for the socket connection.
    - index: int - The index of the configuration to retrieve.
    - param: ArcParam& - Reference to an ArcParam object to store the configuration.
  - Returns: Result - The result of the operation.

weld_get_config_robot(socketFd, robotNum, index, param)
  - Retrieves welding configuration for a specific robot and index.
  - Parameters:
    - socketFd: SOCKETFD - File descriptor for the socket connection.
    - robotNum: int - The robot number.
    - index: int - The index of the configuration to retrieve.
    - param: ArcParam& - Reference to an ArcParam object to store the configuration.
  - Returns: Result - The result of the operation.
```

----------------------------------------

TITLE: Get Current Line and Joint Speed
DESCRIPTION: Retrieves the current line speed and joint speeds. This function requires a valid socket file descriptor and output variables/vectors to store the speed data. It is used for general speed retrieval.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_curretn_line_speed_and_joint_speed(SOCKETFD socketFd, double& lineSpeed, std::vector<double>& jointSpeed, std::vector<double>& jointSpeedSync)
  - Retrieves current line speed and joint speeds.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - lineSpeed: Output variable to store the line speed.
    - jointSpeed: Output vector to store joint speed values.
    - jointSpeedSync: Output vector to store synchronized joint speed values.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: HMI Library IO Operations
DESCRIPTION: Provides a comprehensive set of functions for managing various IO operations within the HMI library. This includes setting and getting digital and analog inputs/outputs, remote function control, and safety IO configurations. These functions are crucial for robot control and monitoring.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h_source

LANGUAGE: APIDOC
CODE:
```
set_IO_reset_function(SOCKETFD socketFd, int robotNum, int type, std::vector< int > enable, std::vector< int > value)
  - Resets IO functions with specified parameters.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - type: The type of reset operation.
    - enable: A vector of integers indicating which functions to enable.
    - value: A vector of integers representing the values for the reset operation.
  - Returns: Result status of the operation.

set_remote_function(SOCKETFD socketFd, int robotNum, RemoteControl general, std::vector< RemoteProgram > program, int num=10)
  - Sets remote control functions for a robot.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - general: RemoteControl structure with general settings.
    - program: Vector of RemoteProgram structures for program settings.
    - num: Number of remote functions (default 10).
  - Returns: Result status of the operation.

get_error_msg_of_digital_output(SOCKETFD socketFd, std::vector< AlarmdIO > &msg)
  - Retrieves error messages for digital output ports.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - msg: Reference to a vector of AlarmdIO to store error messages.
  - Returns: Result status of the operation.

get_digital_input(SOCKETFD socketFd, std::vector< int > &in)
  - Retrieves all digital input values at once.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - in: Reference to a vector of integers to store digital input values.
  - Returns: Result status of the operation.

get_remote_status_tips(SOCKETFD socketFd, int robotNum, int &num, int &outagePort, int &outageValue, std::vector< RemoteProgram > &program)
  - Retrieves status tips for remote functions.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - num: Reference to an integer for the number of tips.
    - outagePort: Reference to an integer for the outage port.
    - outageValue: Reference to an integer for the outage value.
    - program: Reference to a vector of RemoteProgram for program status.
  - Returns: Result status of the operation.

get_error_msg_of_digital_input(SOCKETFD socketFd, std::vector< AlarmdIO > &msg)
  - Retrieves error messages for digital input ports.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - msg: Reference to a vector of AlarmdIO to store error messages.
  - Returns: Result status of the operation.

set_digital_output(SOCKETFD socketFd, int port, int value)
  - Sets the value of a digital output port.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - port: The digital output port number.
    - value: The value to set for the port.
  - Returns: Result status of the operation.

get_remote_program(SOCKETFD socketFd, int robotNum, int &num, std::vector< RemoteProgramSetting > &program)
  - Retrieves IO remote program settings.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - num: Reference to an integer for the number of programs.
    - program: Reference to a vector of RemoteProgramSetting for program details.
  - Returns: Result status of the operation.

get_force_digital_input(SOCKETFD socketFd, std::vector< int > &port, std::vector< double > &status)
  - Retrieves currently forced digital input ports and their statuses.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - port: Reference to a vector of integers for the port numbers.
    - status: Reference to a vector of doubles for the port statuses.
  - Returns: Result status of the operation.

set_force_digital_input(SOCKETFD socketFd, int port, int force, int value)
  - Sets whether a digital input port is forced and its value.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - port: The digital input port number.
    - force: Integer indicating whether to force the port (1 for force, 0 for not).
    - value: The value to set if forced.
  - Returns: Result status of the operation.

set_hard_enable_port(SOCKETFD socketFd, int enable, int port1, int port2)
  - Enables or disables hard enable and sets associated ports.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - enable: Integer to enable (1) or disable (0) hard enable.
    - port1: The first associated port.
    - port2: The second associated port.
  - Returns: Result status of the operation.

get_safe_IO_function(SOCKETFD socketFd, int robotNum, SafeIO &safeIO)
  - Retrieves safety IO settings for a robot.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - safeIO: Reference to a SafeIO structure to store safety settings.
  - Returns: Result status of the operation.

get_analog_input(SOCKETFD socketFd, std::vector< double > &ain)
  - Queries analog input values.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - ain: Reference to a vector of doubles to store analog input values.
  - Returns: Result status of the operation.

set_remote_program(SOCKETFD socketFd, int robotNum, std::vector< RemoteProgramSetting > program)
  - Selects IO remote programs for a robot.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - program: Vector of RemoteProgramSetting structures to set.
  - Returns: Result status of the operation.

get_remote_function(SOCKETFD socketFd, int robotNum, int &num, int &time, RemoteControl &general, std::vector< RemoteProgram > &program)
  - Retrieves remote IO function settings.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - num: Reference to an integer for the number of functions.
    - time: Reference to an integer for the time setting.
    - general: Reference to a RemoteControl structure for general settings.
    - program: Reference to a vector of RemoteProgram for program settings.
  - Returns: Result status of the operation.

get_IO_reset_function(SOCKETFD socketFd, int robotNum, int type, std::vector< int > &enable, std::vector< int > &value)
  - Retrieves IO reset related parameters.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - type: The type of reset operation.
    - enable: Reference to a vector of integers for enabled functions.
    - value: Reference to a vector of integers for reset values.
  - Returns: Result status of the operation.

set_error_msg_of_digital_input(SOCKETFD socketFd, std::vector< AlarmdIO > msg)
  - Sets error messages for digital input ports.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - msg: Vector of AlarmdIO structures containing error messages.
  - Returns: Result status of the operation.

set_error_msg_of_digital_output(SOCKETFD socketFd, std::vector< AlarmdIO > msg)
  - Sets error messages for digital output ports.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - msg: Vector of AlarmdIO structures containing error messages.
  - Returns: Result status of the operation.

get_hard_enable_port(SOCKETFD socketFd, int &enable, int &port1, int &port2)
  - Retrieves whether the hard enable switch is on and its bound ports.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - enable: Reference to an integer indicating if hard enable is on.
    - port1: Reference to an integer for the first bound port.
    - port2: Reference to an integer for the second bound port.
  - Returns: Result status of the operation.

get_remote_param(SOCKETFD socketFd, int robotNum, int &speed, bool &start, int &time, int &startTime, int &num)
  - Retrieves remote parameter settings.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - speed: Reference to an integer for speed setting.
    - start: Reference to a boolean for start setting.
    - time: Reference to an integer for time setting.
    - startTime: Reference to an integer for start time setting.
    - num: Reference to an integer for number setting.
  - Returns: Result status of the operation.

set_remote_status_tips(SOCKETFD socketFd, int robotNum, int outagePort, int outageValue, std::vector< RemoteProgram > program)
  - Sets status tips for remote functions.
  - Parameters:
    - socketFd: File descriptor for the socket connection.
    - robotNum: The robot number.
    - outagePort: The outage port number.
    - outageValue: The outage value.
    - program: Vector of RemoteProgram structures for program settings.
  - Returns: Result status of the operation.
```

----------------------------------------

TITLE: Class Members Overview
DESCRIPTION: Lists various class members and their associated classes within the net_lib library. This provides a quick reference to where specific variables and functions are defined.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_v

LANGUAGE: APIDOC
CODE:
```
value:
  - Belongs to: AlarmdIO, ParaGroup, RemoteProgram
  - Description: Represents a value, likely for alarm status or parameters.

varname:
  - Belongs to: ParaGroup
  - Description: The name of a parameter.

velocity:
  - Belongs to: MoveCmd
  - Description: Represents the velocity for a movement command.

verticalDeflection:
  - Belongs to: WaveParam
  - Description: Defines the vertical deflection parameter, possibly for wave generation.

VisionCalibrationData():
  - Belongs to: VisionCalibrationData
  - Description: Constructor for the VisionCalibrationData structure.

visionNum:
  - Belongs to: VisionCalibrationData
  - Description: A numerical identifier related to vision calibration data.
```

----------------------------------------

TITLE: ModbusMasterParameter Struct Members
DESCRIPTION: This snippet details the public attributes of the ModbusMasterParameter struct. It includes the communication type (TCP or RTU), a boolean flag for start address, and references to specific parameter structures for TCP and RTU.

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_modbus_master_parameter

LANGUAGE: cpp
CODE:
```
struct ModbusMasterParameter {
    std::string type;
    bool startAddress;
    ModbusTCPParameter TCP;
    ModbusRTUParameter RTU;
};
```

----------------------------------------

TITLE: Robot Joint Parameter Retrieval
DESCRIPTION: Retrieves the joint parameters for a robot. This function requires a socket file descriptor, a joint ID, and a reference to a RobotJointParam structure to store the parameters. It is used to get the joint parameters of the current robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_robot_joint_param(SOCKETFD socketFd, int id, RobotJointParam &param)
  - Retrieves the joint parameters for the current robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - id: The ID of the joint.
    - param: A reference to a RobotJointParam structure to store the joint parameters.
  - Returns: Result of the operation.
```

LANGUAGE: APIDOC
CODE:
```
get_robot_joint_param_robot(SOCKETFD socketFd, int robotNum, int id, RobotJointParam &param)
  - Retrieves the joint parameters for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The number of the robot.
    - id: The ID of the joint.
    - param: A reference to a RobotJointParam structure to store the joint parameters.
  - Returns: Result of the operation.
```

----------------------------------------

TITLE: Create Job with Robots
DESCRIPTION: Creates a new job and associates a specified number of robots with it. This function requires a socket file descriptor, the number of robots, and the name of the job.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_create_robot(SOCKETFD socketFd, int robotNum, const std::string& jobName);
```

----------------------------------------

TITLE: Robot Trajectory Recording API
DESCRIPTION: Provides functions to control and manage robot trajectory recording. This includes starting, stopping, and saving recorded trajectories. Functions are designed to work with a socket file descriptor for communication.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__track_8h

LANGUAGE: APIDOC
CODE:
```
track_record_save_robot(_socketFd, _robotNum, _trajName)
  Saves the recorded trajectory for a specific robot.
  Parameters:
    _socketFd: File descriptor for the socket connection.
    _robotNum: Identifier for the robot.
    _trajName: Name to save the trajectory under.

track_record_start(_socketFd, _maxSamplingNum, _samplingInterval)
  Starts the trajectory recording process.
  Parameters:
    _socketFd: File descriptor for the socket connection.
    _maxSamplingNum: Maximum number of sampling points (range: [200, 12000]).
    _samplingInterval: Interval between samples (range: [0.03, 1]).
  Notes:
    The Chinese description indicates '轨迹记录开始' (Trajectory recording start).

track_record_start_robot(_socketFd, _robotNum, _maxSamplingNum, _samplingInterval)
  Starts the trajectory recording process for a specific robot.
  Parameters:
    _socketFd: File descriptor for the socket connection.
    _robotNum: Identifier for the robot.
    _maxSamplingNum: Maximum number of sampling points (range: [200, 12000]).
    _samplingInterval: Interval between samples (range: [0.03, 1]).

track_record_stop(_socketFd)
  Stops the trajectory recording process.
  Parameters:
    _socketFd: File descriptor for the socket connection.
  Notes:
    The Chinese description indicates '轨迹记录关闭' (Trajectory recording stop).

track_record_stop_robot(_socketFd, _robotNum)
  Stops the trajectory recording process for a specific robot.
  Parameters:
    _socketFd: File descriptor for the socket connection.
    _robotNum: Identifier for the robot.
```

----------------------------------------

TITLE: Get Robot Running State
DESCRIPTION: Queries the current running state of a robot. This function returns an integer status code indicating whether the robot is running, stopped, or in an error state. It requires a socket file descriptor and a reference to an integer for the status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_robot_running_state(SOCKETFD socketFd, int& status)
  - Retrieves the current running state of the robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - status: A reference to an integer to store the robot's running state.
  - Returns: Result code indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_robot_running_state_robot(SOCKETFD socketFd, int robotNum, int& status)
  - Retrieves the current running state of a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier of the robot.
    - status: A reference to an integer to store the robot's running state.
  - Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: Class Members - LaserCuttingIOParam
DESCRIPTION: Lists variables associated with the LaserCuttingIOParam class, specifically water_cooler_fault.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_w

LANGUAGE: APIDOC
CODE:
```
water_cooler_fault: LaserCuttingIOParam
  - Description: Indicates a fault in the water cooler.
  - Type: (Not specified)
```

----------------------------------------

TITLE: Create Job
DESCRIPTION: Creates a new job with a specified name. This function requires a socket file descriptor and the name of the job to be created.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
EXPORT_API Result job_create(SOCKETFD socketFd, const std::string& jobName);
```

----------------------------------------

TITLE: Insert End Until for Robot
DESCRIPTION: Marks the end of an 'until' loop for a specific robot. This function terminates a robot-specific 'until' loop. It requires a socket file descriptor, robot number, and the line number where the 'until' loop started.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: cpp
CODE:
```
Result job_insert_end_until_robot(SOCKETFD socketFd, int robotNum, int line);
```

----------------------------------------

TITLE: Robot Movement and Coordinate System Configuration
DESCRIPTION: Functions for setting the robot's speed and configuring its coordinate system. `set_speed_c` adjusts the robot's movement speed, and `set_current_coord_c` sets the active coordinate system. `get_current_coord_c` retrieves the current coordinate system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__interface_8h_source

LANGUAGE: c
CODE:
```
int set_speed_c(SOCKETFD socketFd, int speed);
int set_current_coord_c(SOCKETFD socketFd, int coord);
int get_current_coord_c(SOCKETFD socketFd, int& coord);
```

----------------------------------------

TITLE: Get Motion Queue Length (General)
DESCRIPTION: Retrieves the current number of commands remaining in the motion queue. This function requires a socket file descriptor and an integer reference to store the length.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c
CODE:
```
queue_motion_get_queuelen(SOCKETFD _socketFd_, int & _len_)
```

----------------------------------------

TITLE: User Coordinate Number Management
DESCRIPTION: Functions to set and get the user coordinate number. These functions are essential for associating specific coordinate systems or configurations with individual users within the Inexbot system. They take a socket file descriptor and a user number as input.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_user_coord_number(SOCKETFD socketFd, int userNum)
  - Sets the coordinate number for a given user.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - userNum: The identifier for the user.
  - Returns: Result indicating success or failure.

get_user_coord_number(SOCKETFD socketFd, int& userNum)
  - Retrieves the coordinate number associated with a user.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - userNum: Reference to an integer where the user's coordinate number will be stored.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: C Interface Header Files
DESCRIPTION: This section lists the header files for the C interface of the net_lib library. These files define the core C functions and structures for interacting with the library.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__c__queue__operate_8h

LANGUAGE: c
CODE:
```
#include "parameter/nrc_define.h"
```

----------------------------------------

TITLE: Remote Function Control
DESCRIPTION: Functions to set and get remote function configurations for a specific robot. This involves controlling general remote operations and program execution. They require a socket file descriptor, robot number, and structures for remote control and program data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: cpp
CODE:
```
int set_remote_function(SOCKETFD socketFd, int robotNum, RemoteControl general, std::vector<RemoteProgram> program, int num = 10);
int get_remote_function(SOCKETFD socketFd, int robotNum, int &num, int &time, RemoteControl &general, std::vector<RemoteProgram> &program);
```

----------------------------------------

TITLE: Get Current Motor Speed for Specific Robot
DESCRIPTION: Retrieves the current motor speed for a specified robot and its external axes. This function requires the socket file descriptor, robot number, and output references for motor speed data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_speed_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< int > & _motorSpeed_, std::vector< int > & _motorSpeedSync_)
  Parameters:
    _socketFd_: The socket file descriptor for communication.
    _robotNum_: The identifier for the specific robot.
    _motorSpeed_: Output parameter for robot motor speeds in RPM.
    _motorSpeedSync_: Output parameter for external axis motor speeds in RPM.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_curretn_motor_speed_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< int > & _motorSpeed_, std::vector< int > & _motorSpeedSync_);
```

----------------------------------------

TITLE: Insert Conveyor Position Instruction for Robot
DESCRIPTION: Inserts a conveyor position instruction into the job file for a specific robot to get the conveyor tracking position. Requires a socket file descriptor, robot number, line number, job ID, and position name.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: c++
CODE:
```
int job_insert_conveyor_pos_robot(SOCKETFD socketFd, int robotNum, int line, int id, const std::string posName)
```

----------------------------------------

TITLE: Get Current Motor Payload for Specific Robot
DESCRIPTION: Retrieves the current motor payload for a specified robot and its external axes. This function requires the socket file descriptor, robot number, and output references for motor payload data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_curretn_motor_payload_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< double > & _motorPayload_, std::vector< double > & _motorPayloadSync_)
  Parameters:
    _socketFd_: The socket file descriptor for communication.
    _robotNum_: The identifier for the specific robot.
    _motorPayload_: Output parameter for robot motor payload values in percentage.
    _motorPayloadSync_: Output parameter for external axis motor payload values in percentage.
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_curretn_motor_payload_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< double > & _motorPayload_, std::vector< double > & _motorPayloadSync_);
```

----------------------------------------

TITLE: Set Analog Output Functions
DESCRIPTION: Functions to control analog outputs.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_s

LANGUAGE: APIDOC
CODE:
```
set_analog_output(channel, value)
  - Sets the value for an analog output channel.
  - Parameters:
    - channel: The analog output channel.
    - value: The analog value to set.
```

----------------------------------------

TITLE: Vision Position Parameter Retrieval
DESCRIPTION: Retrieves the position parameters for a specified vision system. This function is used to get the coordinate parameters of a vision system. It requires a socket file descriptor, a vision ID, and a structure to store the position parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__vision_8h

LANGUAGE: APIDOC
CODE:
```
vision_get_position_parameter(_socketFd, _visionId, _vsPamrm)

Parameters:
  _socketFd: SOCKETFD - File descriptor for the socket connection.
  _visionId: int - The ID of the vision system.
  _vsPamrm: VisionPositionParam& - Reference to a structure to store the position parameters.

Returns:
  int - Status code: 0 for success, -1 for message retrieval failure, -2 for client not found.

Notes:
  Obtains the range and position parameters for the configured vision system.
```

LANGUAGE: C
CODE:
```
EXPORT_API Result vision_get_position_parameter(SOCKETFD _socketFd, int _visionId, VisionPositionParam& _vsPamrm);
```

----------------------------------------

TITLE: Robot-Specific User Coordinate Number Management
DESCRIPTION: Functions to set and get the user coordinate number specifically for a robot. This allows for distinct coordinate management when multiple robots are involved, ensuring that user-specific settings are correctly applied to the intended robot.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_user_coord_number_robot(SOCKETFD socketFd, int robotNum, int userNum)
  - Sets the coordinate number for a user associated with a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - userNum: The identifier for the user.
  - Returns: Result indicating success or failure.

get_user_coord_number_robot(SOCKETFD socketFd, int robotNum, int& userNum)
  - Retrieves the coordinate number for a user associated with a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - userNum: Reference to an integer where the user's coordinate number will be stored.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Remote Status Functions
DESCRIPTION: Provides functions to set and get remote status tips for robots. These functions interact with a specified socket file descriptor, robot number, and related parameters like outage ports and values. They also handle a vector of RemoteProgram objects.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__io_8h

LANGUAGE: APIDOC
CODE:
```
set_remote_status_tips(SOCKETFD socketFd, int robotNum, int outagePort, int outageValue, std::vector<RemoteProgram> program)
  - Sets remote status tips for a given robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - outagePort: The outage port value.
    - outageValue: The outage value.
    - program: A vector of RemoteProgram objects.
  - Returns: Result (likely indicating success or failure).

get_remote_status_tips(SOCKETFD socketFd, int robotNum, int &num, int &outagePort, int &outageValue, std::vector<RemoteProgram> &program)
  - Gets remote status tips data for a given robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - num: Reference to an integer to store the number of items.
    - outagePort: Reference to an integer to store the outage port.
    - outageValue: Reference to an integer to store the outage value.
    - program: Reference to a vector of RemoteProgram objects to store the program data.
  - Returns: Result (likely indicating success or failure).
```

----------------------------------------

TITLE: Set Hard Enable Port Function
DESCRIPTION: Configures the hard enable port.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_s

LANGUAGE: APIDOC
CODE:
```
set_hard_enable_port(port_id, state)
  - Sets the state of a hard enable port.
  - Parameters:
    - port_id: The ID of the hard enable port.
    - state: The desired state (e.g., enabled, disabled).
```

----------------------------------------

TITLE: Robot Kinematics and Position Functions
DESCRIPTION: Provides functions to retrieve robot kinematics parameters (DH parameters, joint parameters) and various position-related information (four-point, global position, reachable positions). Includes C interface versions for some functions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_g

LANGUAGE: APIDOC
CODE:
```
get_four_point()
  Retrieves the four-point calibration data.

get_four_point_robot()
  Retrieves the four-point calibration data for the robot.

get_global_position()
  Retrieves the current global position of the robot.

get_global_position_robot()
  Retrieves the current global position of the robot (robot-specific).

get_global_sync_position()
  Retrieves the synchronized global position.

get_global_sync_position_robot()
  Retrieves the synchronized global position (robot-specific).

get_global_variant()
  Retrieves the global variant information.

get_global_variant_robot()
  Retrieves the global variant information (robot-specific).

get_origin_coord_to_target_coord()
  Calculates the transformation from origin coordinates to target coordinates.

get_origin_coord_to_target_coord_robot()
  Calculates the transformation from origin coordinates to target coordinates (robot-specific).

get_pos_reachable()
  Checks if a given position is reachable by the robot.

get_pos_reachable_robot()
  Checks if a given position is reachable by the robot (robot-specific).

get_robot_dh_param()
  Retrieves the Denavit-Hartenberg (DH) parameters for the robot.

get_robot_dh_param_robot()
  Retrieves the Denavit-Hartenberg (DH) parameters for the robot (robot-specific).

get_robot_joint_param()
  Retrieves the joint parameters of the robot.

get_robot_joint_param_robot()
  Retrieves the joint parameters of the robot (robot-specific).

get_robot_running_state()
  Retrieves the current running state of the robot.

get_robot_running_state_robot()
  Retrieves the current running state of the robot (robot-specific).

get_robot_type()
  Retrieves the type of the robot.

get_robot_type_robot()
  Retrieves the type of the robot (robot-specific).
```

LANGUAGE: C
CODE:
```
get_origin_coord_to_target_coord_c()
  Calculates the transformation from origin coordinates to target coordinates (C interface).

get_robot_dh_param_c()
  Retrieves the Denavit-Hartenberg (DH) parameters for the robot (C interface).

get_robot_joint_param_c()
  Retrieves the joint parameters of the robot (C interface).

get_robot_running_state_c()
  Retrieves the current running state of the robot (C interface).
```

----------------------------------------

TITLE: Servo State Management
DESCRIPTION: Manages the state of the servo motor, allowing transitions between stop, ready, alarm, and running states. Specific state transitions have restrictions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
set_servo_state(SOCKETFD socketFd, int state)
  - Sets the state of the servo motor.
  - Parameters:
    - socketFd: A file descriptor for the socket connection.
    - state: The desired servo state (0: stop, 1: ready).
  - Warning:
    - To set servo to ready, ensure no system errors by calling clear_servo_error(SOCKETFD socketFd) first.
    - This function is only effective when the servo state is 0 (stop) or 1 (ready).
    - Cannot directly set servo state if it is 2 (alarm) or 3 (running).

set_servo_state_robot(SOCKETFD socketFd, int robotNum, int state)
  - Sets the state of the servo motor for a specific robot.
  - Parameters:
    - socketFd: A file descriptor for the socket connection.
    - robotNum: The identifier for the robot.
    - state: The desired servo state (0: stop, 1: ready).
```

----------------------------------------

TITLE: HMI Job Operations API
DESCRIPTION: This section details the API functions for managing job files. It includes operations for opening, getting job line counts, retrieving job content by line, deleting commands by line, and running jobs. Functions are provided for both general job operations and robot-specific operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_open(SOCKETFD socketFd, const std::string &jobName)
  - Opens the specified job file.
  - Parameters:
    - socketFd: The socket file descriptor.
    - jobName: The name of the job file to open.
  - Returns: Result status.

job_open_robot(SOCKETFD socketFd, int robotNum, const std::string &jobName)
  - Opens the specified job file for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - jobName: The name of the job file to open.
  - Returns: Result status.

job_get_command_total_lines(SOCKETFD socketFd, int &totalLines)
  - Gets the total number of lines in the job file.
  - Parameters:
    - socketFd: The socket file descriptor.
    - totalLines: Reference to an integer to store the total lines.
  - Returns: Result status.

job_get_command_total_lines_robot(SOCKETFD socketFd, int robotNum, int &totalLines)
  - Gets the total number of lines in the job file for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - totalLines: Reference to an integer to store the total lines.
  - Returns: Result status.

job_get_command_content_by_line(SOCKETFD socketFd, int line, int &commandType, std::string &jobContent)
  - Gets the job file content for a specific line.
  - Parameters:
    - socketFd: The socket file descriptor.
    - line: The line number to retrieve.
    - commandType: Reference to an integer to store the command type.
    - jobContent: Reference to a string to store the job content.
  - Returns: Result status.

job_get_command_content_by_line_robot(SOCKETFD socketFd, int robotNum, int line, int &commandType, std::string &jobContent)
  - Gets the job file content for a specific line for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - line: The line number to retrieve.
    - commandType: Reference to an integer to store the command type.
    - jobContent: Reference to a string to store the job content.
  - Returns: Result status.

job_delete_command_by_line(SOCKETFD socketFd, int line)
  - Deletes a command from the job file by line number.
  - Parameters:
    - socketFd: The socket file descriptor.
    - line: The line number of the command to delete.
  - Returns: Result status.

job_delete_command_by_line_robot(SOCKETFD socketFd, int robotNum, int line)
  - Deletes a command from the job file by line number for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - line: The line number of the command to delete.
  - Returns: Result status.

job_run(SOCKETFD socketFd, const std::string &jobName)
  - Runs the specified job file.
  - Parameters:
    - socketFd: The socket file descriptor.
    - jobName: The name of the job file to run.
  - Returns: Result status.

job_run_robot(SOCKETFD socketFd, int robotNum, const std::string &jobName)
  - Runs the specified job file for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor.
    - robotNum: The robot number.
    - jobName: The name of the job file to run.
  - Returns: Result status.
```

----------------------------------------

TITLE: User Coordinate Data Setting
DESCRIPTION: Functions to set the detailed coordinate data for a user. This allows for updating or initializing the positional information associated with a user's configuration, which can be critical for robot control and simulation.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
set_user_coordinate_data(SOCKETFD socketFd, int userNum, std::vector<double> pos)
  - Sets the coordinate data for a given user.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - userNum: The identifier for the user.
    - pos: A vector of doubles containing the coordinate data to be set (e.g., [x, y, z]).
  - Returns: Result indicating success or failure.

set_user_coordinate_data_robot(SOCKETFD socketFd, int robotNum, int userNum, std::vector<double> pos)
  - Sets the coordinate data for a user associated with a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier for the robot.
    - userNum: The identifier for the user.
    - pos: A vector of doubles containing the coordinate data to be set.
  - Returns: Result indicating success or failure.
```

----------------------------------------

TITLE: Get Robot Motion Status
DESCRIPTION: Retrieves the current status of a robot's motion queue. This function requires a socket file descriptor, the robot number, and a reference to a boolean to store the status.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: cpp
CODE:
```
Result queue_motion_get_status_robot(SOCKETFD _socketFd, int _robotNum, bool& _status)
```

----------------------------------------

TITLE: Add Vision Craft Get Position Command to Queue
DESCRIPTION: Appends a 'vision_craft_get_pos' command to the motion queue to retrieve a specific vision position. Requires a socket file descriptor, ID, and the name of the position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__queue__operate_8h

LANGUAGE: c++
CODE:
```
EXPORT_API Result queue_motion_push_back_vision_craft_get_pos(SOCKETFD socketFd, int id, const std::string posName)
```

----------------------------------------

TITLE: Robot Joint Parameters
DESCRIPTION: Details parameters related to robot joint configuration, including speeds and reduction ratios.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_r

LANGUAGE: APIDOC
CODE:
```
RobotJointParam:
  ratedDeRotSpeed : Rated deceleration speed of the robot joint.
  ratedRotSpeed : Rated rotational speed of the robot joint.
  ratedVel : Rated velocity of the robot joint.
  reducRatio : Reduction ratio of the robot joint.
```

----------------------------------------

TITLE: job_create_robot API
DESCRIPTION: Creates a new robot job. Requires a socket file descriptor, robot number, and job name.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h

LANGUAGE: APIDOC
CODE:
```
job_create_robot(_socketFd: SOCKETFD, _robotNum: int, _jobName: const std::string &)
  - Creates a new robot job.
  - Parameters:
    - _socketFd: File descriptor for the socket connection.
    - _robotNum: The number of the robot.
    - _jobName: The name of the job to create.
```

----------------------------------------

TITLE: Get Current Extra Position
DESCRIPTION: Retrieves the current 'extra' position data for a robot. This might include additional kinematic information or tool center point (TCP) data. It requires a socket file descriptor and a vector to store the position data.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: APIDOC
CODE:
```
get_current_extra_position(SOCKETFD socketFd, std::vector<double>& pos)
  - Retrieves the current extra position data for the robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - pos: A reference to a vector of doubles to store the extra position data.
  - Returns: Result code indicating success or failure.
```

LANGUAGE: APIDOC
CODE:
```
get_current_extra_position_robot(SOCKETFD socketFd, int robotNum, std::vector<double>& pos)
  - Retrieves the current extra position data for a specific robot.
  - Parameters:
    - socketFd: The socket file descriptor for communication.
    - robotNum: The identifier of the robot.
    - pos: A reference to a vector of doubles to store the extra position data.
  - Returns: Result code indicating success or failure.
```

----------------------------------------

TITLE: ConveyorTrackRangeParams Members
DESCRIPTION: Details the members of the ConveyorTrackRangeParams structure, which likely holds parameters for defining a range along a conveyor track. This includes conveyor ID and various positional data (X, Y, Z coordinates, start points).

SOURCE: https://doc.hmilib.inexbot.coision.cn/struct_conveyor_track_range_params-members

LANGUAGE: APIDOC
CODE:
```
ConveyorTrackRangeParams:
  conveyorID: Identifier for the conveyor.
  position_receLatestPos: Latest received position data.
  position_trackRangeXMax: Maximum X-coordinate for the track range.
  position_trackRangeYMax: Maximum Y-coordinate for the track range.
  position_trackRangeYMin: Minimum Y-coordinate for the track range.
  position_trackRangeZMax: Maximum Z-coordinate for the track range.
  position_trackRangeZMin: Minimum Z-coordinate for the track range.
  position_trackStartXPoint: Starting point X-coordinate for the track.
```

----------------------------------------

TITLE: Motion Queue Operations
DESCRIPTION: Provides an overview of the motion queue operations available for robot and controller interactions. These functions manage the lifecycle of motion commands within the queue system.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_q

LANGUAGE: APIDOC
CODE:
```
queue_motion_restart_robot()
  - Restarts the motion queue for the robot.
  - Source: nrc_queue_operate.h

queue_motion_send_to_controller()
  - Sends motion commands to the controller.
  - Source: nrc_queue_operate.h

queue_motion_send_to_controller_c()
  - Sends motion commands to the controller using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_send_to_controller_robot()
  - Sends motion commands to the robot controller.
  - Source: nrc_queue_operate.h

queue_motion_set_status()
  - Sets the status of the motion queue.
  - Source: nrc_queue_operate.h

queue_motion_set_status_c()
  - Sets the status of the motion queue using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_set_status_robot()
  - Sets the status of the motion queue for the robot.
  - Source: nrc_queue_operate.h

queue_motion_size()
  - Retrieves the current size of the motion queue.
  - Source: nrc_queue_operate.h

queue_motion_size_robot()
  - Retrieves the current size of the motion queue for the robot.
  - Source: nrc_queue_operate.h

queue_motion_stop()
  - Stops all motion commands in the queue.
  - Source: nrc_queue_operate.h

queue_motion_stop_not_power_off()
  - Stops motion commands without powering off.
  - Source: nrc_queue_operate.h

queue_motion_stop_not_power_off_c()
  - Stops motion commands without powering off, using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_stop_not_power_off_robot()
  - Stops motion commands for the robot without powering off.
  - Source: nrc_queue_operate.h

queue_motion_stop_robot()
  - Stops all motion commands for the robot.
  - Source: nrc_queue_operate.h

queue_motion_suspend()
  - Suspends motion commands in the queue.
  - Source: nrc_queue_operate.h

queue_motion_suspend_c()
  - Suspends motion commands using the C interface.
  - Source: nrc_c_queue_operate.h

queue_motion_suspend_robot()
  - Suspends motion commands for the robot.
  - Source: nrc_queue_operate.h
```

----------------------------------------

TITLE: LaserCuttingEquipment Variables
DESCRIPTION: Variables associated with the LaserCuttingEquipment class, including wait times for operations.

SOURCE: https://doc.hmilib.inexbot.coision.cn/functions_vars_w

LANGUAGE: APIDOC
CODE:
```
LaserCuttingEquipment:
  waitFollowTime: Time to wait for following operations.
  waitLiftUpTime: Time to wait for lift-up operations.
```

----------------------------------------

TITLE: Get Controller ID for Robot (C# style)
DESCRIPTION: Retrieves the serial number ID of the controller for a specific robot using a C# style vector of characters. Requires a socket file descriptor, robot number, and a reference to a vector of characters for the ID.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_controller_id_csharp_robot(SOCKETFD _socketFd_, int _robotNum_, std::vector< char > & _id_)
  Parameters:
    _socketFd_: File descriptor for the socket connection.
    _robotNum_: The robot number.
    _id_: Reference to a vector of characters to store the controller serial number ID.
```

----------------------------------------

TITLE: Insert General Move Command
DESCRIPTION: Inserts a general move command into the job file with specified motion parameters. Requires socket file descriptor, line number, move type, velocities, accelerations, decelerations, time, and planning points.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__job__operate_8h_source

LANGUAGE: c++
CODE:
```
EXPORT_API Result job_insert_moveComm(SOCKETFD socketFd, int line, std::string moveType, double m_vel, double m_acc, double m_dec, int m_time, int m_pl);
```

----------------------------------------

TITLE: Get Global Synchronized Position
DESCRIPTION: Retrieves the global synchronized position (GE) data. This function requires a valid SOCKETFD and a position name. It returns the position data in a double vector, including coordinates, robot position, and external axis position.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: APIDOC
CODE:
```
get_global_sync_position(SOCKETFD _socketFd_, const std::string & _posName_, std::vector< double > & _pos_)
  - Retrieves global synchronized position (GE) data.
  - Parameters:
    - _socketFd_: File descriptor for socket communication.
    - _posName_: Name of the global synchronized position (e.g., "GE0001").
    - _pos_: Output vector containing position data (length 21: first 7 for coordinates/attitude, next 7 for robot position, last 7 for external axis position).
```

LANGUAGE: C++
CODE:
```
EXPORT_API Result get_global_sync_position(SOCKETFD _socketFd_, const std::string & _posName_, std::vector< double > & _pos_);
```

----------------------------------------

TITLE: LaserCuttingEquipment Parameters
DESCRIPTION: Defines various equipment settings for laser cutting operations. This includes parameters related to aspiration, focus compensation, and movement controls.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting__parameter_8h_source

LANGUAGE: cpp
CODE:
```
struct LaserCuttingEquipment {
    int delAspiratedMode;
    int follow;
    int arrivalOutLightMode;
    double delAspiratedTime;
    bool focusCompensation;
    int rePerforate;
    double focusCompensationConstant;
    double focusCompensationTime;
    double focusCompensationPower;
    double waitLiftUpTime;
    double preAspiratedTime;
    int focusFormula;
    double waitFollowTime;
    double RetreatDistance;
    double collisionDistance;
};
```

----------------------------------------

TITLE: Robot Linear Movement
DESCRIPTION: Executes a linear movement for the robot. This function requires a socket file descriptor and a MoveCmd structure containing the movement parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: c++
CODE:
```
Result robot_movel(SOCKETFD socketFd, MoveCmd moveCmd);
Result robot_movel_robot(SOCKETFD socketFd, int robotNum, MoveCmd moveCmd);
```

----------------------------------------

TITLE: Set Position Drag Parameters Functions
DESCRIPTION: Functions to configure position drag parameters. Includes variations for robot interfaces.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_s

LANGUAGE: APIDOC
CODE:
```
set_position_dragParams(drag_type, value)
  - Sets position drag parameters.
  - Parameters:
    - drag_type: The type of drag parameter.
    - value: The value for the drag parameter.

set_position_dragParams_robot(drag_type, value)
  - Sets position drag parameters for the robot.
  - Parameters:
    - drag_type: The type of drag parameter.
    - value: The value for the drag parameter.
```

----------------------------------------

TITLE: Robot Movement Commands (C++)
DESCRIPTION: Provides functions for controlling robot movements, including joint space, Cartesian space, and spline path movements. These functions typically require a socket file descriptor, robot number, target positions, velocity, coordinate system, acceleration, and deceleration parameters.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h

LANGUAGE: cpp
CODE:
```
int robot_movec_robot(SOCKETFD socketFd, int robotNum, std::vector< double > pos1, std::vector< double > pos2, std::vector< double > pos3, int vel, int coord, int acc, int dec);
int robot_moveca(SOCKETFD socketFd, std::vector< double > pos1, std::vector< double > pos2, std::vector< double > pos3, int vel, int coord, int acc, int dec);
int robot_moveca_robot(SOCKETFD socketFd, int robotNum, std::vector< double > pos1, std::vector< double > pos2, std::vector< double > pos3, int vel, int coord, int acc, int dec);
int robot_moves(SOCKETFD socketFd, std::vector< std::vector< double > > pos, int vel, int coord, int acc, int dec);
int robot_moves_robot(SOCKETFD socketFd, int robotNum, std::vector< std::vector< double > > pos, int vel, int coord, int acc, int dec);
```

----------------------------------------

TITLE: Laser Cutting IO Parameter Settings
DESCRIPTION: Sets the analog I/O parameters for laser cutting operations. This function requires a socket file descriptor and a LaserCuttingIOParam structure. It returns a status code indicating success or failure.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__craft__laser__cutting_8h

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_io_parameter(SOCKETFD _socketFd, LaserCuttingIOParam _param)
  - Sets analog I/O parameters for laser cutting.
  - Parameters:
    - _socketFd: The socket file descriptor for communication.
    - _param: A structure of type LaserCuttingIOParam containing the I/O parameters.
  - Returns: Status code (0 for success, -1 for message wait failure, -2 for client not found).
```

LANGUAGE: APIDOC
CODE:
```
laser_cutting_set_io_parameter_robot(SOCKETFD _socketFd, int _robotNum, LaserCuttingIOParam _param)
  - Sets analog I/O parameters for laser cutting operations controlled by a robot.
  - Parameters:
    - _socketFd: The socket file descriptor for communication.
    - _robotNum: The robot number.
    - _param: A structure of type LaserCuttingIOParam containing the I/O parameters.
  - Returns: Status code (0 for success, -1 for message wait failure, -2 for client not found).
```

----------------------------------------

TITLE: Set Current Mode (C)
DESCRIPTION: C function to set the current operating mode. Requires a socket file descriptor and the desired mode. Assumes the existence of EXPORT_API and Result definitions.

SOURCE: https://doc.hmilib.inexbot.coision.cn/nrc__interface_8h_source

LANGUAGE: C
CODE:
```
EXPORT_API Result set_current_mode(SOCKETFD socketFd, int mode);
```

----------------------------------------

TITLE: backup_system Function
DESCRIPTION: The backup_system function is available in the nrc_job_operate.h file. This function is likely related to system backup operations within the net_lib library.

SOURCE: https://doc.hmilib.inexbot.coision.cn/globals_func

LANGUAGE: javascript
CODE:
```
backup_system()
```