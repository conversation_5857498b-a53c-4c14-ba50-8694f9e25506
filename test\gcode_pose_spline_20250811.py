#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gcode_pose_spline_20250811.py
-----------------------------
目标：在“点很密集 / 姿态块间有差异”的情况下，仍保持平滑且“看上去恒速”。
做法：
  • 空间：对连续的 G1 段使用 **Centripetal Catmull‑Rom** 样条（C¹ 连续），消除几何折线的顿挫；
  • 姿态：全段使用 **SQUAD**（四元数样条，C¹ 连续角速度）；
  • 采样：先用较密的“预采样”得到细路径，再按“线性+角度”**组合度量**做**等距重采样**，
           从源点密度中“解耦”，获得更均匀的末端速度与角速度。

KISS / YAGNI：不引入复杂时标（如Jerk受限规划），只做几何样条 + 等距采样 + 控制器 pl 平滑。
"""

import os
import re
import time
import math
import threading
from queue import Queue, Empty

# ------------------------- 基本配置（按需修改） -------------------------

GCODE_FILE = "jiyi copy 2.Gcode"

try:
    from config import ROBOT_IP, ROBOT_PORT
except Exception:
    ROBOT_IP, ROBOT_PORT = "************", "6001"

USER_COORD_NUMBER = 1

# 速度（通过 set_speed 全局切换，MoveCmd.velocity 固定 100）
G0_VELOCITY_PERCENT = 35
G1_VELOCITY_PERCENT = 50
ACCEL_PERCENT = 20

# 控制器平滑（0~8）：适当提高能进一步抑制角速度尖峰
SMOOTHING_LEVEL = 5

# 队列参数
CONTROLLER_QUEUE_CAPACITY = 200
BATCH_SEND_SIZE = 20

# ------------------------ 插值/采样参数 -------------------------------

# A/B/C → RX/RY/RZ 的静态差补（确保 A=B=C=0 → RX=180°,RY=0°,RZ=0°）
GCODE_TO_ROBOT_OFFSET = (180.0, 0.0, 0.0)

# 空间样条：Centripetal Catmull-Rom，alpha=0.5（更抗拐角振铃）
CR_ALPHA = 0.5

# 预采样密度（越大越细，随后还会再做“等距重采样”）
PRE_POS_STEP_MM = 1.0      # 预采样时希望的空间步长
PRE_ANG_STEP_DEG = 2.0     # 预采样时希望的角步长
OVERSAMPLE_FACTOR = 2       # 在上面基础上再放大（保证足够细）

# 等距重采样：将线性/角度统一为“等效长度”（mm）
POS_STEP_MAX_MM = 1.0      # 输出点的最大空间间距
ANG_STEP_MAX_DEG = 1.5     # 输出点的最大角度步长
ANG_MM_PER_DEG = 0.8       # 角度 → 等效“mm”的换算因子（mm/deg）；可根据工艺调

# 在整段（G1块）首尾做轻微缓入缓出（仅参数重映射，不改变曲线形状）
EASE_AT_BLOCK_ENDS = True
EASE_RATIO = 0.12          # 首尾各占比（0~0.3 区间内调）

# ---------------------------------------------------------------------

import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc


# ----------------------------- 数学工具 -------------------------------

def deg2rad(d): return d * math.pi / 180.0
def rad2deg(r): return r * 180.0 / math.pi

def normalize_angle_deg(a):
    a = (a + 180.0) % 360.0 - 180.0
    if a <= -180.0: a += 360.0
    return a

def euler_xyz_deg_to_quat(rx, ry, rz):
    rx, ry, rz = deg2rad(rx), deg2rad(ry), deg2rad(rz)
    cx, sx = math.cos(rx/2), math.sin(rx/2)
    cy, sy = math.cos(ry/2), math.sin(ry/2)
    cz, sz = math.cos(rz/2), math.sin(rz/2)
    w = cz*cy*cx + sz*sy*sx
    x = cz*cy*sx - sz*sy*cx
    y = cz*sy*cx + sz*cy*sx
    z = sz*cy*cx - cz*sy*sx
    return quat_normalize((w,x,y,z))

def quat_to_euler_xyz_deg(q):
    w,x,y,z = q
    r11 = 1 - 2*(y*y + z*z)
    r21 = 2*(x*y + w*z)
    r31 = 2*(x*z - w*y)
    r32 = 2*(y*z + w*x)
    r33 = 1 - 2*(x*x + y*y)
    rx = math.atan2(r32, r33)
    ry = -math.asin(max(-1.0, min(1.0, r31)))
    rz = math.atan2(r21, r11)
    return (rad2deg(rx), rad2deg(ry), rad2deg(rz))

def quat_normalize(q):
    w,x,y,z = q
    n = math.sqrt(w*w+x*x+y*y+z*z)
    if n == 0: return (1.0,0.0,0.0,0.0)
    return (w/n, x/n, y/n, z/n)

def quat_dot(a,b): return a[0]*b[0]+a[1]*b[1]+a[2]*b[2]+a[3]*b[3]
def quat_inv(q): return (q[0], -q[1], -q[2], -q[3])

def quat_mul(a,b):
    aw,ax,ay,az = a; bw,bx,by,bz = b
    return (aw*bw - ax*bx - ay*by - az*bz,
            aw*bx + ax*bw + ay*bz - az*by,
            aw*by - ax*bz + ay*bw + az*bx,
            aw*bz + ax*by - ay*bx + az*bw)

def quat_log(q):
    w,x,y,z = quat_normalize(q)
    vnorm = math.sqrt(x*x+y*y+z*z)
    if vnorm < 1e-12:
        return (0.0, 0.0, 0.0, 0.0)
    theta = math.atan2(vnorm, w)
    scale = theta / vnorm
    return (0.0, x*scale, y*scale, z*scale)

def quat_exp(p):
    _,x,y,z = p
    theta = math.sqrt(x*x+y*y+z*z)
    if theta < 1e-12:
        return (1.0, 0.0, 0.0, 0.0)
    s = math.sin(theta)/theta
    return (math.cos(theta), x*s, y*s, z*s)

def quat_slerp(q0, q1, t):
    cos_th = quat_dot(q0,q1)
    if cos_th < 0.0:
        q1 = (-q1[0], -q1[1], -q1[2], -q1[3])
        cos_th = -cos_th
    if cos_th > 0.9995:
        w = q0[0] + t*(q1[0]-q0[0])
        x = q0[1] + t*(q1[1]-q0[1])
        y = q0[2] + t*(q1[2]-q0[2])
        z = q0[3] + t*(q1[3]-q0[3])
        return quat_normalize((w,x,y,z))
    th = math.acos(cos_th)
    sin_th = math.sin(th)
    s0 = math.sin((1-t)*th) / sin_th
    s1 = math.sin(t*th) / sin_th
    return (q0[0]*s0+q1[0]*s1, q0[1]*s0+q1[1]*s1, q0[2]*s0+q1[2]*s1, q0[3]*s0+q1[3]*s1)

def angle_between_quats_deg(q0, q1):
    c = abs(quat_dot(q0,q1))
    c = max(-1.0, min(1.0, c))
    return rad2deg(2.0 * math.acos(c))

def squad(q0, s0, s1, q1, t):
    a = quat_slerp(q0, q1, t)
    b = quat_slerp(s0, s1, t)
    tau = 2.0 * t * (1.0 - t)
    return quat_slerp(a, b, tau)


# ------------------------ Catmull-Rom（XYZ） --------------------------

def chord_len(p, q):
    dx,dy,dz = (q[0]-p[0], q[1]-p[1], q[2]-p[2])
    return math.sqrt(dx*dx + dy*dy + dz*dz)

def centripetal_params(points, alpha=0.5):
    n = len(points)
    t = [0.0]*n
    for i in range(1, n):
        t[i] = t[i-1] + pow(chord_len(points[i-1], points[i]), alpha)
    return t

def hermite_coeff(u):
    u2, u3 = u*u, u*u*u
    h00 =  2*u3 - 3*u2 + 1
    h10 =      u3 - 2*u2 + u
    h01 = -2*u3 + 3*u2
    h11 =      u3 -   u2
    return h00, h10, h01, h11

def catmull_rom_segment(Pm1, P0, P1, P2, t_m1, t0, t1, t2, t):
    """返回在 [t0, t1] 上参数 t 处的位置。"""
    if t1 == t0: return P0
    # 端点导数（centripetal Catmull-Rom 的一致写法）
    m0 = tuple( (P1[i]-Pm1[i]) / (t1 - t_m1 + 1e-9) for i in range(3) )
    m1 = tuple( (P2[i]-P0[i]) / (t2 - t0 + 1e-9) for i in range(3) )
    u = (t - t0) / (t1 - t0)
    h00,h10,h01,h11 = hermite_coeff(u)
    # 注意 Hermite 里的切线需要乘以段时长 (t1-t0)
    dt = (t1 - t0)
    res = tuple( h00*P0[i] + h01*P1[i] + h10*m0[i]*dt + h11*m1[i]*dt for i in range(3) )
    return res


# ---------------------------- 控制器封装 ------------------------------

class RobotController:
    def __init__(self, ip, port):
        self.ip, self.port = ip, port
        self.socket_fd = -1
        self.is_connected = False

    def connect(self):
        print(f"🔗 连接 {self.ip}:{self.port} ...")
        self.socket_fd = nrc.connect_robot(self.ip, str(self.port))
        self.is_connected = self.socket_fd > 0
        print("✅ 已连接" if self.is_connected else "❌ 连接失败")
        return self.is_connected

    def disconnect(self):
        if self.is_connected:
            try: nrc.disconnect_robot(self.socket_fd)
            finally:
                self.is_connected = False
                self.socket_fd = -1

    def _power_on_if_needed(self) -> bool:
        try:
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            cur = res[1] if isinstance(res, list) else -1
            if cur == 3: return True
            nrc.set_current_mode(self.socket_fd, 0)  # TEACH
            time.sleep(0.1)
            nrc.clear_error(self.socket_fd)
            nrc.set_servo_state(self.socket_fd, 1)  # READY
            time.sleep(1.0)
            if nrc.set_servo_poweron(self.socket_fd) != 0:
                return False
            time.sleep(1.0)
            s = 0
            res = nrc.get_servo_state(self.socket_fd, s)
            return (isinstance(res, list) and res[1] == 3)
        except Exception:
            return False

    def initialize(self) -> bool:
        if not self.is_connected: return False
        if not self._power_on_if_needed(): return False
        if nrc.set_current_mode(self.socket_fd, 2) != 0:  # RUN
            print("❌ 切到运行模式失败"); return False
        nrc.queue_motion_clear_Data(self.socket_fd)
        if nrc.queue_motion_set_status(self.socket_fd, True) != 0:
            print("❌ 启用队列失败"); return False
        if nrc.set_user_coord_number(self.socket_fd, USER_COORD_NUMBER) != 0:
            print("❌ 设置用户坐标失败"); return False
        print("✅ 初始化完毕（运行模式/队列开启）")
        return True

    def power_off(self):
        try:
            nrc.queue_motion_stop(self.socket_fd); time.sleep(0.2)
            nrc.set_servo_poweroff(self.socket_fd)
        except Exception:
            pass

    def get_queue_length(self) -> int:
        res = nrc.queue_motion_get_queuelen(self.socket_fd, 0)
        return res[1] if isinstance(res, list) and res[0] == 0 else -1

    def set_speed(self, percent: int) -> bool:
        return nrc.set_speed(self.socket_fd, percent) == 0

    def add_point(self, x,y,z, rx_deg, ry_deg, rz_deg, move_type='G1'):
        rx = deg2rad(normalize_angle_deg(rx_deg))
        ry = deg2rad(normalize_angle_deg(ry_deg))
        rz = deg2rad(normalize_angle_deg(rz_deg))

        cmd = nrc.MoveCmd()
        cmd.targetPosType = 0
        cmd.targetPosValue = nrc.VectorDouble([x,y,z, rx,ry,rz])
        cmd.coord = 3
        cmd.userNum = USER_COORD_NUMBER
        cmd.velocity = 100
        cmd.acc = ACCEL_PERCENT
        cmd.dec = ACCEL_PERCENT
        cmd.pl  = SMOOTHING_LEVEL

        if move_type == 'G1':
            r = nrc.queue_motion_push_back_moveL(self.socket_fd, cmd)
        else:
            r = nrc.queue_motion_push_back_moveJ(self.socket_fd, cmd)
        if r != 0:
            raise RuntimeError(f"push_back失败: {r}")

    def flush(self, n):
        if n<=0: return True
        return nrc.queue_motion_send_to_controller(self.socket_fd, n) == 0


# ------------------------- G-code 解析与数据结构 -----------------------

_COORD_RE = re.compile(r'([XYZABCFE])([-+]?\d*\.?\d+)')

class Waypoint:
    __slots__ = ("cmd","x","y","z","a","b","c","q")
    def __init__(self, cmd, x,y,z, a,b,c, q):
        self.cmd, self.x, self.y, self.z = cmd, x,y,z
        self.a, self.b, self.c = a,b,c
        self.q = q  # 已经做了静态偏移后的姿态四元数

def parse_line(line: str):
    s = line.strip()
    if not s or s.startswith(';'): return None
    up = s.upper()
    if not (up.startswith('G0') or up.startswith('G1')): return None
    cmd = 'G0' if up.startswith('G0') else 'G1'
    vals = dict((k, float(v)) for k, v in _COORD_RE.findall(up))
    x = vals.get('X'); y = vals.get('Y'); z = vals.get('Z')
    a = vals.get('A'); b = vals.get('B'); c = vals.get('C')
    return cmd, x,y,z, a,b,c

def make_robot_rxyz_from_gabc(a,b,c):
    rx = normalize_angle_deg(a + GCODE_TO_ROBOT_OFFSET[0])
    ry = normalize_angle_deg(b + GCODE_TO_ROBOT_OFFSET[1])
    rz = normalize_angle_deg(c + GCODE_TO_ROBOT_OFFSET[2])
    return rx,ry,rz

def load_waypoints(filepath):
    print(f"📄 解析: {filepath}")
    last = {'x':0.0,'y':0.0,'z':20.0,'a':0.0,'b':0.0,'c':0.0}
    wps = []
    with open(filepath,'r',encoding='utf-8') as f:
        for raw in f:
            p = parse_line(raw)
            if not p: continue
            cmd,x,y,z,a,b,c = p
            cur = dict(last)
            if x is not None: cur['x'] = x
            if y is not None: cur['y'] = y
            if z is not None: cur['z'] = z
            if cmd == 'G0':
                cur['a']=0.0; cur['b']=0.0; cur['c']=0.0
            else:
                if a is not None: cur['a']=a
                if b is not None: cur['b']=b
                if c is not None: cur['c']=c
            rx,ry,rz = make_robot_rxyz_from_gabc(cur['a'],cur['b'],cur['c'])
            q = euler_xyz_deg_to_quat(rx,ry,rz)
            wps.append(Waypoint(cmd, cur['x'],cur['y'],cur['z'], cur['a'],cur['b'],cur['c'], q))
            last = cur
    # 保证四元数正向一致，避免 PI 跨越
    for i in range(1, len(wps)):
        if quat_dot(wps[i-1].q, wps[i].q) < 0.0:
            w = wps[i]
            wps[i] = Waypoint(w.cmd, w.x,w.y,w.z, w.a,w.b,w.c, (-w.q[0],-w.q[1],-w.q[2],-w.q[3]))
    return wps


# ----------------------------- 样条与采样 -----------------------------

def build_squad_controls(Q):
    n = len(Q)
    if n == 1:
        return [Q[0]]
    S = [Q[0]]
    for i in range(1, n-1):
        qi = Q[i]; qim1 = Q[i-1]; qip1 = Q[i+1]
        term1 = quat_log( quat_mul( quat_inv(qi), qip1 ) )
        term2 = quat_log( quat_mul( quat_inv(qi), qim1 ) )
        m = (0.0, -0.25*(term1[1]+term2[1]), -0.25*(term1[2]+term2[2]), -0.25*(term1[3]+term2[3]))
        si = quat_mul(qi, quat_exp(m))
        S.append( quat_normalize(si) )
    S.append(Q[-1])
    return S

def block_presample(P, Q, S, ease_ends=True):
    """
    对一个 G1 block 进行预采样：返回细分后的 pose 列表（x,y,z, quat）。
    - 位置：centripetal Catmull-Rom；
    - 姿态：SQUAD；
    - 参数：段内用 t∈[t_i, t_{i+1}]；若 ease_ends，则把首末两端的参数做轻微余弦缓动。
    """
    n = len(P)
    if n == 1:
        return [ (P[0][0],P[0][1],P[0][2], Q[0]) ]

    t = centripetal_params(P, CR_ALPHA)

    # 预采样步长 → 每段细分数
    pres = []
    for i in range(n-1):
        L = chord_len(P[i], P[i+1])
        ang = angle_between_quats_deg(Q[i], Q[i+1])
        n_pos = max(1, int(math.ceil(L / PRE_POS_STEP_MM)))
        n_ori = max(1, int(math.ceil(ang / PRE_ANG_STEP_DEG)))
        steps = max(n_pos, n_ori) * OVERSAMPLE_FACTOR
        pres.append(max(2, steps))

    poses = []
    # 先放第一个点
    poses.append( (P[0][0],P[0][1],P[0][2], Q[0]) )

    for i in range(n-1):
        # 备四点（端点复制）
        im1 = max(0, i-1)
        ip2 = min(n-1, i+2)
        Pm1, P0, P1, P2 = P[im1], P[i], P[i+1], P[ip2]
        t_m1, t0, t1, t2 = t[im1], t[i], t[i+1], t[ip2]
        steps = pres[i]

        for k in range(1, steps+1):
            u = k/steps
            # 端点缓动，仅在整个 block 的起/末段的一端生效
            if ease_ends and EASE_AT_BLOCK_ENDS:
                if i == 0 and u < EASE_RATIO:
                    u = 0.5 - 0.5*math.cos(math.pi * (u/EASE_RATIO)) * EASE_RATIO
                elif i == n-2 and u > 1.0 - EASE_RATIO:
                    w = (u - (1.0 - EASE_RATIO)) / EASE_RATIO
                    u = (1.0 - EASE_RATIO) + (0.5 - 0.5*math.cos(math.pi * w)) * EASE_RATIO

            # 位置
            t_cur = t0 + (t1 - t0) * u
            X = catmull_rom_segment(Pm1,P0,P1,P2, t_m1,t0,t1,t2, t_cur)
            # 姿态
            q = squad(Q[i], S[i], S[i+1], Q[i+1], u)
            poses.append( (X[0],X[1],X[2], q) )

    return poses

def resample_uniform_combined(poses_xyz_q):
    """
    把预采样的 (x,y,z,q) 等距化：用组合度量 ds = sqrt( dL^2 + (K*Δθ)^2 )。
    返回 (x,y,z,rx,ry,rz) 列表（角度为度）。
    """
    if not poses_xyz_q: return []
    # 目标间距（等效mm）
    ds_pos = POS_STEP_MAX_MM
    ds_ang = ANG_MM_PER_DEG * ANG_STEP_MAX_DEG
    ds_target = min(ds_pos, ds_ang)

    out = []
    x0,y0,z0,q0 = poses_xyz_q[0]
    rx0,ry0,rz0 = quat_to_euler_xyz_deg(q0)
    out.append( (x0,y0,z0, rx0,ry0,rz0) )
    s_acc = 0.0

    for i in range(1, len(poses_xyz_q)):
        x1,y1,z1,q1 = poses_xyz_q[i]
        # 局部弧长
        dl = chord_len((x0,y0,z0), (x1,y1,z1))
        dth = angle_between_quats_deg(q0, q1)
        ds = math.sqrt( dl*dl + (ANG_MM_PER_DEG * dth)**2 )
        if s_acc + ds < ds_target - 1e-9:
            s_acc += ds
            x0,y0,z0,q0 = x1,y1,z1,q1
            continue

        # 在 [prev,cur] 内找到等距点数
        remain = ds_target - s_acc
        seg_len = ds
        px,py,pz,pq = x0,y0,z0,q0
        cx,cy,cz,cq = x1,y1,z1,q1
        while remain <= seg_len + 1e-12:
            t = remain / seg_len if seg_len>1e-12 else 1.0
            xi = px + (cx-px)*t
            yi = py + (cy-py)*t
            zi = pz + (cz-pz)*t
            qi = quat_slerp(pq, cq, t)
            rxi,ryi,rzi = quat_to_euler_xyz_deg(qi)
            out.append( (xi,yi,zi, rxi,ryi,rzi) )
            seg_len -= remain
            remain = ds_target
            px,py,pz,pq = xi,yi,zi,qi

        s_acc = seg_len
        x0,y0,z0,q0 = x1,y1,z1,q1

    # 确保最后一个端点纳入
    x1,y1,z1,q1 = poses_xyz_q[-1]
    rxe,rye,rze = quat_to_euler_xyz_deg(q1)
    if chord_len((out[-1][0],out[-1][1],out[-1][2]), (x1,y1,z1)) > 1e-6 or angle_between_quats_deg(euler_xyz_deg_to_quat(out[-1][3],out[-1][4],out[-1][5]), q1) > 1e-3:
        out.append( (x1,y1,z1, rxe,rye,rze) )
    return out


# ---------------------------- 生产者：重采样 ----------------------------

def producer(filepath: str, q: Queue, stop):
    wps = load_waypoints(filepath)
    if not wps:
        print("⚠️ 没有可用的G0/G1指令"); q.put(None); return

    i = 0
    while i < len(wps):
        # 先处理连续的 G0（作为分隔 + 抬刀/空移）
        while i < len(wps) and wps[i].cmd == 'G0':
            rx,ry,rz = make_robot_rxyz_from_gabc(0.0,0.0,0.0)
            q.put( ('G0', (wps[i].x, wps[i].y, wps[i].z, rx,ry,rz)) )
            i += 1
        if i >= len(wps): break

        # 收集一整块连续 G1
        j = i
        while j < len(wps) and wps[j].cmd == 'G1':
            j += 1
        block = wps[i:j]

        if len(block) == 1:
            # 单点：直接下发
            rx,ry,rz = quat_to_euler_xyz_deg(block[0].q)
            q.put( ('G1', (block[0].x, block[0].y, block[0].z, rx,ry,rz)) )
            i = j
            continue

        P = [(w.x,w.y,w.z) for w in block]
        Q = [w.q for w in block]
        S = build_squad_controls(Q)

        # 预采样 + 等距重采样
        fine = block_presample(P, Q, S, ease_ends=True)
        uni  = resample_uniform_combined(fine)

        # 起点先入一次（建立状态）
        q.put( ('G1', uni[0]) )
        for pose in uni[1:]:
            q.put( ('G1', pose) )

        i = j

    q.put(None)
    print("✅ 重采样完成")


# -------------------------------- 主流程 --------------------------------

class RobotControllerWrapper(RobotController):
    pass

def main():
    import argparse
    global SMOOTHING_LEVEL, POS_STEP_MAX_MM, ANG_STEP_MAX_DEG, ANG_MM_PER_DEG
    global PRE_POS_STEP_MM, PRE_ANG_STEP_DEG, EASE_AT_BLOCK_ENDS
    pa = argparse.ArgumentParser("Catmull-Rom(XYZ) + SQUAD(姿态) 等距采样执行器")
    pa.add_argument("-f","--file", default=GCODE_FILE, help="G-code 文件路径")
    pa.add_argument("--pl", type=int, default=SMOOTHING_LEVEL, help="控制器平滑等级(0~8)")
    pa.add_argument("--ds", type=float, default=POS_STEP_MAX_MM, help="输出最大线性步长(mm)")
    pa.add_argument("--da", type=float, default=ANG_STEP_MAX_DEG, help="输出最大角步长(deg)")
    pa.add_argument("--ang-mm", type=float, default=ANG_MM_PER_DEG, help="角度等效长度(mm/deg)")
    pa.add_argument("--pre-ds", type=float, default=PRE_POS_STEP_MM, help="预采样线性步长(mm)")
    pa.add_argument("--pre-da", type=float, default=PRE_ANG_STEP_DEG, help="预采样角步长(deg)")
    pa.add_argument("--ease", action="store_true", default=EASE_AT_BLOCK_ENDS, help="在整块首尾做缓动")
    args = pa.parse_args()

    

    SMOOTHING_LEVEL = args.pl
    POS_STEP_MAX_MM = args.ds
    ANG_STEP_MAX_DEG = args.da
    ANG_MM_PER_DEG  = args.ang_mm
    PRE_POS_STEP_MM = args.pre_ds
    PRE_ANG_STEP_DEG= args.pre_da
    EASE_AT_BLOCK_ENDS = args.ease

    q = Queue(maxsize=10000)
    stop = threading.Event()

    rc = RobotControllerWrapper(ROBOT_IP, ROBOT_PORT)
    prod = None

    try:
        if not rc.connect() or not rc.initialize():
            return

        # 起始准备：G0 抬到安全位，朝下
        rc.set_speed(G0_VELOCITY_PERCENT)
        rx0,ry0,rz0 = make_robot_rxyz_from_gabc(0.0,0.0,0.0)
        rc.add_point(0.0,0.0,20.0, rx0,ry0,rz0, move_type='G0')
        rc.flush(1)

        # 启动生产者
        prod = threading.Thread(target=producer, args=(args.file, q, stop), daemon=True)
        prod.start()

        sent = 0
        batch = []
        cur_type = 'G0'
        while not stop.is_set():
            ctl_len = rc.get_queue_length()
            if ctl_len < 0:
                print("\n❌ 队列查询失败"); break

            if ctl_len < CONTROLLER_QUEUE_CAPACITY - BATCH_SEND_SIZE:
                try:
                    item = q.get(timeout=0.05)
                except Empty:
                    item = None

                if item is None:
                    if batch:
                        rc.flush(len(batch)); batch.clear()
                    if rc.get_queue_length() == 0:
                        print("\n🎉 执行完成"); break
                    time.sleep(0.05); continue

                move_type, pose = item

                if move_type != cur_type:
                    if batch:
                        rc.flush(len(batch)); batch.clear()
                    rc.set_speed(G0_VELOCITY_PERCENT if move_type=='G0' else G1_VELOCITY_PERCENT)
                    cur_type = move_type

                x,y,z,rx,ry,rz = pose
                rc.add_point(x,y,z, rx,ry,rz, move_type)
                batch.append(item); sent += 1

                if len(batch) >= BATCH_SEND_SIZE:
                    rc.flush(len(batch)); batch.clear()

            print(f"\r📊 已发{sent} | 本地{q.qsize():4d} | 控制器{ctl_len:3d} | 模式{cur_type} ", end="")
            time.sleep(0.01)

    except KeyboardInterrupt:
        print("\n🛑 中断")
    except Exception as e:
        print("\n❌ 异常：", e)
    finally:
        stop.set()
        if prod and prod.is_alive(): prod.join()
        try: rc.power_off()
        finally: rc.disconnect()


if __name__ == "__main__":
    main()
