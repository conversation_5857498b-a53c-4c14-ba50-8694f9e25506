---
alwaysApply: false
---
The other day, I was getting frustrated with <PERSON> giving me these bloated, over- engineered solutions with a bunch of "what- if" features I didn't need. Then I tried adding these three principles to my prompts, and it was like talking to a completely different <PERSON>. The code it wrote was literally half the size and just... solved the damn problem without all the extra BS. And all I had to do was ask it to follow these principles:
 KISS (Keep It Simple, Stupid) -Encourages <PERSON> to write straightforward, uncomplicated solutions -Avoids over-engineering and unnecessary complexity -Results in more readable and maintainable code

 YAGNI (You Aren't Gonna Need It) -Prevents <PERSON> from adding speculative features -Focuses on implementing only what's currently needed -Reduces code bloat and maintenance overhead

 SOLID Principles -Single Responsibility Principle -Open-Closed Principle -Liskov Substitution Principle -Interface Segregation Principle -Dependency Inversion Principle