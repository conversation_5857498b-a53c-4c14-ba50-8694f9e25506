# -*- coding: utf-8 -*-
"""
严格日志包装器 v5（只加强日志与故障可见性）：
- 抓 “故障发生时的 P 索引 + 最近3条运动指令 + 报警/伺服/队列 + 伺服历史”。
- 不改动你的运动/插补/限速逻辑（KISS / YAGNI）。
"""

import sys, time, re, importlib.util, argparse, collections, inspect
from pathlib import Path
from types import ModuleType
from typing import Optional

# —— 你的原始控制脚本路径（确认无误；r"" 防转义）——
ORIG_FILE = Path(r"C:\Users\<USER>\Desktop\INEXBOT_3DP2\test\kiss_yagni_solid_arm_runner.py")
DEFAULT_GCODE = "jiyi copy.Gcode"

# 故障预防配置 - 针对间歇性伺服故障的优化参数
FAULT_PREVENTION_CONFIG = {
    'reduced_batch_size': 10,        # 降低批次大小
    'conservative_speed': 20,        # 保守速度百分比
    'queue_high_water': 30,          # 降低队列高水位
    'queue_low_water': 10,           # 降低队列低水位
    'servo_check_interval': 0.02,    # 更频繁的伺服检查(30ms)
    'recovery_delay': 1.5,           # 恢复后等待时间
    'max_consecutive_faults': 3,     # 最大连续故障次数
}
NOMINAL_SERVO_STATES = {1, 3}  # 1=就绪, 3=运行 都是正常；2=报警, 0=停止 视为异常

# 运行期上下文：最近进度、最近指令、伺服历史
LAST_PROGRESS = {"idx": None, "total": None, "run": None, "ts": None}
RECENT_CMDS = collections.deque(maxlen=8)      # [(ts, func, args, kwargs)]
SERVO_HISTORY = collections.deque(maxlen=20)   # [(ts, state)]

# 故障预防状态
CONSECUTIVE_FAULTS = 0
LAST_RECOVERY_TIME = 0
PREVENTION_MODE_ACTIVE = False

def _activate_prevention_mode(robot):
    """激活智能故障预防模式"""
    global PREVENTION_MODE_ACTIVE, LAST_RECOVERY_TIME

    PREVENTION_MODE_ACTIVE = True
    LAST_RECOVERY_TIME = time.time()

    try:
        print("   🛑 执行预防性停止...")
        import nrc_interface as nrc
        nrc.queue_motion_stop(robot.socket_fd)
        time.sleep(0.3)

        print("   🧹 清除所有错误...")
        nrc.clear_error(robot.socket_fd)
        time.sleep(0.8)

        print("   ⚡ 重新初始化伺服...")
        nrc.set_servo_state(robot.socket_fd, 1)
        time.sleep(1.2)

        # 强制执行上电，确保进入运行状态
        print("   🔌 执行上电操作...")
        result = nrc.set_servo_poweron(robot.socket_fd)
        time.sleep(1.0)  # 等待上电完成

        # 检查上电后的状态
        final_state = robot.read_servo_state() if hasattr(robot, 'read_servo_state') else -1
        print(f"   📊 上电后状态: {final_state}")

        if result == 0 and final_state == 3:
            print("   🐌 切换到超保守模式...")
            # 设置极低速度
            if hasattr(robot, 'set_speed'):
                robot.set_speed(FAULT_PREVENTION_CONFIG['conservative_speed'])

            # 应用保守参数到原始模块
            _apply_conservative_parameters()

            print("   ✅ 预防模式激活成功")
        else:
            print(f"   ⚠️ 上电返回: {result}，但继续使用保守模式")
            # 即使上电失败，也应用保守参数
            _apply_conservative_parameters()
            if hasattr(robot, 'set_speed'):
                robot.set_speed(FAULT_PREVENTION_CONFIG['conservative_speed'])

    except Exception as e:
        print(f"   ❌ 预防模式激活异常: {e}")

def _apply_conservative_parameters():
    """应用保守参数到原始模块"""
    try:
        # 动态修改原始模块的参数
        orig_module = sys.modules.get('kiss_yagni_solid_arm_runner')
        if orig_module:
            # 降低批次大小
            if hasattr(orig_module, 'BATCH_SEND'):
                orig_module.BATCH_SEND = FAULT_PREVENTION_CONFIG['reduced_batch_size']
                print(f"     📦 批次大小降至: {orig_module.BATCH_SEND}")

            # 降低队列水位
            if hasattr(orig_module, 'HIGH_WATER'):
                orig_module.HIGH_WATER = FAULT_PREVENTION_CONFIG['queue_high_water']
                print(f"     🌊 高水位降至: {orig_module.HIGH_WATER}")

            if hasattr(orig_module, 'LOW_WATER'):
                orig_module.LOW_WATER = FAULT_PREVENTION_CONFIG['queue_low_water']
                print(f"     🌊 低水位降至: {orig_module.LOW_WATER}")

    except Exception as e:
        print(f"     ⚠️ 参数应用异常: {e}")

# =============== 故障门控（抑制噪音 + 强化可见） ===============
FAULT_ACTIVE = False
FAULT_CTX = {
    "where": None,
    "alarm": None,
    "servo": None,
    "queue": None,
    "reason": None,
    "last_cmd": None,
    "ts": None,
    "p_idx": None,
    "p_total": None,
}

def _probe_alarm(robot):
    """尽可能探测报警：遍历零参数的 *alarm*/*fault*/*error* 方法。"""
    names = [n for n in dir(robot) if any(k in n.lower() for k in ("alarm","fault","error"))]
    for n in sorted(set(names)):
        try:
            attr = getattr(robot, n)
            if callable(attr):
                sig = inspect.signature(attr)
                if all(p.default is not inspect._empty or p.kind in (p.VAR_KEYWORD, p.VAR_POSITIONAL)
                       for p in sig.parameters.values()) or len(sig.parameters)==0:
                    try:
                        res = attr()
                        if res is not None:
                            return f"{n}", res
                    except Exception:
                        pass
        except Exception:
            pass
    return None, None

def _fault_on(robot, where: str, reason: str = "", ex: Exception = None):
    """进入故障态：记录上下文并打印摘要行（仅一次）。"""
    global FAULT_ACTIVE, FAULT_CTX
    if FAULT_ACTIVE:
        return
    FAULT_ACTIVE = True

    # 读取报警/伺服/队列
    aname, aval = (None, None)
    try:
        aname, aval = _probe_alarm(robot)
    except Exception:
        pass
    if not aname:
        try:
            aname, aval = robot.try_fetch_alarm()
        except Exception:
            pass
    try:
        servo = robot.read_servo_state()
    except Exception:
        servo = None
    try:
        qlen  = robot.get_queue_length()
    except Exception:
        qlen = None

    # 增强诊断：读取当前位置和关节状态
    current_pos = None
    joint_angles = None
    try:
        # 尝试获取当前笛卡尔坐标和关节角度
        if hasattr(robot, 'socket_fd'):
            try:
                # 使用robot对象的方法获取位置信息
                pos_result = robot.try_get_current_position(1)  # 1=笛卡尔坐标
                current_pos = pos_result
            except:
                pass
            try:
                joint_result = robot.try_get_current_position(0)  # 0=关节坐标
                joint_angles = joint_result
            except:
                pass
    except Exception:
        pass

    last_cmd = None
    if RECENT_CMDS:
        t, fn, args, kwargs = RECENT_CMDS[-1]
        last_cmd = f"{fn}{args}{kwargs}"

    FAULT_CTX = {
        "where": where,
        "alarm": (f"{aname}={aval}" if aname else "未提供"),
        "servo": servo,
        "queue": qlen,
        "reason": (str(reason) if reason else (str(ex) if ex else "")),
        "last_cmd": last_cmd,
        "ts": time.strftime("%H:%M:%S"),
        "p_idx": LAST_PROGRESS["idx"],
        "p_total": LAST_PROGRESS["total"],
        "current_pos": current_pos,
        "joint_angles": joint_angles,
    }

    # 主摘要
    print(
        f"⛔ 故障：来源={where}  P={FAULT_CTX['p_idx']}/{FAULT_CTX['p_total']}  "
        f"原因={FAULT_CTX['reason']}  报警={FAULT_CTX['alarm']}  "
        f"伺服={FAULT_CTX['servo']}  队列={FAULT_CTX['queue']}"
    )

    # 最近 3 条运动指令
    if RECENT_CMDS:
        k = min(3, len(RECENT_CMDS))
        for i in range(1, k+1):
            t, fn, args, kwargs = RECENT_CMDS[-i]
            print(f"  • 最近指令[-{i}] @{time.strftime('%H:%M:%S', time.localtime(t))}: {fn}{args}{kwargs}")

    # 伺服历史
    if SERVO_HISTORY:
        trail = ", ".join(f"{st}@{time.strftime('%H:%M:%S', time.localtime(ts))}" for ts, st in list(SERVO_HISTORY)[-6:])
        print(f"  • 伺服历史(最近): {trail}")

    # 增强诊断信息
    if current_pos:
        print(f"  • 当前笛卡尔位置: {current_pos}")
    if joint_angles:
        print(f"  • 当前关节角度: {joint_angles}")
    if last_cmd:
        print(f"  • 最后执行指令: {last_cmd}")

def _fault_off(robot=None):
    """退出故障态：仅当伺服恢复到运行状态时才真正退出。"""
    global FAULT_ACTIVE, FAULT_CTX
    if robot is not None:
        try:
            ss = robot.read_servo_state()
            # 只有状态3（运行）才算真正恢复，状态1（就绪）还不够
            if ss != 3:
                print(f"   ⚠️ 伺服状态{ss}，尚未完全恢复到运行状态")
                return
        except Exception:
            return
    if FAULT_ACTIVE:
        print("✅ 故障已恢复到运行状态，继续执行。")
    FAULT_ACTIVE = False
    for k in FAULT_CTX:
        FAULT_CTX[k] = None

# =============== 加载原脚本 ===============
def _load_orig(orig_path: Path) -> ModuleType:
    if not orig_path.exists():
        raise FileNotFoundError(f"原始控制脚本不存在：{orig_path}")
    spec = importlib.util.spec_from_file_location("orig_runner", str(orig_path))
    if spec is None or spec.loader is None:
        raise RuntimeError(f"无法加载原始脚本：{orig_path}")
    mod = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mod)
    return mod

# =============== 打补丁（最小侵入） ===============
def _install_patches(orig: ModuleType):
    global FAULT_ACTIVE

    # 1) 覆写 dlog：故障态下静默
    _old_dlog = getattr(orig, "dlog", lambda msg: None)
    def dlog_patched(msg: str):
        if not FAULT_ACTIVE:
            _old_dlog(msg)
    setattr(orig, "dlog", dlog_patched)

    # 2) 包裹 RobotController 关键方法
    RC = orig.RobotController

    # 通用装饰器：记录最近指令
    def _record_call(fn_name, fn):
        def wrap(self, *args, **kwargs):
            try:
                RECENT_CMDS.append((time.time(), fn_name, args, kwargs))
                self._last_motion_cmd = f"{fn_name}{args}{kwargs}"
            except Exception:
                pass
            return fn(self, *args, **kwargs)
        return wrap

    # 2.1 flush - 增强版，添加队列管理和预防措施
    if hasattr(RC, "flush"):
        _old_flush = RC.flush
        def _flush(self, n: int):
            global PREVENTION_MODE_ACTIVE

            # 预防模式下的额外检查
            if PREVENTION_MODE_ACTIVE:
                print(f"🛡️ 预防模式下flush {n}个点...")

                # 检查队列状态
                try:
                    queue_len = self.get_queue_length()
                    if queue_len > FAULT_PREVENTION_CONFIG['queue_high_water']:
                        print(f"   ⚠️ 队列过满({queue_len})，强制等待...")
                        self.wait_inflight_below(
                            high=FAULT_PREVENTION_CONFIG['queue_high_water'],
                            low=FAULT_PREVENTION_CONFIG['queue_low_water']
                        )
                except Exception:
                    pass

                # 预防模式下减小批次
                if n > FAULT_PREVENTION_CONFIG['reduced_batch_size']:
                    print(f"   📦 批次过大({n})，分批发送...")
                    # 分批发送
                    batch_size = FAULT_PREVENTION_CONFIG['reduced_batch_size']
                    success_count = 0
                    for i in range(0, n, batch_size):
                        current_batch = min(batch_size, n - i)
                        ok = _old_flush(self, current_batch)
                        if not ok:
                            _fault_on(self, "flush", f"分批flush失败 (批次{i//batch_size + 1})")
                            return False
                        success_count += current_batch
                        time.sleep(0.05)  # 批次间短暂等待
                    print(f"   ✅ 分批发送完成: {success_count}/{n}")
                    return True

            # 执行原始flush
            ok = _old_flush(self, n)

            if ok:
                try:
                    ss = self.read_servo_state()
                except Exception:
                    ss = None
                if ss in NOMINAL_SERVO_STATES:
                    _fault_off(self)
                else:
                    if not FAULT_ACTIVE:
                        _fault_on(self, "flush", f"flush ok 但伺服异常={ss}")
            else:
                _fault_on(self, "flush", "queue_motion_send_to_controller 返回非 0")
            return ok
        RC.flush = _flush

    # 2.2 set_speed
    if hasattr(RC, "set_speed"):
        _old_set_speed = RC.set_speed
        def _set_speed(self, *args, **kwargs):
            ok = _old_set_speed(self, *args, **kwargs)
            if not ok:
                _fault_on(self, "set_speed", "设置速度失败")
            return ok
        RC.set_speed = _set_speed

    # 2.3 push_back 及所有 push/enqueue 家族方法：统一记录
    for name in dir(RC):
        if not any(name.startswith(p) for p in ("push", "enqueue")) and ("push_back" not in name):
            continue
        if not callable(getattr(RC, name)):
            continue
        if name in ("flush", "set_speed", "read_servo_state"):
            continue
        fn_old = getattr(RC, name)
        setattr(RC, name, _record_call(name, fn_old))

    # 2.4 read_servo_state：记录伺服历史；异常即刻进故障；添加智能预防
    if hasattr(RC, "read_servo_state"):
        _old_read_servo_state = RC.read_servo_state
        def _read_servo_state(self, *args, **kwargs):
            global CONSECUTIVE_FAULTS, LAST_RECOVERY_TIME, PREVENTION_MODE_ACTIVE

            # 限制检查频率，避免过度查询控制器
            current_time = time.time()
            if hasattr(self, '_last_servo_check'):
                time_since_last = current_time - self._last_servo_check
                if time_since_last < FAULT_PREVENTION_CONFIG['servo_check_interval']:
                    time.sleep(0.01)  # 短暂等待
            self._last_servo_check = current_time

            ss = _old_read_servo_state(self, *args, **kwargs)
            try:
                SERVO_HISTORY.append((time.time(), ss))
            except Exception:
                pass

            if ss not in NOMINAL_SERVO_STATES:
                # 只有状态2（报警）和0（停止）才是真正的异常
                if ss == 2 or ss == 0:
                    CONSECUTIVE_FAULTS += 1
                    print(f"⚠️ 伺服真正异常检测 #{CONSECUTIVE_FAULTS}: 状态={ss}")

                    # 智能预防措施
                    if CONSECUTIVE_FAULTS >= FAULT_PREVENTION_CONFIG['max_consecutive_faults']:
                        if not PREVENTION_MODE_ACTIVE:
                            print("🛡️ 启动智能故障预防模式...")
                            _activate_prevention_mode(self)

                    _fault_on(self, "servo_state", f"伺服异常={ss}")
                else:
                    # 其他状态（如初始化状态）不触发故障计数
                    print(f"ℹ️ 伺服状态变化: {ss} (非异常状态)")
            else:
                # 状态正常，重置计数
                if CONSECUTIVE_FAULTS > 0:
                    print(f"✅ 伺服状态恢复正常 (之前连续异常{CONSECUTIVE_FAULTS}次)")
                    CONSECUTIVE_FAULTS = 0
                    if PREVENTION_MODE_ACTIVE:
                        print("🛡️ 退出故障预防模式")
                        PREVENTION_MODE_ACTIVE = False

            return ss
        RC.read_servo_state = _read_servo_state

    # 3) 包裹 stdout：先解析“进度行”，再按需抑制噪音
    real_write = sys.stdout.write
    progress_re = re.compile(r"^▶\s*进度\s+(\d+)\s*/\s*(\d+).*?当前run=([0-9]+)%")
    noisy_prefix = ("▶ 进度", "批量发送成功", "尾批发送成功", "[")
    def write_patched(s: str):
        # 捕获进度（即便待会儿抑制，也先解析出来）
        line = s.strip()
        m = progress_re.match(line)
        if m:
            try:
                LAST_PROGRESS.update({
                    "idx": int(m.group(1)),
                    "total": int(m.group(2)),
                    "run": int(m.group(3)),
                    "ts": time.time(),
                })
            except Exception:
                pass

        if FAULT_ACTIVE:
            low = line.lower()
            if (
                any(line.startswith(p) for p in noisy_prefix) or
                ("sending" in low) or ("发送" in line) or
                ("路径已全部下发" in line)
            ):
                return 0  # 丢弃噪音
        return real_write(s)
    sys.stdout.write = write_patched  # type: ignore

# =============== 对外入口 ===============
def run(gcode_path: Optional[str] = None, orig_path: Optional[Path] = None):
    orig_path = (orig_path or ORIG_FILE)
    orig = _load_orig(orig_path)
    _install_patches(orig)
    g = gcode_path or DEFAULT_GCODE
    return orig.run(g)

# =============== CLI ===============
def main():
    parser = argparse.ArgumentParser(description="严格日志包装器（故障可见、无冗余日志）")
    parser.add_argument("gcode", nargs="?", default=None, help="GCode 文件路径（可选）")
    args = parser.parse_args()
    run(args.gcode)

if __name__ == "__main__":
    main()
